<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
		<string>cydia</string>
		<string>tez</string>
		<string>gpay</string>
		<string>phonepe</string>
		<string>paytm</string>
		<string>paytmmp</string>
		<string>bhim</string>
		<string>upi</string>
		<string>lotza</string>
		<string>mobikwik</string>
		<string>whatsapp</string>
		<string>credpay</string>
		<string>devtools</string>
		<string>myairtel</string>
		<string>slice-upi</string>
		<string>ppe</string>
		<string>cugext</string>
		<string>amazonpay</string>
		<string>kiwi</string>
		<string>navipay</string>
		<string>tatadigital</string>
		<string>popclubapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Camera usage to change profile photo</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location usage to find services and caretakers around you</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Gallery usage to change profile photo</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
</dict>
</plist>
