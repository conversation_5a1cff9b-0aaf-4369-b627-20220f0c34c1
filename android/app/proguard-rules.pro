# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.bumptech.glide.Glide
-dontwarn com.bumptech.glide.RequestBuilder
-dontwarn com.bumptech.glide.RequestManager
-dontwarn com.bumptech.glide.request.BaseRequestOptions
-dontwarn com.bumptech.glide.request.RequestOptions
-dontwarn com.bumptech.glide.request.target.ViewTarget
-dontwarn com.google.android.exoplayer2.ExoPlayer$Builder
-dontwarn com.google.android.exoplayer2.ExoPlayer
-dontwarn com.google.android.exoplayer2.MediaItem
-dontwarn com.google.android.exoplayer2.Player$Listener
-dontwarn com.google.android.exoplayer2.Player
-dontwarn com.google.android.exoplayer2.source.MediaSource
-dontwarn com.google.android.exoplayer2.source.hls.HlsMediaSource$Factory
-dontwarn com.google.android.exoplayer2.source.hls.HlsMediaSource
-dontwarn com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection$Factory
-dontwarn com.google.android.exoplayer2.trackselection.DefaultTrackSelector
-dontwarn com.google.android.exoplayer2.trackselection.ExoTrackSelection$Factory
-dontwarn com.google.android.exoplayer2.trackselection.TrackSelector
-dontwarn com.google.android.exoplayer2.ui.StyledPlayerView
-dontwarn com.google.android.exoplayer2.upstream.BandwidthMeter
-dontwarn com.google.android.exoplayer2.upstream.DataSource$Factory
-dontwarn com.google.android.exoplayer2.upstream.DefaultBandwidthMeter$Builder
-dontwarn com.google.android.exoplayer2.upstream.DefaultBandwidthMeter
-dontwarn com.google.android.exoplayer2.upstream.DefaultDataSource$Factory
-dontwarn com.google.android.exoplayer2.upstream.DefaultHttpDataSource$Factory
-dontwarn com.google.android.exoplayer2.upstream.TransferListener
-dontwarn com.google.android.exoplayer2.util.Util

-dontwarn com.google.android.gms.auth.api.credentials.*