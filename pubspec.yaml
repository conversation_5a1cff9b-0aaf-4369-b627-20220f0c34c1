name: homeservice_app
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.4+6

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  lottie: ^3.2.0
  package_info_plus: ^8.3.0
  permission_handler: ^12.0.0+1
  pin_code_fields: ^8.0.1
  shimmer: ^3.0.0
  equatable: ^2.0.7
  flutter_bloc: ^9.1.0
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.17
  get_it: ^8.0.3
  image_picker: ^1.1.2
  internet_connection_checker_plus: ^2.7.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  country_code_picker: ^3.2.0
  dropdown_button2: ^2.3.9
  curl_logger_dio_interceptor: ^1.0.0
  vibration: ^3.1.3
  device_info_plus: ^11.2.0
  google_maps_flutter: ^2.10.0
  form_builder_extra_fields: ^12.1.0
  flutter_typeahead: ^5.2.0
  google_maps_webapi: ^0.0.24
  geolocator: ^14.0.2
  geocoding: ^4.0.0
  http: ^1.2.2
  carousel_slider: ^5.0.0
  url_launcher: ^6.3.1
  video_player: ^2.9.3
  chewie: ^1.10.0
  remove_emoji_input_formatter: ^0.0.1+1
  # sms_autofill:
  #   git:
  #     url: https://github.com/Pulkit077/sms_autofill
  #     ref: master
  flutter_html: ^3.0.0
  geolocator_android: ^5.0.2
  flutter_cashfree_pg_sdk: ^2.2.6+44
  intl: ^0.20.2
  collection: ^1.18.0
  flutter_native_splash: ^2.4.4
  webview_flutter: ^4.10.0
  expansion_tile_group: ^2.2.0
  firebase_core: ^4.0.0
  firebase_messaging: ^16.0.0
  firebase_crashlytics: ^5.0.0
  uuid: ^4.5.1
  pdf: ^3.11.3
  path_provider: ^2.1.5
  printing: ^5.14.2
  number_to_words_english: ^3.0.0
  open_file: ^3.5.10

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.4.13
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
    - assets/data/
    - assets/raw/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: NunitoSans-Regular
      fonts:
        - asset: assets/fonts/NunitoSans-Regular.ttf
          weight: 400

    - family: NunitoSans-Medium
      fonts:
        - asset: assets/fonts/NunitoSans-Medium.ttf
          weight: 500

    - family: NunitoSans-SemiBold
      fonts:
        - asset: assets/fonts/NunitoSans-SemiBold.ttf
          weight: 600

    - family: NunitoSans-Bold
      fonts:
        - asset: assets/fonts/NunitoSans-Bold.ttf
          weight: 700

    - family: NunitoSans-ExtraBold
      fonts:
        - asset: assets/fonts/NunitoSans-ExtraBold.ttf
          weight: 800

    - family: NunitoSans-Black
      fonts:
        - asset: assets/fonts/NunitoSans-Black.ttf
          weight: 900

    - family: PlaywriteIE
      fonts:
        - asset: assets/fonts/PlaywriteIE.ttf

    - family: Epilogue-ExtraBold
      fonts:
        - asset: assets/fonts/Epilogue-ExtraBold.ttf
          weight: 800

    - family: NunitoSans-Medium-Italic
      fonts:
        - asset: assets/fonts/NunitoSans-Medium-Italic.ttf
          weight: 500

    - family: NunitoSans-ExtraBold-Italic
      fonts:
        - asset: assets/fonts/NunitoSans-ExtraBold-Italic.ttf
          weight: 800

  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_native_splash:
  color: "#FFFFFF" # White background
  android: true
  ios: false
  web: false

  android_12:
    color: "#FFFFFF"
