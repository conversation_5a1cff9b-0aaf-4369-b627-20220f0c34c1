import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/string_constants/app_strings.dart';
import 'package:homeservice_app/widgets/animated_rating_star.dart';
import 'package:homeservice_app/widgets/buttons/primary_button.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/custom_multiselect_dropdown_dialog.dart';
import '../../../../widgets/textfields/app_textfield.dart';
import '../features/common/feedback/data/feedback_reason_model.dart';
import '../utils/string_constants/app_data.dart';
import '../utils/string_constants/app_images.dart';
import 'custom_cache_image.dart';

class CommonFeedbackWidget extends StatefulWidget {
  final double? rating;
  final String? caregiverName;
  final String? caregiverImage;
  final String? caregiverServiceType;
  final void Function(
      String? selectedReasons, double ratingVal, String? otherReason)? onSubmit;
  const CommonFeedbackWidget(
      {super.key,
      this.onSubmit,
      this.rating,
      this.caregiverName,
      this.caregiverImage,
      this.caregiverServiceType});

  @override
  State<CommonFeedbackWidget> createState() => _CommonFeedbackWidgetState();
}

class _CommonFeedbackWidgetState extends State<CommonFeedbackWidget> {
  double ratingVal = 0;
  List<String>? selectedReasons;
  List<FeedbackReason> reasons = [];
  bool isOtherReason = false;
  TextEditingController otherReasonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    loadFeedbackReasons().then((value) {
      setState(() {
        reasons = value;
      });
    });
  }

  Future<List<FeedbackReason>> loadFeedbackReasons() async {
    final String response = await rootBundle.loadString(AppData.feedbackData);
    final List<dynamic> data = json.decode(response);
    return data.map((json) => FeedbackReason.fromJson(json)).toList();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ClipOval(
              child: CustomCacheImage(
                imageUrl: widget.caregiverImage ?? '',
                errorWidget: SvgPicture.asset(
                  AppImages.caregiverUserIcon,
                  width: 32.h,
                  height: 32.h,
                  fit: BoxFit.cover,
                ),
                width: 32.h,
                height: 32.h,
                fit: BoxFit.cover,
              ),
            ),
            12.pw,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text14Bold(widget.caregiverName ?? '', fontSize: 16.sp),
                  Text14SemiBold(
                      widget.caregiverServiceType != null
                          ? "${widget.caregiverServiceType!} ${AppStrings.expert}"
                          : '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    color: colorScheme.lightGrey6B6B6B),
                ],
              ),
            )
          ],
        ),
        8.ph,
        if (widget.rating == null || widget.rating == 0) ...[
          Text14SemiBold(AppStrings.pleaseRateCaregiverExperience),
          24.ph,
        ],
        AnimatedRatingStar(
          initialRating: widget.rating ?? 0,
          iconSize: 24,
          allowHalfRating: false,
          gap: 4,
          ignoreGestures: widget.rating != null && widget.rating! > 0,
          onRatingUpdate: (value) {
            setState(() {
              ratingVal = value;
              if (ratingVal >= 4) {
                selectedReasons = null;
                isOtherReason = false;
              }
            });
          },
        ),
        AnimatedSize(
          alignment: Alignment.topCenter,
          curve: Curves.linear,
          duration: const Duration(milliseconds: 200),
          child: ratingVal < 4 && ratingVal != 0
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    24.ph,
                    Text14Medium(
                        AppStrings.weAreSorryToHearYouThatPleaseShareDetails),
                    8.ph,
                    CustomDialogMultiSelectWidget<String>(
                      maxHeight: 0.8.sh,
                      displayText: AppStrings.selectCaregiverIssue,
                      selectedValues: selectedReasons,
                      hintText: isOtherReason
                          ? AppStrings.otherReason
                          : selectedReasons != null &&
                                  selectedReasons!.isNotEmpty
                              ? selectedReasons!.join(', ')
                              : AppStrings.selectCaregiverIssue,
                      hintTextColor:
                          isOtherReason ? colorScheme.blue150045 : null,
                      items: reasons.map((e) {
                        return DropdownMenuItem<String>(
                          value: e.reason,
                          child: Text(e.reason),
                        );
                      }).toList(),
                      onChanged: (selected) {
                        setState(() {
                          isOtherReason = false;
                          selectedReasons = selected;
                        });
                      },
                      extraWidget: Column(
                        children: [
                          Row(
                            children: [
                              Text12Medium(AppStrings.or),
                              4.pw,
                              Expanded(
                                child: Divider(
                                  color: colorScheme.lightGreyDEDEDE,
                                  height: 1.h,
                                ),
                              ),
                            ],
                          ),
                          12.ph,
                          CustomOutlinedButton(
                            height: 48.h,
                            width: double.maxFinite,
                            buttonText: AppStrings.specifyOtherReason,
                            onPressed: () {
                              setState(() {
                                isOtherReason = true;
                                selectedReasons = null;
                              });
                              Navigator.pop(context);
                            },
                          )
                        ],
                      ),
                    ),
                    16.ph,
                    if (isOtherReason)
                      AppTextFormField(
                        hintText: AppStrings.pleaseShareDetails,
                        maxLines: 4,
                        controller: otherReasonController,
                      ),
                  ],
                )
              : SizedBox(),
        ),
        if (widget.rating == null || widget.rating == 0) ...[
          24.ph,
        PrimaryButton(
          width: 160.w,
          onPressed: (ratingVal == 0) ||
                  (ratingVal < 4 &&
                      (selectedReasons == null || selectedReasons!.isEmpty) &&
                      !isOtherReason)
              ? null
              : () {
                  widget.onSubmit?.call(
                      selectedReasons != null && selectedReasons!.isNotEmpty
                          ? selectedReasons?.join(', ')
                          : isOtherReason
                              ? AppStrings.otherReason
                              : null,
                      ratingVal,
                      isOtherReason &&
                              otherReasonController.text.trim().isNotEmpty
                          ? otherReasonController.text.trim()
                          : null);
                },
          buttonText: AppStrings.submit,
        ),
        ]
      ],
    );
  }
}
