import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';
import '../utils/string_constants/app_images.dart';
import 'texts/app_text.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final EdgeInsetsGeometry? padding;
  final bool showBackButton;
  const CustomAppbar(
      {super.key,
      required this.title,
      this.padding,
      this.showBackButton = true});

  @override
  Size get preferredSize => Size.fromHeight(52.h);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: padding ?? EdgeInsets.symmetric(horizontal: 18.w, vertical: 8.h),
        child: Row(
          children: [
            if (showBackButton)
              AppGestureDetector(
                onTap: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else {
                    SystemNavigator.pop();
                  }
                },
                child: SvgPicture.asset(AppImages.leftArrowIc),
              ),
            8.pw,
            Expanded(
              child: Text20Bold(
                title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
