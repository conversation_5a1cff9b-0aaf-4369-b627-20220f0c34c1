import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/theme/app_theme.dart';
import '../../utils/extensions/empty_space_extn.dart';

class DropDownShimmer extends StatelessWidget {
  const DropDownShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Shimmer.fromColors(
      baseColor: colorScheme.shimmerBaseColor,
      highlightColor: colorScheme.shimmerHighlightColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shimmer for title
          Container(
            width: 140.w,
            height: 14.h,
            color: colorScheme.white,
          ),
          11.ph,
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            width: double.infinity,
            height: 40.h,
            decoration: BoxDecoration(
              color: colorScheme.white,
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          20.ph,
        ],
      ),
    );
  }
}
