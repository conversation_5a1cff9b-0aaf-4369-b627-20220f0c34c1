import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';

class AnimatedRatingStar extends StatefulWidget {
  final double initialRating;
  final double minRating;
  final double maxRating;
  final ValueChanged<double>? onRatingUpdate;
  final Color? filledColor;
  final Color? unfilledColor;
  final double iconSize;
  final Duration animationDuration;
  final bool allowHalfRating;
  final bool ignoreGestures;
  final double gap;

  const AnimatedRatingStar({
    super.key,
    this.initialRating = 0.0,
    this.minRating = 0.0,
    this.maxRating = 5.0,
    this.onRatingUpdate,
    this.filledColor,
    this.unfilledColor,
    this.iconSize = 40.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.allowHalfRating = true,
    this.ignoreGestures = false,
    this.gap = 8.0,
  })  : assert(minRating <= maxRating),
        assert(initialRating >= minRating && initialRating <= maxRating);

  @override
  State<AnimatedRatingStar> createState() => _AnimatedRatingStarState();
}

class _AnimatedRatingStarState extends State<AnimatedRatingStar>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _ratingAnimation;
  double _currentRating = 0.0;
  final Map<int, AnimationController> _bounceControllers = {};
  final Map<int, double> _previousRatings = {};

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _ratingAnimation =
        Tween<double>(begin: _currentRating, end: _currentRating).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.addListener(() {
      setState(() {
        _currentRating = _ratingAnimation.value;
        _checkAndTriggerBounce();
      });
    });

    // Initialize previous ratings
    for (int i = 0; i < widget.maxRating.toInt(); i++) {
      _previousRatings[i] = widget.initialRating;
    }
  }

  @override
  void didUpdateWidget(covariant AnimatedRatingStar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialRating != widget.initialRating) {
      _animateTo(widget.initialRating);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _bounceControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  void _animateTo(double newRating) {

    _ratingAnimation =
        Tween<double>(begin: _currentRating, end: newRating).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward(from: 0.0).then((_) {
      // After the animation completes, update _previousRatings for all stars.
      for (int i = 0; i < widget.maxRating.toInt(); i++) {
        _previousRatings[i] = newRating;
      }
    });
  }

  void _checkAndTriggerBounce() {
    for (int i = 0; i < widget.maxRating.toInt(); i++) {
      final starRating = i + 1;
      final previousRating = _previousRatings[i] ?? 0.0;
      final isFilledNow = _currentRating >= starRating;
      final wasFilledBefore = previousRating >= starRating;

      if (isFilledNow && !wasFilledBefore) {
        _triggerBounceForStar(i);
      }
    }

    // Move this outside the loop to update all at once after checking.
    for (int i = 0; i < widget.maxRating.toInt(); i++) {
      _previousRatings[i] = _currentRating;
    }
  }

  void _triggerBounceForStar(int index) {
    if (!_bounceControllers.containsKey(index)) {
      _bounceControllers[index] = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 200),
      );
    }

    final controller = _bounceControllers[index]!;

    // Start the bounce animation immediately without depending on previous stars.
    controller.forward(from: 0.0).then((_) => controller.reset());
  }

  void _handleTap(double position) {
    if (widget.ignoreGestures) return;
    int starIndex = (position / (widget.iconSize + widget.gap)).floor();
    double newRating = starIndex + 1.0;
    if (widget.allowHalfRating) {
      double fraction =
          (position % (widget.iconSize + widget.gap)) / widget.iconSize;
      if (fraction < 0.5) {
        newRating -= 0.5;
      }
    }
    newRating = newRating.clamp(widget.minRating, widget.maxRating);
    if (newRating != _currentRating) {
      _animateTo(newRating);
      widget.onRatingUpdate?.call(newRating);
    }
  }

  Widget _buildStar(int index) {
    final double starRating = index + 1;
    final bool isFilled = _currentRating >= starRating;
    final bool isHalfFilled = widget.allowHalfRating &&
        (_currentRating - starRating + 1) >= 0.5 &&
        !isFilled;

    Widget star = SizedBox(
      width: widget.iconSize,
      height: widget.iconSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SvgPicture.asset(
            AppImages.starEmpty,
            width: widget.iconSize,
            height: widget.iconSize,
          ),
          if (isFilled || isHalfFilled)
            ClipRect(
              clipper: _StarClipper(percentage: isFilled ? 1.0 : 0.5),
              child: SvgPicture.asset(
                AppImages.starFilled,
                width: widget.iconSize,
                height: widget.iconSize,
              ),
            ),
        ],
      ),
    );

    if (isFilled && _bounceControllers.containsKey(index)) {
      final bounceController = _bounceControllers[index]!;
      return AnimatedBuilder(
        animation: bounceController,
        builder: (context, child) {
          final scale = 1.0 + (0.2 * bounceController.value); // 1.0 to 1.2
          return Transform.scale(
            scale: scale,
            child: child,
          );
        },
        child: star,
      );
    }

    return star;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragUpdate: widget.ignoreGestures
          ? null
          : (details) {
              RenderBox box = context.findRenderObject() as RenderBox;
              final localPosition = box.globalToLocal(details.globalPosition);
              _handleTap(localPosition.dx);
            },
      onTapUp: widget.ignoreGestures
          ? null
          : (details) {
              RenderBox box = context.findRenderObject() as RenderBox;
              final localPosition = box.globalToLocal(details.globalPosition);
              _handleTap(localPosition.dx);
            },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          widget.maxRating.toInt() * 2 - 1,
          (index) => index.isEven ? _buildStar(index ~/ 2) : widget.gap.pw,
        ),
      ),
    );
  }
}

class _StarClipper extends CustomClipper<Rect> {
  final double percentage;

  _StarClipper({required this.percentage});

  @override
  Rect getClip(Size size) {
    return Rect.fromLTRB(0, 0, size.width * percentage, size.height);
  }

  @override
  bool shouldReclip(_StarClipper oldClipper) {
    return oldClipper.percentage != percentage;
  }
}
