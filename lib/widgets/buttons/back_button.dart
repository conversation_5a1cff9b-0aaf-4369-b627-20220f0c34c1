import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';

class CustomBackButton extends StatelessWidget {
  final Function()? onTap;
  final IconData? icon;
  const CustomBackButton({super.key, this.onTap, this.icon});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [SvgPicture.asset(AppImages.leftArrowIc), 8.pw, ],
    );
  }
}
