import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/app_theme.dart';
import '../texts/app_text.dart';

class PrimaryButton extends StatelessWidget {
  final String? buttonText;
  final Widget? child;
  final double? height;
  final Color? backgroundColor;
  final Color? disabledBackgroundColor;
  final Color? textColor;
  final void Function()? onPressed;
  final OutlinedBorder? shape;
  final double? width;
  final EdgeInsets? padding;

  const PrimaryButton({
    super.key,
    this.onPressed,
    this.buttonText,
    this.child,
    this.backgroundColor,
    this.textColor,
    this.shape,
    this.width,
    this.height,
    this.disabledBackgroundColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: padding ?? EdgeInsets.zero,
        disabledBackgroundColor: disabledBackgroundColor ?? colorScheme.lightGreyD5D5D5,
        shape: shape ?? RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
        fixedSize: Size(width ?? double.maxFinite, height ?? 48.h),
        backgroundColor: backgroundColor ?? colorScheme.primary,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        elevation: 0,
      ),
      onPressed: onPressed,
      child: child ??
          Text14Bold(
            buttonText ?? "",
            color: textColor ?? colorScheme.white,
            fontSize: 16.sp,
          ),
    );
  }
}
