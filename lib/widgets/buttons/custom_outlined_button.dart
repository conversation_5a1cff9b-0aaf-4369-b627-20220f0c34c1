import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../texts/app_text.dart';

class CustomOutlinedButton extends StatelessWidget {
  final String? buttonText;
  final Widget? buttonWidget;
  final void Function()? onPressed;
  final Color? textColor;
  final double? height;
  final double? width;
  final EdgeInsets? padding;
  final Color? borderColor;
  final Color? backgroundColor;

  const CustomOutlinedButton({
    super.key,
    this.buttonText,
    this.buttonWidget,
    this.onPressed,
    this.textColor,
    this.height,
    this.width,
    this.padding,
    this.borderColor,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        padding: padding,
        minimumSize: Size(width ?? 64.w, height ?? 48.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        backgroundColor: backgroundColor,
        side: BorderSide(color: borderColor ?? colorScheme.primary, width: 1.w),
      ),
      onPressed: onPressed,
      child: buttonWidget ??
          Text14Bold(
            buttonText ?? '',
            color: textColor ?? colorScheme.primary,
            fontSize: 16.sp,
          ),
    );
  }
}
