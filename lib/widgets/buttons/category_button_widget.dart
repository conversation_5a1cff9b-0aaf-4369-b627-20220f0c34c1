import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/app_theme.dart';
import '../texts/app_text.dart';

class CategoryButtonRadioWidget extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback? onPressed;
  final Color? unselectedBorderColor;

  const CategoryButtonRadioWidget({
    super.key,
    required this.label,
    required this.isSelected,
    this.unselectedBorderColor,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.whiteF7F7F7,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
          side: BorderSide(
            color: isSelected ? colorScheme.primary : unselectedBorderColor ?? Colors.transparent,
          ),
        ),
        elevation: 0,
      ),
      onPressed: onPressed,
      child: Text14Bold(
        label,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
