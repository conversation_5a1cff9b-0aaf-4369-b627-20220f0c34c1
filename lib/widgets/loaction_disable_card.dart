import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../utils/string_constants/app_strings.dart';
import 'buttons/primary_button.dart';

class LocationDisableCard extends StatelessWidget {
  final void Function()? onEnableLocationPressed;
  const LocationDisableCard({super.key, required this.onEnableLocationPressed});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        gradient: LinearGradient(
          colors: [
            colorScheme.lightBrownFCEEE3,
            colorScheme.lightBrownF7D2B5,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SvgPicture.asset(
                AppImages.locationDisabled,
              ),
              4.pw,
              Text20SemiBold(
                AppStrings.locationDisabled,
                fontSize: 18.sp,
              ),
            ],
          ),
          4.ph,
          Text14Medium(
            AppStrings.locationUnavailableDescription,
          ),
          8.ph,
          PrimaryButton(
            buttonText: AppStrings.enableLocationAccess,
            backgroundColor: colorScheme.white,
            width: 170.w,
            onPressed: onEnableLocationPressed,
            child: Text14Bold(
              AppStrings.enableLocationAccess,
              color: colorScheme.primary,
            ),
          )
        ],
      ),
    );
  }
}
