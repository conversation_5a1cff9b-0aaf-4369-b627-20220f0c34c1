import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import '../utils/string_constants/app_strings.dart';
import 'buttons/primary_button.dart';
import 'texts/app_text.dart';

class Dialogs {
static void showLoadingDialog(BuildContext context,
      {String loadingText = AppStrings.loading}) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevents closing when tapping outside
      builder: (context) {
        return PopScope(
          canPop: false,
          child: Dialog(
            insetPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.r)),
            child: Padding(
              padding: EdgeInsets.all(16.h),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 16.h,
                    width: 16.h,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                  12.pw,
                  Text14Medium(loadingText)
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static void hideDialog(BuildContext context){
    Navigator.pop(context);
  }

  static void showCommonDialog({
    required BuildContext context,
    required String title,
    required String message,
    String? primaryButtonText = AppStrings.okay,
    required VoidCallback onPrimaryButtonTap,
    Color? primaryButtonColor,
    String? cancelButtonText = AppStrings.cancel,
    bool showCancelButton = true,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    // final isDesktop = screenWidth > 600; // Typical desktop breakpoint
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            Dialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: screenWidth * 0.3,
                  minWidth: screenWidth * 0.3,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text20SemiBold(title),
                      35.ph,
                      Text14Medium(
                        message,
                        textAlign: TextAlign.center,
                      ),
                      25.ph,
                      PrimaryButton(
                        onPressed: onPrimaryButtonTap,
                        buttonText: primaryButtonText,
                        ),
                      if (showCancelButton) ...[
                        12.ph,
                        PrimaryButton(
                          backgroundColor: colorScheme.primary,
                          onPressed: () => Navigator.of(context).pop(),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40.r),
                            side: BorderSide(
                              color: colorScheme.primary,
                              width: 1.2.h,
                            ),
                          ),
                          child: const Text14Medium(
                            AppStrings.cancel,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
