import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:shimmer/shimmer.dart';
import '../utils/string_constants/app_strings.dart';
import 'texts/app_text.dart';

class CustomMultiselectDropdown<T> extends StatefulWidget {
  final List<T> items; // List of objects (e.g., List<SportsModel>)
  final List<T> selectedValues; // Selected items
  final ValueChanged<List<T>>
      onSelectionChanged; // Callback when selection changes
  final bool isLoading;
  final bool isError;
  final String? hintText;
  final String? titleText;
  const CustomMultiselectDropdown(
      {super.key,
      required this.items,
      required this.selectedValues,
      required this.isLoading,
      required this.isError,
      this.hintText,
      this.titleText,
      required this.onSelectionChanged});

  @override
  State<CustomMultiselectDropdown<T>> createState() =>
      _CustomMultiselectDropdownState<T>();
}

class _CustomMultiselectDropdownState<T>
    extends State<CustomMultiselectDropdown<T>> {
  bool isOpen = false;
  late ColorScheme colorScheme;
  late TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    colorScheme = theme.colorScheme;
    textTheme = theme.textTheme;
    if (widget.isLoading) {
      return Shimmer.fromColors(
        baseColor: colorScheme.lightGreyDDDDDD,
        highlightColor: colorScheme.white,
        child: Container(
          width: double.infinity,
          height: 56.h,
          decoration: BoxDecoration(
              color: colorScheme.white,
              borderRadius: BorderRadius.circular(12.r)),
        ),
      );
    }
    if (widget.isError) {
      return InkWell(
        onTap: () {},
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text14Medium("Error occured while loading data",),
            const SizedBox(
              height: 5,
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.primary,
                  width: 2.0,
                ),
                //Border.all
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text(
                    AppStrings.refresh,
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  Icon(
                    Icons.refresh,
                    size: 15,
                    color: colorScheme.primary,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [DropdownButtonHideUnderline(
          child: DropdownButton2<T>(
            isExpanded: true,
            customButton: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border:
                    Border.all(color: colorScheme.lightGreyDDDDDD, width: 1.2),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text14Medium(
                    widget.hintText ?? "Select items",
                  ),
                  Icon(
                    isOpen
                        ? Icons.keyboard_arrow_up_rounded
                        : Icons.keyboard_arrow_down_rounded,
                    color: colorScheme.blue150045,
                  )
                ],
              ),
            ),
            items: widget.items.map((item) {
              return DropdownMenuItem<T>(
                value: item,
                enabled: false, // Disable default onTap
                child: StatefulBuilder(
                  builder: (context, menuSetState) {
                    final isSelected = widget.selectedValues.contains(item);
                    return InkWell(
                      splashColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          isSelected
                              ? widget.selectedValues.remove(item)
                              : widget.selectedValues.add(item);
                        });
                        menuSetState(() {});
                        widget.onSelectionChanged(widget.selectedValues);
                      },
                      child: SizedBox(
                        height: 56.h,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.h),
                          child: Row(
                            children: [
                              // isSelected
                              //     ? SvgPicture.asset(
                              //         AppImages.selectedCheckboxIc)
                              //     : SvgPicture.asset(AppImages.emptyCheckboxIc),
                              const SizedBox(width: 16),
                              Text14Medium(
                                (item as dynamic).name ?? "",
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
            }).toList(),
            value: widget.selectedValues.isEmpty
                ? null
                : widget.selectedValues.first,
            onChanged: (_) {}, // Not needed since we handle selection manually
            buttonStyleData: const ButtonStyleData(
                overlayColor: WidgetStatePropertyAll(Colors.transparent)),
            menuItemStyleData: MenuItemStyleData(
              padding: EdgeInsets.zero,
              height: 56.h,
              overlayColor:
                  WidgetStatePropertyAll(colorScheme.lightGreyDDDDDD),
            ),
            dropdownStyleData: DropdownStyleData(
              elevation: 0,
              decoration: BoxDecoration(
                color: colorScheme.white,
                border:
                    Border.all(color: colorScheme.lightGreyDDDDDD, width: 1.0),
                borderRadius: BorderRadius.circular(12),
              ),
              maxHeight: 300,
            ),
          ),
        ),
      ],
    );
  }
}
