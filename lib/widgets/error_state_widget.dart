import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../utils/extensions/empty_space_extn.dart';
import '../utils/string_constants/app_images.dart';
import '../utils/string_constants/app_strings.dart';
import 'buttons/custom_outlined_button.dart';
import 'texts/app_text.dart';

class ErrorStateWidget extends StatelessWidget {
  final bool showImage;
  final String errorMessage;
  final VoidCallback? onRetry;
  final double? height;
  final EdgeInsets? margin;
  final bool showRetryButton;
  final bool removeDecoration;

  const ErrorStateWidget({
    super.key,
    required this.errorMessage,
    this.onRetry,
    this.showImage = true,
    this.height,
    this.margin,
    this.showRetryButton = true,
    this.removeDecoration = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      height: height,
      width: double.infinity,
      margin: margin ?? EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.only(top: 32.h, bottom: 24.h),
      decoration: removeDecoration ? null : BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: colorScheme.primary),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showImage) ...[
            SvgPicture.asset(
              AppImages.errorStateImage,
              height: 100.h,
              width: 100.w,
            ),
            20.ph,
          ],
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Text14Bold(
              errorMessage,
              fontSize: 16.sp,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          if (showRetryButton) ...[
            24.ph,
          CustomOutlinedButton(
            onPressed: onRetry,
            buttonWidget: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.refresh,
                  color: colorScheme.primary,
                ),
                4.pw,
                Text14Bold(
                    AppStrings.retry,
                  fontSize: 16.sp,
                ),
              ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
