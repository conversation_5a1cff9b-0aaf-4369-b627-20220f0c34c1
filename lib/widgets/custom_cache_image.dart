import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../core/theme/app_theme.dart';

class CustomCacheImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double scale;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final Widget? errorWidget;

  const CustomCacheImage({
    super.key,
    required this.imageUrl,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.scale = 1.0,
    this.memCacheWidth,
    this.memCacheHeight,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return CachedNetworkImage(
      cacheManager: CustomCacheManager.instance,
      key: <PERSON><PERSON><PERSON>(imageUrl),
      imageUrl: imageUrl,
      height: height,
      width: width,
      fit: fit,
      scale: scale,
      placeholder: (_, _) => Shimmer.fromColors(
        baseColor: colorScheme.shimmerBaseColor,
        highlightColor: colorScheme.shimmerHighlightColor,
        child: Container(
          height: height ?? 80.0,
          width: width ?? 80.0,
          decoration: BoxDecoration(
            color: colorScheme.shimmerColor,
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
      ),
      errorWidget: (_, _, _) => errorWidget ?? SizedBox.shrink(),
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
    );
  }
}

class CustomCacheManager {
  static const key = 'customCacheKey';
  static final CacheManager _instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 7),
      maxNrOfCacheObjects: 500,
      repo: JsonCacheInfoRepository(databaseName: key),
      // fileSystem: IOFileSystem(key),
      fileService: HttpFileService(),
    ),
  );

  static CacheManager get instance => _instance;
}
