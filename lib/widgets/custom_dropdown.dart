import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../core/theme/app_theme.dart';
import '../utils/extensions/empty_space_extn.dart';
import 'dropdown_error_field.dart';
import 'shimmers/drop_down_shimmer.dart';
import 'texts/app_text.dart';

class CustomDropDownWidget<T> extends StatefulWidget {
  final List<DropdownMenuItem<T>>? items;
  final T? value;
  final String? selectedValue;
  final bool isLoading;
  final bool isError;
  final String? hintText;
  final ValueChanged<T?>? onChanged;
  final String? titleText;
  final bool readOnly;
  final void Function()? onErrorTap;

  const CustomDropDownWidget({
    super.key,
    required this.items,
    required this.value,
    this.onChanged,
    this.titleText,
    this.isLoading = false,
    this.isError = false,
    this.hintText,
    this.selectedValue,
    this.readOnly = false,
    this.onErrorTap,
  });

  @override
  State<CustomDropDownWidget<T>> createState() =>
      _CustomDropDownWidgetState<T>();
}

class _CustomDropDownWidgetState<T> extends State<CustomDropDownWidget<T>> {
  bool isOpen = false;
  late ColorScheme colorScheme;
  late TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    colorScheme = theme.colorScheme;
    textTheme = theme.textTheme;
    if (widget.isLoading) {
      return DropDownShimmer();
    }
    if (widget.isError) {
      return DropdownErrorField(
        onErrorTap: widget.onErrorTap,
        titleText: widget.titleText,
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.titleText != null) ...[
          Text14Medium(widget.titleText ?? ""),
          8.ph,
        ],
        DropdownButtonHideUnderline(
          child: DropdownButton2<T>(
            customButton: Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(12, 10, 12, 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: colorScheme.lightGreyDDDDDD,
                  width: 1.2,
                ),
                color: widget.readOnly ? colorScheme.lightGreyDDDDDD : null,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text14Medium(
                    widget.selectedValue ?? widget.hintText ?? "",
                  ),
                  Icon(
                    isOpen
                        ? Icons.keyboard_arrow_up_rounded
                        : Icons.keyboard_arrow_down_rounded,
                    color: colorScheme.blue150045,
                  )
                ],
              ),
            ),
            items: widget.readOnly ? null : widget.items,
            value: widget.value,
            onChanged: widget.readOnly ? null : widget.onChanged,
            buttonStyleData: const ButtonStyleData(
                overlayColor: WidgetStatePropertyAll(Colors.transparent)),
            dropdownStyleData: DropdownStyleData(
              elevation: 0,
              decoration: BoxDecoration(
                color: colorScheme.white,
                border: Border.all(
                  color: colorScheme.lightGreyDDDDDD,
                  width: 1.0,
                  strokeAlign: BorderSide.strokeAlignOutside,
                ),
                //Border.all
                borderRadius: BorderRadius.circular(12),
              ),
              maxHeight: 300,
            ),
            onMenuStateChange: widget.readOnly
                ? null
                : (isOpen) {
                    this.isOpen = isOpen;
                    setState(() {});
                  },
            menuItemStyleData: MenuItemStyleData(
                height: 40.h,
                overlayColor:
                    WidgetStatePropertyAll(colorScheme.lightGrey8F8F8F)),
          ),
        ),
      ],
    );
  }
}
