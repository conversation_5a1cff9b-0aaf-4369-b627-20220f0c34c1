import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../core/theme/app_theme.dart';
import '../utils/extensions/empty_space_extn.dart';
import 'app_gesture_detector.dart';
import 'dropdown_error_field.dart';
import 'shimmers/drop_down_shimmer.dart';
import 'texts/app_text.dart';

class CustomDialogSelectorWidget<T> extends StatefulWidget {
  final String? titleText;
  final String? displayText;
  final String? hintText;
  final T? value;
  final String? selectedValue;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final bool isLoading;
  final bool isError;
  final VoidCallback? onErrorTap;
  final bool readOnly;

  const CustomDialogSelectorWidget({
    super.key,
    this.titleText,
    required this.displayText,
    this.hintText,
    this.value,
    this.selectedValue,
    required this.items,
    required this.onChanged,
    this.isLoading = false,
    this.isError = false,
    this.onErrorTap,
    this.readOnly = false,
  });

  @override
  State<CustomDialogSelectorWidget<T>> createState() => _CustomDialogSelectorWidgetState<T>();
}

class _CustomDialogSelectorWidgetState<T> extends State<CustomDialogSelectorWidget<T>> {
  late ColorScheme colorScheme;
  late TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    colorScheme = theme.colorScheme;
    textTheme = theme.textTheme;

    if (widget.isLoading) {
      return const DropDownShimmer();
    }

    if (widget.isError) {
      return DropdownErrorField(
        onErrorTap: widget.onErrorTap,
        titleText: widget.titleText,
      );
    }

    String displayValue = widget.selectedValue != null
        ? widget.selectedValue!
        : widget.hintText ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.titleText != null) ...[
          Text14Medium(widget.titleText!),
          8.ph,
        ],
        GestureDetector(
          onTap: widget.readOnly || widget.items.isEmpty ? null : _showSelectionDialog,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: colorScheme.lightGreyDEDEDE,
                width: 1.2,
              ),
              color: widget.readOnly ? colorScheme.lightGreyDEDEDE : colorScheme.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text14Medium(
                    displayValue,
                    color: widget.value == null && !widget.readOnly
                        ? Colors.grey
                        : null,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                8.pw,
                Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: widget.readOnly
                      ? colorScheme.lightGreyDEDEDE
                      : colorScheme.blue150045,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showSelectionDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            T? currentDialogValue = widget.value;

            return Dialog(
              backgroundColor: colorScheme.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              insetPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
              child: Container(
                constraints: BoxConstraints(maxHeight: 450.h),
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h).copyWith(bottom: 38.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text20Bold(
                            widget.displayText ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        AppGestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Icon(
                            Icons.close_rounded,
                            color: colorScheme.blue150045,
                          ),
                        ),
                      ],
                    ),
                    Divider(height: 1, thickness: 1, color: colorScheme.lightGreyDEDEDE),

                    8.ph,

                    Flexible(
                      child: ListView.builder(
                        shrinkWrap: true,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        itemCount: widget.items.length,
                        itemBuilder: (context, index) {
                          final item = widget.items[index];
                          final bool isSelected = item.value == currentDialogValue;

                          return AppGestureDetector(
                            onTap: () {
                              setDialogState(() {
                                currentDialogValue = item.value;
                              });
                              widget.onChanged?.call(item.value);
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 12.h),
                              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 15.h),
                              decoration: BoxDecoration(
                                color: colorScheme.white,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: isSelected
                                      ? colorScheme.primary
                                      : colorScheme.lightGreyD5D5D5,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(child: item.child),
                                  Container(
                                    width: 20.h,
                                    height: 20.h,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: isSelected
                                            ? colorScheme.primary
                                            : colorScheme.lightGreyD5D7DA,
                                        width: isSelected ? 6 : 1,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
