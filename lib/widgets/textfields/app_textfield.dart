import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';

class AppTextFormField extends StatelessWidget {
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final bool readOnly;
  final String? labelText;
  final void Function(String)? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final FormFieldValidator<String>? validator;
  final int? inputCharacterLimit;
  final bool isAutovalidateModeOn;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? prefixText;
  final String? hintText;
  final Color? cursorColor;
  final bool? isenabled;
  final bool isParentField;
  final bool? filled;
  final Color? fillColor;
  final Color? textColor;
  final String? initialValue;
  final Color? enabledBorderColor;
  final int? maxLength;
  final String? suffixText;
  final InputDecoration? decoration;
  final void Function(String str)? onSubmitted;
  final void Function()? onTap;
  final String? titleText;
  final String? indicatorText;
  final BoxConstraints? prefixIconConstraints;
  final Color? hintTextColor;
  final FontWeight? hintTextFontWeight;
  final Iterable<String>? autofillHints;
  final FocusNode? focusNode;

  const AppTextFormField({
    super.key,
    this.controller,
    this.keyboardType,
    this.readOnly = false,
    this.labelText,
    this.validator,
    this.inputCharacterLimit,
    this.isAutovalidateModeOn = false,
    this.textInputAction,
    this.prefixIcon,
    this.prefixText,
    this.suffixIcon,
    this.onChanged,
    this.inputFormatters,
    this.maxLines,
    this.isenabled,
    this.onSubmitted,
    this.hintText,
    this.cursorColor,
    this.contentPadding,
    this.decoration,
    this.textColor,
    this.filled,
    this.fillColor,
    this.enabledBorderColor,
    this.maxLength = 350,
    this.suffixText,
    this.initialValue,
    this.titleText,
    this.prefixIconConstraints,
    this.isParentField = false,
    this.onTap,
    this.indicatorText,
    this.hintTextColor,
    this.hintTextFontWeight,
    this.autofillHints,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    TextTheme textTheme = theme.textTheme;
    ColorScheme colorScheme = theme.colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (titleText != null) ...[
          Text12Medium(titleText ?? ""),
          8.ph,
        ],
        TextFormField(
          scrollPadding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 40.h),
          initialValue: initialValue,
          enabled: isenabled,
          focusNode: focusNode,
          onTap: onTap,
          controller: controller,
          keyboardType: keyboardType ?? TextInputType.text,
          readOnly: readOnly,
          validator: validator,
          textInputAction: textInputAction ?? TextInputAction.next,
          onChanged: onChanged,
          cursorColor: cursorColor ?? colorScheme.blue150045,
          inputFormatters: inputFormatters,
          autovalidateMode: isAutovalidateModeOn
              ? AutovalidateMode.always
              : AutovalidateMode.disabled,
          onFieldSubmitted: onSubmitted,
          maxLines: maxLines,
          maxLength: maxLength,
          style: textTheme.displayMedium,
          autofillHints: autofillHints,
          decoration: InputDecoration(
            prefixIconConstraints: prefixIconConstraints,
            hintText: hintText,
            counterText: "",
            hintStyle: textTheme.displayMedium!.copyWith(color: colorScheme.lightGrey8F8F8F),
            suffixIcon: suffixIcon,
            suffixText: suffixText,
            prefixIcon: prefixIcon,
            prefixText: prefixText,
            labelText: labelText,
            filled: filled,
            fillColor: fillColor,
            isDense: true,
            errorStyle: textTheme.bodySmall!
                .copyWith(color: colorScheme.error, fontSize: 9),
            errorMaxLines: 3,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide:
                  BorderSide(
                color: colorScheme.lightGreyDDDDDD,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide:
                  BorderSide(
                color: colorScheme.lightGreyDDDDDD,
              ),
            ),
            errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: colorScheme.error)),
            focusedErrorBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: colorScheme.error,
                ),
                borderRadius: BorderRadius.circular(12.r)),
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    color: enabledBorderColor ?? colorScheme.lightGreyDDDDDD,
                    width: 1.2),
                borderRadius: BorderRadius.circular(12.r)),
            focusedBorder: OutlineInputBorder(
                borderSide:
                    BorderSide(
                  color: colorScheme.primary,
                ),
                borderRadius: BorderRadius.circular(12.r)),
          ),
        ),
      ],
    );
  }
}
