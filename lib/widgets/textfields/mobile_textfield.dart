import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/app_theme.dart';
import '../../utils/string_constants/app_strings.dart';
import '../../utils/string_constants/country_codes.dart';
import '../../utils/validations.dart';
import 'app_textfield.dart';

class MobileTextfield extends StatelessWidget {
  final TextEditingController? controller;
  final Widget? prefixIcon;
  final String? initialCountrySelection;
  final TextInputAction? textInputAction;
  final bool isAutovalidateModeOn;
  final void Function(String str)? onChangedValue;
  final FocusNode? focusNode;
  final void Function(String)? onSubmitted;
  final void Function(CountryCode)? onCountryChanged;
  final Widget? suffixIcon;
  final Function()? onTap;

  const MobileTextfield({
    super.key,
    this.controller,
    this.prefixIcon,
    this.textInputAction,
    this.isAutovalidateModeOn = false,
    this.onChangedValue,
    this.focusNode,
    this.onSubmitted,
    this.initialCountrySelection,
    this.suffixIcon,
    this.onCountryChanged,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    return AppTextFormField(
      suffixIcon: suffixIcon,
      onSubmitted: onSubmitted,
      focusNode: focusNode,
      titleText: AppStrings.phoneNumber,
      validator: (value) {
        return Validator.phoneNumberValidator(value!);
      },
      hintText: AppStrings.enterPhoneNumber,
      controller: controller,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp("[0-9]")),
        LengthLimitingTextInputFormatter(10),
      ],
      prefixIcon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CountryCodePicker(
            // margin: EdgeInsets.only(right: 8.w),
            padding: EdgeInsets.zero,
            showFlag: false,
            showDropDownButton: false,
            flagWidth: 0.w,
            dialogSize: Size(
              MediaQuery.of(context).size.width * 0.8,
              MediaQuery.of(context).size.height * 0.8,
            ),
            countryList: countryCodes,
            onChanged: onCountryChanged,
            initialSelection: initialCountrySelection ?? 'IN',
            textStyle: textTheme.displayMedium,
            headerText: AppStrings.selectCountry,
            dialogTextStyle: textTheme.titleMedium,
            searchDecoration: InputDecoration(
              hintText: AppStrings.search,
              hintStyle: textTheme.titleMedium,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            searchStyle: textTheme.titleMedium,
          ),
          Transform.translate(
            offset: Offset(-5.w, 0),
            child: SizedBox(
              height: 24.h,
              child: VerticalDivider(
                color: colorScheme.blue150045,
                thickness: 1,
                width: 1,
              ),
            ),
          ),
        ],
      ),
      // cursorColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      inputCharacterLimit: 10,
      isAutovalidateModeOn: isAutovalidateModeOn,
      textInputAction: textInputAction ?? TextInputAction.next,
      keyboardType: TextInputType.phone,
      onChanged: onChangedValue,
      autofillHints: [
        AutofillHints.telephoneNumberDevice,
        AutofillHints.telephoneNumber,
      ],
      onTap: onTap,
    );
  }
}
