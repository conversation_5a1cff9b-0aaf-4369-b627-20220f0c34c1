import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:http/http.dart';
import 'package:homeservice_app/core/routes/app_routes.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/location_methods.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';
import 'package:homeservice_app/utils/string_constants/app_strings.dart';
import 'package:homeservice_app/widgets/buttons/primary_button.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../../../features/my_addresses/data/add_complete_address_nav_data.dart';
import '../../../features/my_addresses/data/my_address_model.dart';
import '../../custom_appbar.dart';
import '../map_location_picker.dart';

class MapLocationPicker extends StatefulWidget {
  /// Padding around the map
  final EdgeInsets padding;

  /// Compass for the map (default: true)
  final bool compassEnabled;

  /// Lite mode for the map (default: false)
  final bool liteModeEnabled;

  /// API key for the map & places
  final String apiKey;

  /// GPS accuracy for the map
  final LocationAccuracy desiredAccuracy;

  /// GeoCoding base url
  final String? geoCodingBaseUrl;

  /// GeoCoding http client
  final Client? geoCodingHttpClient;

  /// GeoCoding api headers
  final Map<String, String>? geoCodingApiHeaders;

  /// GeoCoding location type
  final List<String> locationType;

  /// GeoCoding result type
  final List<String> resultType;

  /// Map minimum zoom level & maximum zoom level
  final MinMaxZoomPreference minMaxZoomPreference;

  /// Top card margin
  final EdgeInsetsGeometry topCardMargin;

  /// Top card color
  final Color? topCardColor;

  /// Top card shape
  final ShapeBorder topCardShape;

  /// Top card text field border radius
  final BorderRadiusGeometry borderRadius;

  /// Top card text field hint text
  final String searchHintText;

  /// Bottom card shape
  final ShapeBorder bottomCardShape;

  /// Bottom card margin
  final EdgeInsetsGeometry bottomCardMargin;

  /// Bottom card icon
  final Icon bottomCardIcon;

  /// Bottom card tooltip
  final String bottomCardTooltip;

  /// Bottom card color
  final Color? bottomCardColor;

  /// On location permission callback
  final bool hasLocationPermission;

  /// detect location button click callback
  final Function()? getLocation;

  /// On Suggestion Selected callback
  final Function(PlacesDetailsResponse?)? onSuggestionSelected;

  /// On Next Page callback
  final Function(GeocodingResult?)? onNext;

  /// When tap on map decode address callback function
  final Function(GeocodingResult?)? onDecodeAddress;

  /// Show back button (default: true)
  final bool hideBackButton;

  /// Popup route on next press (default: false)
  final bool popOnNextButtonTaped;

  /// Back button replacement when [hideBackButton] is false and [backButton] is not null
  final Widget? backButton;

  /// Show more suggestions
  final bool hideMoreOptions;

  /// Dialog title
  final String dialogTitle;

  /// httpClient is used to make network requests.
  final Client? placesHttpClient;

  /// apiHeader is used to add headers to the request.
  final Map<String, String>? placesApiHeaders;

  /// baseUrl is used to build the url for the request.
  final String? placesBaseUrl;

  /// Session token for Google Places API
  final String? sessionToken;

  /// Offset for pagination of results
  /// offset: int,
  final num? offset;

  /// Origin location for calculating distance from results
  /// origin: Location(lat: -33.852, lng: 151.211),
  final Location? origin;

  /// currentLatLng init location for camera position
  /// currentLatLng: Location(lat: -33.852, lng: 151.211),
  final LatLng? currentLatLng;

  /// Location bounds for restricting results to a radius around a location
  /// location: Location(lat: -33.867, lng: 151.195)
  final Location? location;

  /// Radius for restricting results to a radius around a location
  /// radius: Radius in meters
  final num? radius;

  /// Language code for Places API results
  /// language: 'en',
  final String? language;

  /// Types for restricting results to a set of place types
  final List<String> types;

  /// Components set results to be restricted to a specific area
  /// components: [Component(Component.country, "us")]
  final List<Component> components;

  /// Bounds for restricting results to a set of bounds
  final bool strictbounds;

  /// Region for restricting results to a set of regions
  /// region: "us"
  final String? region;

  /// List of fields to be returned by the Google Maps Places API.
  /// Refer to the Google Documentation here for a list of valid values: https://developers.google.com/maps/documentation/places/web-service/details
  final List<String> fields;

  /// Map type (default: MapType.normal)
  final MapType mapType;

  /// Hide top search bar (default: false)
  final bool hideSearchBar;

  /// Search text field controller
  final TextEditingController? searchController;

  /// Add your own custom markers
  final Map<String, LatLng>? additionalMarkers;

  /// Safe area parameters (default: true)
  final bool bottom;
  final bool left;
  final bool maintainBottomViewPadding;
  final EdgeInsets minimum;
  final bool right;
  final bool top;

  /// Address id
  final int? addressId;

  /// Is edit
  final bool isEdit;
  final double? latitude;
  final double? longitude;

  /// hide location button and map type button (default: false)
  final bool hideLocationButton;
  final bool hideMapTypeButton;

  /// hide bottom card (default: false)
  final bool hideBottomCard;

  /// Focus node for the search text field
  final FocusNode? focusNode;

  /// Tooltip for the FAB button.
  final String fabTooltip;

  /// FAB icon
  final IconData fabIcon;

  /// Minimum number of characters to trigger the autocomplete
  /// Defaults to 0
  final int minCharsForSuggestions;

  /// Map styles, you can style a map to be dark or light, see more at
  /// [https://stackoverflow.com/questions/49953913/flutter-styled-map] and see here to create a json
  /// styling and load it from assets as a string [https://mapstyle.withgoogle.com]
  final String? mapStyle;

  /// Enables or disables showing 3D buildings where available
  final bool buildingsEnabled;

  /// Geographical bounding box for the camera target.
  final CameraTargetBounds cameraTargetBounds;

  /// Circles to be placed on the map.
  final Set<Circle> circles;

  /// Identifier that's associated with a specific cloud-based map style.
  ///
  /// See https://developers.google.com/maps/documentation/get-map-id
  /// for more details.
  final String? cloudMapId;

  /// True if 45 degree imagery should be enabled. Web only.
  final bool fortyFiveDegreeImageryEnabled;

  /// Enables or disables the indoor view from the map
  final bool indoorViewEnabled;

  /// The layout direction to use for the embedded view.
  ///
  /// If this is null, the ambient [Directionality] is used instead. If there is
  /// no ambient [Directionality], [TextDirection.ltr] is used.
  final TextDirection? layoutDirection;

  /// True if the map should show a toolbar when you interact with the map. Android only.
  final bool mapToolbarEnabled;

  /// Called when camera movement has ended, there are no pending
  /// animations and the user has stopped interacting with the map.
  final VoidCallback? onCameraIdle;

  /// Called when the camera starts moving.
  ///
  /// This can be initiated by the following:
  /// 1. Non-gesture animation initiated in response to user actions.
  ///    For example: zoom buttons, my location button, or marker clicks.
  /// 2. Programmatically initiated animation.
  /// 3. Camera motion initiated in response to user gestures on the map.
  ///    For example: pan, tilt, pinch to zoom, or rotate.
  final VoidCallback? onCameraMoveStarted;

  /// Called every time a [GoogleMap] is long pressed.
  final ArgumentCallback<LatLng>? onLongPress;

  /// Polygons to be placed on the map.
  final Set<Polygon> polygons;

  /// Focus node for the search text field
  final bool isFocus;

  /// Polylines to be placed on the map.
  final Set<Polyline> polylines;

  /// True if the map view should respond to rotate gestures.
  final bool rotateGesturesEnabled;

  /// True if the map view should respond to scroll gestures.
  final bool scrollGesturesEnabled;

  /// Tile overlays to be placed on the map.
  final Set<TileOverlay> tileOverlays;

  /// True if the map view should respond to tilt gestures.
  final bool tiltGesturesEnabled;

  /// Enables or disables the traffic layer of the map
  final bool trafficEnabled;

  final MyAddressModel? address;

  /// This setting controls how the API handles gestures on the map. Web only.
  ///
  /// See [WebGestureHandling] for more details.
  final WebGestureHandling? webGestureHandling;

  /// True if the map view should respond to zoom gestures.
  final bool zoomGesturesEnabled;

  final InputDecoration? decoration;

  final String? cameFrom;

  final Widget? Function(
    BuildContext context,
    GeocodingResult? result,
    String address,
  )? bottomCardBuilder;

  /// Duration for search debounce in milliseconds
  final Duration debounceDuration;

  const MapLocationPicker({
    super.key,
    this.desiredAccuracy = LocationAccuracy.high,
    required this.apiKey,
    this.geoCodingBaseUrl,
    this.geoCodingHttpClient,
    this.geoCodingApiHeaders,
    this.language,
    this.locationType = const [],
    this.resultType = const [],
    this.minMaxZoomPreference = const MinMaxZoomPreference(0, 16),
    this.padding = const EdgeInsets.all(0),
    this.compassEnabled = true,
    this.liteModeEnabled = false,
    this.topCardMargin = const EdgeInsets.all(8),
    this.topCardColor,
    this.topCardShape = const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(12)),
    ),
    this.borderRadius = const BorderRadius.all(Radius.circular(12)),
    this.searchHintText = "Start typing to search",
    this.bottomCardShape = const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(12)),
    ),
    this.bottomCardMargin = const EdgeInsets.fromLTRB(8, 8, 8, 16),
    this.bottomCardIcon = const Icon(Icons.send),
    this.bottomCardTooltip = "Continue with this location",
    this.bottomCardColor,
    this.hasLocationPermission = true,
    this.getLocation,
    this.onSuggestionSelected,
    this.onNext,
    this.currentLatLng = const LatLng(28.8993468, 76.6250249),
    this.hideBackButton = false,
    this.popOnNextButtonTaped = false,
    this.backButton,
    this.hideMoreOptions = false,
    this.dialogTitle = 'You can also use the following options',
    this.placesHttpClient,
    this.placesApiHeaders,
    this.placesBaseUrl,
    this.sessionToken,
    this.offset,
    this.origin,
    this.location,
    this.radius,
    this.region,
    this.fields = const [],
    this.types = const [],
    this.components = const [],
    this.strictbounds = false,
    this.mapType = MapType.normal,
    this.hideSearchBar = false,
    this.searchController,
    this.additionalMarkers,
    this.bottom = true,
    this.left = true,
    this.maintainBottomViewPadding = false,
    this.minimum = EdgeInsets.zero,
    this.right = true,
    this.top = true,
    this.hideLocationButton = false,
    this.hideMapTypeButton = false,
    this.hideBottomCard = false,
    this.onDecodeAddress,
    this.focusNode,
    this.mapStyle,
    this.fabTooltip = 'My Location',
    this.fabIcon = Icons.my_location,
    this.minCharsForSuggestions = 0,
    this.buildingsEnabled = true,
    this.cameraTargetBounds = CameraTargetBounds.unbounded,
    this.circles = const <Circle>{},
    this.cloudMapId,
    this.fortyFiveDegreeImageryEnabled = false,
    this.indoorViewEnabled = false,
    this.layoutDirection,
    this.mapToolbarEnabled = true,
    this.onCameraIdle,
    this.onCameraMoveStarted,
    this.onLongPress,
    this.polygons = const <Polygon>{},
    this.polylines = const <Polyline>{},
    this.rotateGesturesEnabled = true,
    this.scrollGesturesEnabled = true,
    this.tileOverlays = const <TileOverlay>{},
    this.tiltGesturesEnabled = true,
    this.trafficEnabled = true,
    this.webGestureHandling,
    this.zoomGesturesEnabled = true,
    this.decoration,
    this.bottomCardBuilder,
    this.debounceDuration = const Duration(milliseconds: 500),
    this.addressId,
    this.isEdit = false,
    this.latitude,
    this.longitude,
    this.isFocus = false,
    this.address,
    this.cameFrom,
  });

  @override
  State<MapLocationPicker> createState() => _MapLocationPickerState();
}

class _MapLocationPickerState extends State<MapLocationPicker> {
  /// Map controller for movement & zoom
  final Completer<GoogleMapController> _controller = Completer();

  /// initial latitude & longitude
  late LatLng _initialPosition = const LatLng(28.8993468, 76.6250249);

  /// initial address text
  late String _address = "Tap on map to get address";

  /// initial zoom level
  late double _zoom = 18.0;

  /// GeoCoding result for further use
  GeocodingResult? _geocodingResult;

  double? lat;
  double? long;

  /// Camera position moved to location
  CameraPosition cameraPosition() {
    return CameraPosition(
      target: _initialPosition,
      zoom: _zoom,
    );
  }

  /// Search text field controller
  late TextEditingController _searchController = TextEditingController();

  /// Decode address from latitude & longitude
  void _decodeAddress(Location location) async {
    try {
      await LocationMethods.decodeAddress(lat: location.lat, long: location.lng)
          .then((value) {
        if (!value.success) {
          _address = value.address;
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(value.address),
              ),
            );
          }
          return;
        }
        _address = value.address;
        _geocodingResult = value.geocodingResult;
        lat = value.lat;
        long = value.long;
        markers.removeWhere((element) => element.markerId == MarkerId('one'));
        markers.add(
          Marker(
            markerId: const MarkerId('one'),
            position: LatLng(location.lat, location.lng),
            icon: sourceIcon ?? BitmapDescriptor.defaultMarker,
          ),
        );
        setState(() {});
      });
    } catch (e) {
      _address = "Address not found, something went wrong!";
    }
  }

  bool isLocationDenied = false;

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
    setSourceAndDestinationIcons();
    _initialPosition = widget.currentLatLng ?? _initialPosition;
    _searchController = widget.searchController ?? _searchController;
    if (widget.latitude != null && widget.longitude != null) {
      _initialPosition = LatLng(widget.latitude ?? 0, widget.longitude ?? 0);
      _decodeAddress(Location(lat: widget.latitude!, lng: widget.longitude!));
    } else {
      Geolocator.getCurrentPosition().then((value) async {
        _initialPosition = LatLng(value.latitude, value.longitude);
        final controller = await _controller.future;
        controller.animateCamera(
          CameraUpdate.newCameraPosition(cameraPosition()),
        );
        _decodeAddress(Location(lat: value.latitude, lng: value.longitude));
      });
    }
  }

  /// Checks location permission and updates state accordingly.
  Future<void> _checkLocationPermission() async {
    final permission = await Geolocator.checkPermission();
    final denied = permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever;

    if (mounted && isLocationDenied != denied) {
      setState(() {
        isLocationDenied = denied;
      });
    }
  }

  BitmapDescriptor? sourceIcon;
  Set<Marker> markers = {};

  void setSourceAndDestinationIcons() async {
    sourceIcon = await BitmapDescriptor.asset(
        ImageConfiguration(devicePixelRatio: 2.5), AppImages.mapPinFilledIc,
        height: 32, width: 32);
    markers.add(
      Marker(
        markerId: const MarkerId('one'),
        position: _initialPosition,
        icon: sourceIcon ?? BitmapDescriptor.defaultMarker,
      ),
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    ColorScheme colorScheme = theme.colorScheme;
    TextTheme textTheme = theme.textTheme;
    return Scaffold(
      appBar: CustomAppbar(
        title: AppStrings.chooseLocation,
      ),
      body: Stack(
        children: [
          /// Google map view
          GoogleMap(
            minMaxZoomPreference: widget.minMaxZoomPreference,
            onCameraMove: (CameraPosition position) {
              /// set zoom level
              _zoom = position.zoom;
            },
            initialCameraPosition: CameraPosition(
              target: _initialPosition,
              zoom: _zoom,
            ),
            onTap: (LatLng position) async {
              _initialPosition = position;
              final controller = await _controller.future;
              controller.animateCamera(
                CameraUpdate.newCameraPosition(cameraPosition()),
              );
              _decodeAddress(
                Location(
                  lat: position.latitude,
                  lng: position.longitude,
                ),
              );
              setState(() {});
            },
            onMapCreated: (GoogleMapController controller) =>
                _controller.complete(controller),
            markers: markers,
            myLocationButtonEnabled: false,
            myLocationEnabled: false,
            zoomControlsEnabled: false,
            padding: widget.padding,
            compassEnabled: widget.compassEnabled,
            liteModeEnabled: widget.liteModeEnabled,
            mapType: widget.mapType,
            style: widget.mapStyle,
            buildingsEnabled: widget.buildingsEnabled,
            cameraTargetBounds: widget.cameraTargetBounds,
            circles: widget.circles,
            cloudMapId: widget.cloudMapId,
            fortyFiveDegreeImageryEnabled: widget.fortyFiveDegreeImageryEnabled,
            indoorViewEnabled: widget.indoorViewEnabled,
            layoutDirection: widget.layoutDirection,
            mapToolbarEnabled: widget.mapToolbarEnabled,
            onCameraIdle: widget.onCameraIdle,
            onCameraMoveStarted: widget.onCameraMoveStarted,
            onLongPress: widget.onLongPress,
            polygons: widget.polygons,
            polylines: widget.polylines,
            rotateGesturesEnabled: widget.rotateGesturesEnabled,
            scrollGesturesEnabled: widget.scrollGesturesEnabled,
            tileOverlays: widget.tileOverlays,
            tiltGesturesEnabled: widget.tiltGesturesEnabled,
            trafficEnabled: widget.trafficEnabled,
            webGestureHandling: widget.webGestureHandling,
            zoomGesturesEnabled: widget.zoomGesturesEnabled,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              PlacesAutocomplete(
                hideOnEmpty: true,
                hideOnSelect: true,
                isFocus: widget.isFocus,
                hideWithKeyboard: true,
                loadingBuilder: (context) {
                  return Container(
                    padding: EdgeInsets.symmetric(vertical: 4.w),
                    color: Theme.of(context).colorScheme.white,
                    width: double.infinity,
                    height: 52.h,
                    child: Center(
                      child: SizedBox(
                        height: 24.h,
                        width: 24.h,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                  );
                },
                decoration: widget.decoration ??
                    InputDecoration(
                      contentPadding: EdgeInsets.zero,
                      fillColor: colorScheme.white,
                      filled: true,
                      prefixIcon: Padding(
                        padding: EdgeInsets.only(left: 12.w, right: 8.w),
                        child: SvgPicture.asset(AppImages.searchIcon),
                      ),
                      prefixIconConstraints: BoxConstraints(
                        minHeight: 20.h,
                        minWidth: 20.h,
                      ),
                      hintText: AppStrings.searchForAreaStreetName,
                      hintStyle: textTheme.bodySmall!.copyWith(
                          fontSize: 14.sp, color: colorScheme.lightGrey8F8F8F),
                      focusedErrorBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: colorScheme.error,
                          ),
                          borderRadius: BorderRadius.circular(12.r)),
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: colorScheme.lightGreyDDDDDD, width: 1.2),
                          borderRadius: BorderRadius.circular(12.r)),
                      focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: colorScheme.primary,
                          ),
                          borderRadius: BorderRadius.circular(12.r)),
                    ),
                apiKey: widget.apiKey,
                mounted: mounted,
                searchController: _searchController,
                radius: widget.radius,
                components: widget.components,
                fields: widget.fields,
                language: widget.language,
                location: widget.location,
                origin: widget.origin,
                placesApiHeaders: widget.placesApiHeaders,
                placesBaseUrl: widget.placesBaseUrl,
                region: widget.region,
                sessionToken: widget.sessionToken,
                strictbounds: widget.strictbounds,
                types: widget.types,
                onGetDetailsByPlaceId: (placesDetails) async {
                  if (placesDetails == null) {
                    return;
                  }
                  _initialPosition = LatLng(
                    placesDetails.result.geometry?.location.lat ?? 0,
                    placesDetails.result.geometry?.location.lng ?? 0,
                  );
                  final controller = await _controller.future;
                  controller.animateCamera(
                      CameraUpdate.newCameraPosition(cameraPosition()));
                  _decodeAddress(Location(
                    lat: placesDetails.result.geometry?.location.lat ?? 0,
                    lng: placesDetails.result.geometry?.location.lng ?? 0,
                  ));
                  _address = placesDetails.result.formattedAddress ?? "";
                  widget.onSuggestionSelected?.call(placesDetails);
                  setState(() {});
                },
              ),
              const Spacer(),
              InkWell(
                onTap: isLocationDenied
                    ? null
                    : () async {
                  final position = await Geolocator.getCurrentPosition();
                  _initialPosition =
                      LatLng(position.latitude, position.longitude);
                  final controller = await _controller.future;
                  controller.animateCamera(
                      CameraUpdate.newCameraPosition(cameraPosition()));
                  _decodeAddress(Location(
                      lat: position.latitude, lng: position.longitude));
                  setState(() {});
                },
                child: Container(
                  margin: EdgeInsets.only(right: 20.w, bottom: 8.h),
                  alignment: Alignment.center,
                  height: 44.h,
                  width: 44.h,
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: SvgPicture.asset(
                    AppImages.locationIcon,
                    colorFilter: isLocationDenied ? ColorFilter.mode(
                      colorScheme.lightGrey8F8F8F,
                      BlendMode.srcIn,
                    ) : null,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 24.w),
                decoration: BoxDecoration(
                  color: colorScheme.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                            padding: EdgeInsets.all(4.h),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: colorScheme.primary.withOpacity(0.1),
                            ),
                            child: SvgPicture.asset(AppImages.mapPinIcon)),
                        8.pw,
                        Expanded(child: Text12Regular(_address)),
                      ],
                    ),
                    20.ph,
                    PrimaryButton(
                      buttonText: AppStrings.continueTxt,
                      onPressed: _geocodingResult == null
                          ? null
                          : () async {
                              Navigator.pushNamed(
                                  context, AppRoute.addCompleteAddress,
                                  arguments: AddCompleteAddressNavData(
                                    geocodingResult: _geocodingResult,
                                    latitude: lat,
                                    addressId: widget.addressId,
                                    address: widget.address,
                                    isEdit: widget.isEdit,
                                    longitude: long,
                                    cameFrom: widget.cameFrom,
                                  ));
                            },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
