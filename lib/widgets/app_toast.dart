import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import 'package:vibration/vibration.dart';
import '../core/dependency_injection/service_locator.dart';
import '../core/navigation/navigation_service.dart';

enum CustomToastGravity { top, bottom }

class CustomToast {
  static Future<void> showToast({
    required String message,
    bool isSuccess = false,
    bool isInfo = false,
    bool autoDismiss = true,
    CustomToastGravity gravity = CustomToastGravity.top,
    int durationInSec = 3,
    double position = 0,
    Color? errorColor,
    Widget? messageWidget,
    bool hapticEnabled=true,
    ///position from baseline
  }) async {
    var navKey = serviceLocator<NavigationService>().getGlobalKey();
    bool showFromBottom = (gravity == CustomToastGravity.bottom);
    final overlay = navKey.currentState?.overlay;
    late OverlayEntry overlayEntry;
    if (overlay == null) return;
    overlayEntry = OverlayEntry(
      builder: (context) =>
          Positioned(
            left: 0,
            right: 0,
            top: showFromBottom ? null : 10,
            bottom: showFromBottom ? 10 : null,
            child: Material(
              color: Colors.transparent,
              child: SafeArea(
                child: _SlideToast(
                  message: message,
                  showFromBottom: showFromBottom,
                  durationInSec: durationInSec,
                  autoDismiss: autoDismiss,
                  isSuccess: isSuccess,
                  isInfo: isInfo,
                  errorColor: errorColor,
                  onDismiss: () {
                    overlayEntry.remove();
                  }, messageWidget: messageWidget,
                ),
              ),
            ),
          ),
    );
    overlay.insert(overlayEntry);
    if ((await Vibration.hasVibrator()) && hapticEnabled) {
      Vibration.vibrate();
    }
    }
}

class _SlideToast extends StatefulWidget {
  final String message;
  final bool isSuccess;
  final bool isInfo;
  final bool autoDismiss;
  final bool showFromBottom;
  final int durationInSec;
  final VoidCallback onDismiss;
  final Color? errorColor;
  final Widget? messageWidget;

  const _SlideToast(
      {required this.message,
        required this.onDismiss,
        required this.isSuccess,
        required this.autoDismiss,
        required this.durationInSec,
        required this.showFromBottom, this.errorColor,
        required this.messageWidget,
        required this.isInfo,
      });

  @override
  State<_SlideToast> createState() => _SlideToastState();
}

class _SlideToastState extends State<_SlideToast>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _animationController.forward();

    Future.delayed(Duration(seconds: widget.durationInSec), () async {
      if (!_animationController.isDismissed && widget.autoDismiss) {
        try {
          _animationController.reverse().then((value) {
            widget.onDismiss();
          });
        } catch (error) {
          if (kDebugMode) print("The ToastAnimationController is disposed!");
        }
      }
    });
  }

    void _dismissToast() {
    _animationController.reverse().then((_) {
      widget.onDismiss();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme
        .of(context)
        .colorScheme;
    return SlideTransition(
      position: Tween<Offset>(
        begin: Offset(0, widget.showFromBottom ? 1 : -1),
        end: const Offset(0, 0),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      )),
      child: GestureDetector(
        onVerticalDragEnd: (details) => _dismissToast(),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.isSuccess
              ? colorScheme.greenB5E1B1
              : widget.isInfo
              ? colorScheme.orangeF5C7A3
              : colorScheme.redEFD0C7,
            ),
    borderRadius: BorderRadius.circular(8.r),
      color: widget.isSuccess
              ? colorScheme.greenF0FFF0
              : widget.isInfo
              ? colorScheme.orangeFAE3D1
              : colorScheme.redFBEFEB,
    ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              SvgPicture.asset(
                widget.isSuccess || widget.isInfo
                    ? AppImages.checkGreenIcon
                    : AppImages.infoRedIcon,
                fit: BoxFit.contain,
                colorFilter: widget.isInfo ? ColorFilter.mode(
                  colorScheme.blue150045,
                  BlendMode.srcIn,
                ) : null,
              ),
              8.pw,
              Flexible(
                fit: FlexFit.tight,
                child: widget.messageWidget ?? Text12Medium(
                  widget.message,
                    color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
