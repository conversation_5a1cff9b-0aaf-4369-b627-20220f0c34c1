import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';

class LegUpLogo extends StatelessWidget {
  const LegUpLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 52.h,
          width: 104.w,
          child: SvgPicture.asset(AppImages.logoIc)),
      ],
    );
  }
}
