import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/string_constants/app_strings.dart';
import '../utils/string_constants/app_images.dart';
import 'app_gesture_detector.dart';
import 'buttons/primary_button.dart';
import 'dropdown_error_field.dart';
import 'shimmers/drop_down_shimmer.dart';
import 'texts/app_text.dart';

class CustomDialogMultiSelectWidget<T> extends StatefulWidget {
  final String? titleText;
  final String? displayText;
  final String? hintText;
  final Color? hintTextColor;
  final List<T>? selectedValues;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<List<T>>? onChanged;
  final bool isLoading;
  final bool isError;
  final VoidCallback? onErrorTap;
  final bool readOnly;
  final double? maxHeight;
  final Widget? extraWidget;

  const CustomDialogMultiSelectWidget({
    super.key,
    this.titleText,
    required this.displayText,
    this.hintText,
    this.selectedValues,
    required this.items,
    required this.onChanged,
    this.isLoading = false,
    this.isError = false,
    this.onErrorTap,
    this.readOnly = false,
    this.maxHeight,
    this.extraWidget,
    this.hintTextColor,
  });

  @override
  State<CustomDialogMultiSelectWidget<T>> createState() =>
      _CustomDialogMultiSelectWidgetState<T>();
}

class _CustomDialogMultiSelectWidgetState<T>
    extends State<CustomDialogMultiSelectWidget<T>> {
  late ColorScheme colorScheme;
  late TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    colorScheme = theme.colorScheme;
    textTheme = theme.textTheme;

    if (widget.isLoading) {
      return const DropDownShimmer();
    }

    if (widget.isError) {
      return DropdownErrorField(
        onErrorTap: widget.onErrorTap,
        titleText: widget.titleText,
      );
    }

    String displayValue = widget.selectedValues?.isNotEmpty ?? false
        ? widget.items
            .where((item) => widget.selectedValues!.contains(item.value))
            .map((item) => item.child is Text
                ? (item.child as Text).data
                : item.value.toString())
            .join(', ')
        : widget.hintText ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.titleText != null) ...[
          Text14Medium(widget.titleText!),
          8.ph,
        ],
        GestureDetector(
          onTap: widget.readOnly || widget.items.isEmpty
              ? null
              : () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  _showSelectionDialog.call();
                },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: colorScheme.lightGreyDEDEDE,
                width: 1.2,
              ),
              color: widget.readOnly ? colorScheme.lightGreyDEDEDE : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text14Medium(
                    displayValue,
                    color: (widget.selectedValues == null ||
                                widget.selectedValues!.isEmpty) &&
                            !widget.readOnly
                        ? widget.hintTextColor ?? colorScheme.lightGrey8F8F8F
                        : null,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                8.pw,
                Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: widget.readOnly
                      ? colorScheme.lightGreyDEDEDE
                      : colorScheme.blue150045,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showSelectionDialog() {
    List<T> selectedItems = List<T>.from(widget.selectedValues ?? []);
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              backgroundColor: colorScheme.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              insetPadding:
                  EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
              child: Container(
                constraints:
                    BoxConstraints(maxHeight: widget.maxHeight ?? 450.h),
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h)
                    .copyWith(bottom: 38.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text20Bold(
                            widget.displayText ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                          splashRadius: 20,
                          constraints: const BoxConstraints(),
                          padding: EdgeInsets.zero,
                          color: colorScheme.blue150045,
                        ),
                      ],
                    ),
                    Divider(
                        height: 1,
                        thickness: 1,
                        color: colorScheme.lightGreyDEDEDE),
                    8.ph,
                    Flexible(
                      child: ListView.builder(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        itemCount: widget.items.length,
                        itemBuilder: (context, index) {
                          final item = widget.items[index];
                          final isSelected = selectedItems.contains(item.value);

                          return AppGestureDetector(
                            onTap: () {
                              setDialogState(() {
                                if (isSelected) {
                                  selectedItems.remove(item.value);
                                } else {
                                  selectedItems.add(item.value as T);
                                }
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 12.h),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 16.w, vertical: 15.h),
                              decoration: BoxDecoration(
                                color: colorScheme.white,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: isSelected
                                      ? colorScheme.primary
                                      : colorScheme.lightGreyD5D5D5,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(child: item.child),
                                  8.pw,
                                  SvgPicture.asset(
                                    isSelected
                                        ? AppImages.checkBoxChecked
                                        : AppImages.checkBoxEmpty,
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    20.ph,
                    PrimaryButton(
                      buttonText: AppStrings.confirm,
                      onPressed: () {
                        widget.onChanged?.call(selectedItems);
                        Navigator.of(context).pop();
                      },
                    ),
                    if (widget.extraWidget != null) ...[
                      20.ph,
                      widget.extraWidget!,
                    ],
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
