import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Text20SemiBold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;
  const Text20SemiBold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines, this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.labelLarge!.copyWith(
            fontFamily: fontFamily,
              fontSize: fontSize, color: color, fontWeight: fontWeight),
    );
  }
}

class Text20Bold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;
  final double? lineHeight;

  const Text20Bold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.fontFamily,
    this.lineHeight,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.bodyMedium!.copyWith(
            fontFamily: fontFamily,
            fontSize: fontSize ?? 20.sp,
            color: color,
            fontWeight: fontWeight,
            height: lineHeight,
          ),
    );
  }
}

class Text20ExtraBold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;
  final double? lineHeight;
  final FontStyle? fontStyle;

  const Text20ExtraBold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.fontFamily,
    this.lineHeight,
    this.fontStyle
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.bodyLarge!.copyWith(
            fontStyle: fontStyle,
            fontFamily: fontFamily,
            fontSize: fontSize,
            color: color,
            fontWeight: fontWeight,
            letterSpacing: 0,
            height: lineHeight,
          ),
    );
  }
}

class Text16Medium extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  const Text16Medium(this.data,
      {super.key,
      this.style,
      this.color,
      this.fontWeight,
      this.fontSize,
      this.textAlign,
      this.overflow,
      this.maxLines});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.titleMedium!.copyWith(
              fontSize: fontSize, color: color, fontWeight: fontWeight),
    );
  }
}

class Text14Medium extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;
  final double? lineHeight;

  const Text14Medium(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.fontFamily,
    this.lineHeight,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.displayMedium!.copyWith(
            fontSize: fontSize,
            fontFamily: fontFamily,
            color: color,
            fontWeight: fontWeight,
            height: lineHeight,
          ),
    );
  }
}

class Text14SemiBold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;

  const Text14SemiBold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.displaySmall!.copyWith(
            fontSize: fontSize ?? 14.sp,
            fontFamily: fontFamily,
            color: color,
            fontWeight: fontWeight,
          ),
    );
  }
}

class Text14Bold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;
  const Text14Bold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.bodyMedium!.copyWith(
            fontSize: fontSize,
            fontFamily: fontFamily,
            color: color,
            fontWeight: fontWeight,
          ),
    );
  }
}

class Text14ExtraBold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final String? fontFamily;

  const Text14ExtraBold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontWeight,
    this.fontSize,
    this.textAlign,
    this.overflow,
    this.maxLines,
    this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.bodyLarge!.copyWith(
            fontSize: fontSize ?? 14.sp,
            fontFamily: fontFamily,
            color: color,
            fontWeight: fontWeight,
          ),
    );
  }
}

class Text12Medium extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  const Text12Medium(this.data,
      {super.key,
      this.style,
      this.color,
      this.fontSize,
      this.fontWeight,
      this.textAlign,
      this.overflow,
      this.maxLines});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.displayMedium!.copyWith(color: color, fontSize: fontSize ?? 12.sp),
    );
  }
}


class Text12SemiBold extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;

  const Text12SemiBold(
    this.data, {
    super.key,
    this.style,
    this.color,
    this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.overflow,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.displaySmall!.copyWith(
            color: color,
            fontSize: fontSize,
            fontWeight: fontWeight,
          ),
    );
  }
}

class Text12Regular extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  const Text12Regular(this.data,
      {super.key,
      this.style,
      this.color,
      this.fontSize,
      this.fontWeight,
      this.textAlign,
      this.overflow,
      this.maxLines});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Text(
      data,
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      style: style ??
          textTheme.bodySmall!.copyWith(color: color, fontSize: fontSize),
    );
  }
}
