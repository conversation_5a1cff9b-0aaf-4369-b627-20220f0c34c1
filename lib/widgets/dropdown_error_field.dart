import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../core/theme/app_theme.dart';
import '../utils/extensions/empty_space_extn.dart';
import '../utils/string_constants/app_strings.dart';
import 'app_gesture_detector.dart';
import 'texts/app_text.dart';

class DropdownErrorField extends StatelessWidget {
  final String? titleText;
  final void Function()? onErrorTap;

  const DropdownErrorField({super.key, this.titleText, this.onErrorTap});

  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    var colorScheme = theme.colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (titleText != null) ...[
          Text14Medium(titleText ?? ""),
          8.ph,
        ],
        AppGestureDetector(
          onTap: onErrorTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(12, 10, 12, 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: colorScheme.lightGreyDDDDDD,
                width: 1.2,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text14Medium(
                      "Error occured while loading data",
                    ),
                    4.pw,
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Text(
                      AppStrings.refresh,
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    Icon(
                      Icons.refresh,
                      size: 15,
                      color: colorScheme.primary,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
