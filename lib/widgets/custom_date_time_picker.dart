import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import '../core/theme/app_theme.dart';
import '../utils/extensions/empty_space_extn.dart';
import '../utils/string_constants/app_images.dart';
import '../utils/string_constants/app_strings.dart';
import 'app_gesture_detector.dart';
import 'texts/app_text.dart';

class DateTimePickerWidget extends StatefulWidget {
  final DateTime initialDateTime;
  final bool isInstantServiceSelected;
  final ValueChanged<DateTime> onDateTimeChanged;
  final DateTime? minDate;
  final DateTime? maxDate;
  final int minimumDelay;

  const DateTimePickerWidget({
    super.key,
    required this.initialDateTime,
    required this.onDateTimeChanged,
    required this.isInstantServiceSelected,
    this.minDate,
    this.maxDate,
    this.minimumDelay = 0,
  });

  @override
  State<DateTimePickerWidget> createState() => _DateTimePickerWidgetState();
}

class _DateTimePickerWidgetState extends State<DateTimePickerWidget> {
  late DateTime _selectedDateTime;

  late List<String> _hours;
  final List<String> _minutes = ['00', '15', '30', '45'];
  final List<String> _amPm = ['AM', 'PM'];
  late FixedExtentScrollController _hourController;
  late FixedExtentScrollController _minuteController;
  late FixedExtentScrollController _amPmController;
  int _selectedHourIndex = 0;
  int _selectedMinuteIndex = 0;
  int _selectedAmPmIndex = 0;

  final double _itemHeight = 50.0;
  final double _pickerColumnWidth = 70.0;

  static const int _startHour = 10; // 10 AM
  static const int _endHour = 20; // 8 PM

  @override
  void initState() {
    super.initState();
    _selectedDateTime = _clampInitialDateTime(widget.initialDateTime);
    _updateHoursForDate(_selectedDateTime);
    _updateTimePickerStateFromDateTime(_selectedDateTime, initializeControllers: true);

    if (_selectedDateTime != widget.initialDateTime) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onDateTimeChanged(_selectedDateTime);
      });
    }
  }

  @override
  void didUpdateWidget(covariant DateTimePickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if ((widget.initialDateTime != oldWidget.initialDateTime &&
            widget.initialDateTime != _selectedDateTime) ||
        widget.isInstantServiceSelected != oldWidget.isInstantServiceSelected) {
      setState(() {
        _selectedDateTime = _clampInitialDateTime(widget.initialDateTime);
        _updateHoursForDate(_selectedDateTime);
        _updateTimePickerStateFromDateTime(_selectedDateTime, jumpToItem: true);
      });
      if (widget.isInstantServiceSelected != oldWidget.isInstantServiceSelected) {
        widget.onDateTimeChanged(_selectedDateTime);
      }
    }
  }

  @override
  void dispose() {
    _hourController.dispose();
    _minuteController.dispose();
    _amPmController.dispose();
    super.dispose();
  }

  DateTime _clampInitialDateTime(DateTime dt) {
    final now = DateTime.now();
    final delayedTime = now.add(Duration(hours: widget.minimumDelay));
    DateTime effectiveTime = delayedTime;

    // If initialDateTime is after delayedTime, use it
    if (dt.isAfter(delayedTime)) {
      effectiveTime = dt;
    }

    // Round up to next 15-minute interval
    int hour = effectiveTime.hour;
    int minute = ((effectiveTime.minute + 14) ~/ 15) * 15;
    if (minute >= 60) {
      hour += 1;
      minute = 0;
    }
    effectiveTime = DateTime(effectiveTime.year, effectiveTime.month, effectiveTime.day, hour, minute);

    // Calculate days to shift to align with service hours
    final serviceEndToday = DateTime(effectiveTime.year, effectiveTime.month, effectiveTime.day, _endHour, 0);
    int daysToAdd = 0;
    if (effectiveTime.isAfter(serviceEndToday)) {
      daysToAdd = effectiveTime.difference(serviceEndToday).inHours ~/ 24 + 1;
    } else if (effectiveTime.hour < _startHour) {
      daysToAdd = 0; // Stay on same day, adjust to 10 AM
    }

    // Adjust to service hours
    if (daysToAdd > 0) {
      return DateTime(effectiveTime.year, effectiveTime.month, effectiveTime.day + daysToAdd, _startHour, 0);
    } else {
      if (effectiveTime.hour < _startHour) {
        return DateTime(effectiveTime.year, effectiveTime.month, effectiveTime.day, _startHour, 0);
      }
      return effectiveTime;
    }
  }

  DateTime _getEffectiveMinDateTime(DateTime now) {
    final delayedTime = now.add(Duration(hours: widget.minimumDelay));
    final serviceEndToday = DateTime( delayedTime.year, delayedTime.month, delayedTime.day, _endHour, 0);
    int daysToAdd = delayedTime.isAfter(serviceEndToday)
        ? delayedTime.difference(serviceEndToday).inHours ~/ 24 + 1
        : 0;
    return daysToAdd > 0
        ? DateTime(delayedTime.year, delayedTime.month, delayedTime.day + daysToAdd, _startHour, 0)
        : delayedTime;
  }

  void _updateHoursForDate(DateTime date) {
    final now = DateTime.now();
    final today = DateUtils.dateOnly(now);
    final delayedTime = now.add(Duration(hours: widget.minimumDelay));
    if (DateUtils.isSameDay(date, today) &&
        delayedTime.isBefore(DateTime(today.year, today.month, today.day, _endHour, 0))) {
      int startHour = delayedTime.hour > _startHour ? delayedTime.hour : _startHour;
      if (startHour > _endHour) {
        startHour = _startHour; // Will shift to next day elsewhere
      }
      _hours = List.generate(_endHour - startHour + 1, (i) {
        int hour12 = (startHour + i) > 12 ? (startHour + i) - 12 : (startHour + i);
        if (hour12 == 0) hour12 = 12;
        return hour12.toString().padLeft(2, '0');
      });
    } else {
      _hours = List.generate(_endHour - _startHour + 1, (i) {
        int hour12 = (_startHour + i) > 12 ? (_startHour + i) - 12 : (_startHour + i);
        if (hour12 == 0) hour12 = 12;
        return hour12.toString().padLeft(2, '0');
      });
    }
  }

  void _updateTimePickerStateFromDateTime(DateTime dt, {bool initializeControllers = false, bool jumpToItem = false}) {
    int hour24 = dt.hour;
    int hour12 = hour24 > 12 ? hour24 - 12 : (hour24 == 0 ? 12 : hour24);
    int minute = ((dt.minute + 14) ~/ 15) * 15;
    if (minute >= 60) {
      hour24 += 1;
      minute = 0;
      hour12 = hour24 > 12 ? hour24 - 12 : (hour24 == 0 ? 12 : hour24);
    }
    int periodIndex = (hour24 < 12) ? 0 : 1;

    _selectedHourIndex = _hours.indexOf(hour12.toString().padLeft(2, '0'));
    if (_selectedHourIndex == -1) _selectedHourIndex = 0;
    _selectedMinuteIndex = _minutes.indexOf(minute.toString().padLeft(2, '0'));
    if (_selectedMinuteIndex == -1) _selectedMinuteIndex = 0;
    _selectedAmPmIndex = periodIndex;

    if (initializeControllers) {
      _hourController = FixedExtentScrollController(initialItem: _selectedHourIndex);
      _minuteController = FixedExtentScrollController(initialItem: _selectedMinuteIndex);
      _amPmController = FixedExtentScrollController(initialItem: _selectedAmPmIndex);
    } else if (jumpToItem && _hourController.hasClients && _minuteController.hasClients && _amPmController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _hourController.jumpToItem(_selectedHourIndex);
          _minuteController.jumpToItem(_selectedMinuteIndex);
          _amPmController.jumpToItem(_selectedAmPmIndex);
        }
      });
    }
  }

  DateTime _combineDateAndTime() {
    int selectedHour12 = int.parse(_hours[_selectedHourIndex]);
    int selectedMinuteValue = int.parse(_minutes[_selectedMinuteIndex]);
    bool isAm = (_selectedAmPmIndex == 0);
    int selectedHour24 = isAm
        ? (selectedHour12 == 12 ? 0 : selectedHour12)
        : (selectedHour12 == 12 ? 12 : selectedHour12 + 12);

    return DateTime(
      _selectedDateTime.year,
      _selectedDateTime.month,
      _selectedDateTime.day,
      selectedHour24,
      selectedMinuteValue,
    );
  }

  void _handleDateChange(DateTime newDate) {
    setState(() {
      _selectedDateTime = DateTime(
        newDate.year,
        newDate.month,
        newDate.day,
        _selectedDateTime.hour,
        _selectedDateTime.minute,
      );
      _updateHoursForDate(_selectedDateTime);
      _updateTimePickerStateFromDateTime(_selectedDateTime, jumpToItem: true);
    });
    widget.onDateTimeChanged(_selectedDateTime);
  }

  void _handleTimeChange() {
    final newDateTime = _combineDateAndTime();
    if (newDateTime != _selectedDateTime) {
      setState(() {
        _selectedDateTime = newDateTime;
      });
      widget.onDateTimeChanged(_selectedDateTime);
    }
  }

  void _handleHourChange(int newHourIndex) {
    final oldHour12 = int.parse(_hours[_selectedHourIndex]);
    _selectedHourIndex = newHourIndex;
    final newHour12 = int.parse(_hours[_selectedHourIndex]);
    final now = DateTime.now();
    final today = DateUtils.dateOnly(now);
    final minTimeToday = now.add(Duration(hours: widget.minimumDelay));
    final minHourToday = minTimeToday.hour > _startHour ? minTimeToday.hour : _startHour;

    if (newHour12 < oldHour12 && newHour12 <= 8 && _selectedAmPmIndex == 0) {
      if (!(DateUtils.isSameDay(_selectedDateTime, today) && minHourToday > 12)) {
        _selectedAmPmIndex = 1;
        if (_amPmController.hasClients) {
          _amPmController.jumpToItem(1);
        }
      }
    } else if (newHour12 >= 10 && newHour12 <= 11 && _selectedAmPmIndex == 1) {
      if (!(DateUtils.isSameDay(_selectedDateTime, today) && minHourToday > 11)) {
        _selectedAmPmIndex = 0;
        if (_amPmController.hasClients) {
          _amPmController.jumpToItem(0);
        }
      }
    }

    _handleTimeChange();
  }

  Widget _buildDatePickerRow(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final now = DateTime.now();
    final selectedDateOnly = DateUtils.dateOnly(_selectedDateTime);
    final effectiveMinDate = _getEffectiveMinDateTime(now);
    final minDateOnly = DateUtils.dateOnly(effectiveMinDate);

    bool canGoBack = selectedDateOnly.isAfter(minDateOnly);
    bool canGoForward = widget.maxDate == null || selectedDateOnly.isBefore(DateUtils.dateOnly(widget.maxDate!));

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.greenD9F0E8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppGestureDetector(
            onTap: widget.isInstantServiceSelected ? null : canGoBack ? _onPreviousDateTap : null,
            child: Transform.rotate(
              angle: 180 * math.pi / 180,
              child: Container(
                width: 24.w,
                height: 24.h,
                padding: EdgeInsets.all(4.w),
                child: SvgPicture.asset(
                  AppImages.arrowRightIcon,
                  colorFilter: !canGoBack ? ColorFilter.mode(
                    Colors.grey.withOpacity(0.5),
                    BlendMode.srcIn,
                  ) : null,
                ),
              ),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(AppImages.calendarIcon),
              4.pw,
              Text14SemiBold(
                DateFormat('EEE, d MMM').format(_selectedDateTime),
                fontSize: 16.sp,
              ),
            ],
          ),
          AppGestureDetector(
            onTap: widget.isInstantServiceSelected ? null : canGoForward ? _onNextDateTap : null,
            child: Container(
              width: 24.w,
              height: 24.h,
              padding: EdgeInsets.all(4.w),
              child: SvgPicture.asset(
                AppImages.arrowRightIcon,
                color: widget.isInstantServiceSelected ? Colors.grey.withOpacity(0.5) : !canGoForward ? Colors.grey.withOpacity(0.5) : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onNextDateTap() {
    final nextDay = _selectedDateTime.add(const Duration(days: 1));
    _handleDateChange(nextDay);
  }

  void _onPreviousDateTap() {
    final previousDay = _selectedDateTime.subtract(const Duration(days: 1));
    _handleDateChange(previousDay);
  }

  Widget _buildTimePicker(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      decoration: BoxDecoration(
        color: colorScheme.whiteF7F7F7,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          SelectionIndicator(itemHeight: _itemHeight),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              FlatWheelPicker(
                items: _hours,
                initialItem: _selectedHourIndex,
                itemHeight: _itemHeight,
                width: _pickerColumnWidth,
                controller: _hourController,
                looping: false,
                onSelectedItemChanged: _handleHourChange,
              ),
              FlatWheelPicker(
                items: _minutes,
                initialItem: _selectedMinuteIndex,
                itemHeight: _itemHeight,
                width: _pickerColumnWidth,
                controller: _minuteController,
                looping: true,
                onSelectedItemChanged: (index) {
                  _selectedMinuteIndex = index;
                  _handleTimeChange();
                },
              ),
              FlatWheelPicker(
                items: _amPm,
                initialItem: _selectedAmPmIndex,
                itemHeight: _itemHeight,
                width: _pickerColumnWidth,
                controller: _amPmController,
                looping: false,
                onSelectedItemChanged: (index) {
                  _selectedAmPmIndex = index;
                  _handleTimeChange();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDatePickerRow(context),
        16.ph,
        Text14SemiBold(
          AppStrings.startTimeOfService,
          fontSize: 16.sp,
        ),
        16.ph,
        _buildTimePicker(context),
      ],
    );
  }
}

class FlatWheelPicker extends StatefulWidget {
  final List<String> items;
  final int initialItem;
  final double itemHeight;
  final double width;
  final FixedExtentScrollController controller;
  final ValueChanged<int> onSelectedItemChanged;
  final TextStyle? textStyle;
  final TextStyle? selectedTextStyle;
  final bool looping;

  const FlatWheelPicker({
    super.key,
    required this.items,
    required this.initialItem,
    required this.itemHeight,
    required this.width,
    required this.controller,
    required this.onSelectedItemChanged,
    this.textStyle,
    this.selectedTextStyle,
    this.looping = false,
  });

  @override
  State<FlatWheelPicker> createState() => _FlatWheelPickerState();
}

class _FlatWheelPickerState extends State<FlatWheelPicker> {
  int _currentRawIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentRawIndex = widget.initialItem;
    widget.controller.addListener(_updateVisualStyle);
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateVisualStyle());
  }

  @override
  void didUpdateWidget(covariant FlatWheelPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller.removeListener(_updateVisualStyle);
      widget.controller.addListener(_updateVisualStyle);
    }
    if (widget.initialItem != oldWidget.initialItem && widget.controller.hasClients) {
      int currentEffectiveIndex = widget.looping
          ? (_currentRawIndex % widget.items.length)
          : _currentRawIndex.clamp(0, widget.items.length - 1);

      if (currentEffectiveIndex != widget.initialItem) {
        _currentRawIndex = widget.initialItem;

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && widget.controller.hasClients) {
            widget.controller.jumpToItem(widget.initialItem);
            _updateVisualStyle();
          }
        });
      } else {
        // If initialItem matches the current effective index, ensure raw index is consistent
        // (especially important if controller listener hasn't fired yet after a programmatic change)
        if (widget.looping && _currentRawIndex % widget.items.length != widget.initialItem) {
          // Less common case, but might happen. Just update raw index visually.
          setStateIfMounted(() {
            _currentRawIndex = widget.initialItem;
          });
        } else if (!widget.looping && _currentRawIndex != widget.initialItem) {
          setStateIfMounted(() {
            _currentRawIndex = widget.initialItem;
          });
        }
      }
    } else if (!widget.controller.hasClients) {
      // If controller has no clients (e.g. during initial build phases), ensure raw index matches
      _currentRawIndex = widget.initialItem;
    }
    // Always ensure visual style is up-to-date after potential changes
    _updateVisualStyle();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_updateVisualStyle);
    super.dispose();
  }

  void _updateVisualStyle() {
    if (mounted && widget.controller.hasClients && widget.controller.position.hasContentDimensions) {
      // Use selectedItem as it's often more reliable than index during scrolls/jumps
      final currentItemFromController = widget.controller.selectedItem;

      // For non-looping, ensure the index reported by controller is valid
      if (!widget.looping && (currentItemFromController < 0 || currentItemFromController >= widget.items.length)) {
        // Can happen briefly during jumps or edge scrolls, ignore invalid states
        return;
      }

      if (_currentRawIndex != currentItemFromController) {
        setStateIfMounted(() {
          _currentRawIndex = currentItemFromController;
        });
      }
    } else if (mounted && !widget.controller.hasClients) {
      // If controller isn't ready, ensure visual state matches initialItem
      if (_currentRawIndex != widget.initialItem) {
        setStateIfMounted(() {
          _currentRawIndex = widget.initialItem;
        });
      }
    }
  }

  // Helper to prevent setState calls if disposed
  void setStateIfMounted(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return SizedBox(
      height: widget.itemHeight * 3,
      width: widget.width,
      child: ListWheelScrollView.useDelegate(
        controller: widget.controller,
        itemExtent: widget.itemHeight,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          // index is the raw index from the delegate
          HapticFeedback.lightImpact();

          // Update the internal raw index state for styling FIRST
          setStateIfMounted(() {
            _currentRawIndex = index;
          });

          // Notify the parent widget with the *effective* index (0 to length-1)
          final effectiveIndex = widget.looping ? (index % widget.items.length) : index.clamp(0, widget.items.length -1);

          // Check if effective index is valid before calling back
           if (effectiveIndex >= 0 && effectiveIndex < widget.items.length) {
              widget.onSelectedItemChanged(effectiveIndex);
           }
        },
        // Visual properties
        useMagnifier: false,
        magnification: 1.0,
        offAxisFraction: 0.0, // Keep items aligned vertically
        diameterRatio: 10000, // Makes the wheel appear flat
        squeeze: 1.0, // No squeeze effect

        childDelegate: widget.looping
            ? ListWheelChildLoopingListDelegate(
                children: List<Widget>.generate(widget.items.length, (index) {
                  // Calculate effective index for the current raw index *being processed*
                  final effectiveCurrentIndex = _currentRawIndex % widget.items.length;
                  final isSelected = (index == effectiveCurrentIndex);
                  return Center(
                    child: Text14Bold(
                      widget.items[index],
                      fontSize: 16.sp,
                      color: isSelected ? null : colorScheme.lightGreyD0D8DE,
                    ),
                  );
                }),
              )
            : ListWheelChildListDelegate(
                // Non-looping delegate
                children: List<Widget>.generate(widget.items.length, (index) {
                  final safeRawIndex = _currentRawIndex.clamp(0, widget.items.length - 1);
                  final isSelected = (index == safeRawIndex);
                  return Center(
                    child: Text14Bold(
                      widget.items[index],
                      fontSize: 16.sp,
                      color: isSelected ? null : colorScheme.lightGreyD0D8DE,
                    ),
                  );
                }),
              ),
      ),
    );
  }
}

class SelectionIndicator extends StatelessWidget {
  final double itemHeight;
  final double lineWidth;

  const SelectionIndicator({
    super.key,
    required this.itemHeight,
    this.lineWidth = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return IgnorePointer(
      child: Center(
        child: SizedBox(
          height: itemHeight,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Divider(
                color: colorScheme.greenD9F0E8,
                height: lineWidth,
                thickness: lineWidth,
              ),
              Divider(
                color: colorScheme.greenD9F0E8,
                height: lineWidth,
                thickness: lineWidth,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
