import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/widgets/custom_cache_image.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerWidget extends StatefulWidget {
  final String? videoUrl;
  final String? thumbnailUrl;
  final bool autoPlay;

  const VideoPlayerWidget({
    super.key,
    required this.videoUrl,
    required this.thumbnailUrl,
    this.autoPlay = false,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl ?? ''));

    await _videoPlayerController.initialize();

    _chewieController = ChewieController(
      videoPlayerController: _videoPlayerController,
      autoPlay: widget.autoPlay,
      autoInitialize: true,
      looping: false,
      allowFullScreen: false,
      showOptions: false,
      aspectRatio: _videoPlayerController.value.aspectRatio,
      cupertinoProgressColors: ChewieProgressColors(
        playedColor: const Color(0xff2F6DB5),
        bufferedColor: const Color(0xff2F6DB5).withOpacity(0.2),
      ),
      materialProgressColors: ChewieProgressColors(
        playedColor: const Color(0xff2F6DB5),
        bufferedColor: const Color(0xff2F6DB5).withOpacity(0.2),
      ),
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: _chewieController != null && _chewieController!.videoPlayerController.value.isInitialized
          ? AspectRatio(
              aspectRatio: _chewieController!.videoPlayerController.value.aspectRatio,
              child: Chewie(controller: _chewieController!),
            )
          : AspectRatio(
              aspectRatio: 16 / 9,
              child: Center(
                child: Stack(
                  children: [
                    widget.thumbnailUrl != null && widget.thumbnailUrl!.isNotEmpty
                        ? CustomCacheImage(
                            imageUrl: widget.thumbnailUrl ?? '',
                            width: double.infinity,
                            height: 200.h,
                            fit: BoxFit.cover,
                          )
                        : SizedBox.shrink(),
                    Center(child: CircularProgressIndicator(color: colorScheme.primary)),
                  ],
                ),
              ),
            ),
    );
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }
}