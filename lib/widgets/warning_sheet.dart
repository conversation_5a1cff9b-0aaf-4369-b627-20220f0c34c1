import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../utils/string_constants/app_strings.dart';
import 'buttons/custom_outlined_button.dart';
import 'buttons/primary_button.dart';

class WarningSheet extends StatelessWidget {
  final String title;
  final String message;
  final String? cancelButtonText;
  final String? okButtonText;
  final void Function()? onCancel;
  final void Function()? onOk;
  final Color? cancelButtonColor;
  final Color? okButtonColor;
  const WarningSheet(
      {super.key,
      required this.title,
      required this.message,
      this.cancelButtonText,
      this.okButtonText,
      this.onCancel,
      this.onOk,
      this.cancelButtonColor,
      this.okButtonColor});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            alignment: Alignment.center,
            child: Container(
              height: 4.h,
              width: 36.w,
              decoration: BoxDecoration(
                color: colorScheme.lightGreyD0D8DE,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
          16.ph,
          Text12Medium(
            title,
            fontSize: 16.sp,
          ),
          16.ph,
          Text12Regular(
            message,
            color: colorScheme.subTextGrey6A6A6A,
          ),
          16.ph,
          Row(
            children: [
              Expanded(
                child: CustomOutlinedButton(
                    buttonText: cancelButtonText ?? AppStrings.cancel,
                    onPressed: onCancel ?? () => Navigator.pop(context),
                    textColor: cancelButtonColor),
              ),
              12.pw,
              Expanded(
                child: PrimaryButton(
                    backgroundColor: okButtonColor,
                    buttonText: okButtonText ?? AppStrings.ok,
                    onPressed: onOk),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
