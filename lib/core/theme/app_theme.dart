import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTheme {
  static ThemeData lightThemeData =
      getThemeData(_lightColorScheme);

  static ThemeData getThemeData(ColorScheme colorScheme) {
    return ThemeData(
      scaffoldBackgroundColor: colorScheme.white,
      brightness: colorScheme.brightness,
      hoverColor: colorScheme.primary.withOpacity(0.2),
      splashColor: colorScheme.primary.withOpacity(0.4),
      colorScheme: colorScheme,
      primaryColor: colorScheme.primary,
      textTheme: _textTheme(colorScheme),
      radioTheme: RadioThemeData(
        fillColor: WidgetStatePropertyAll(
          colorScheme.primary,
        ),
      ),
      
// datePickerTheme: DatePickerThemeData(
//         headerForegroundColor: colorScheme.primaryGrey,
//         backgroundColor: colorScheme.white,
//         yearBackgroundColor: WidgetStatePropertyAll(
//           colorScheme.white,
//         ),
//         headerHeadlineStyle: TextStyle(
//           fontSize: 20.sp,
//           fontWeight: _bold,
//           fontFamily: 'NotoSans-Bold',
//           color: colorScheme.primaryGrey,
//         ),
//         // Weekday Text Style
//         weekdayStyle: TextStyle(
//           fontSize: 18.sp,
//           fontWeight: FontWeight.w600,
//           fontFamily: 'NotoSans-Medium',
//           color: colorScheme.primaryGrey,
//         ),
//         // Date Text Style
//         dayStyle: TextStyle(
//           fontSize: 18.sp,
//           fontWeight: FontWeight.w500,
//           fontFamily: 'NotoSans-Medium',
//           color: colorScheme.primaryGrey,
//         ),
//         yearStyle: TextStyle(color: colorScheme.primaryGrey),
//         yearForegroundColor: WidgetStatePropertyAll(colorScheme.primaryGrey),
//         dayForegroundColor: WidgetStatePropertyAll(colorScheme.primaryGrey),

//         // Selected Date Style
//         todayForegroundColor: WidgetStatePropertyAll(
//           colorScheme.white,
//         ),
//         todayBackgroundColor: WidgetStatePropertyAll(
//           colorScheme.hyperlinkBlueColor,
//         ),
//         todayBorder:
//             BorderSide(color: colorScheme.hyperlinkBlueColor, width: 2),

//       ),

      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return states.contains(WidgetState.hovered)
              ? colorScheme.grey575757.withOpacity(0.1)
              : Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all<Color>(colorScheme.white),
        side: WidgetStateBorderSide.resolveWith(
          (states) => BorderSide(
            color: states.contains(WidgetState.selected)
                ? colorScheme.primary
                : colorScheme.grey575757,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.r),
        ),
      ),
      iconTheme: IconThemeData(color: colorScheme.primaryContainer),
      highlightColor: Colors.transparent,
      bottomSheetTheme:
          const BottomSheetThemeData(backgroundColor: Colors.transparent),
      dividerColor: Colors.transparent,
    );
  }

  static const ColorScheme _lightColorScheme = ColorScheme(
    primary: Color(0xff2F6DB5),
    onPrimary: Colors.white,
    secondary: Color(0xFF1243B5),
    onSecondary: Color(0xff3A393C),
    surface: Color(0xffF2F2F2),
    onSurface: Color(0xff3A393C),
    error: Color(0xFFD80000),
    onError: Colors.white,
    brightness: Brightness.light,
  );

  static const _regular = FontWeight.w400;
  static const _medium = FontWeight.w500;
  static const _semiBold = FontWeight.w600;
  static const _bold = FontWeight.w700;
  static const _extraBold = FontWeight.w800;

  static TextTheme _textTheme(ColorScheme colorScheme) {
    return TextTheme(
      labelLarge: TextStyle(
        fontSize: 20.sp,
        fontWeight: _semiBold,
        fontFamily: FontFamily.nunitoSansSemiBold,
        color: colorScheme.blue150045,
      ),
      displayMedium: TextStyle(
        fontSize: 14.sp,
        fontWeight: _medium,
        fontFamily: FontFamily.nunitoSansMedium,
        color: colorScheme.blue150045,
      ),
      titleMedium: TextStyle(
        fontSize: 16.sp,
        fontWeight: _medium,
        fontFamily: FontFamily.nunitoSansMedium,
        color: colorScheme.blue150045,
      ),
      titleSmall: TextStyle(
        fontSize: 12.sp,
        fontWeight: _regular,
        fontFamily: FontFamily.nunitoSansRegular,
        color: colorScheme.blue150045,
      ),
      displaySmall: TextStyle(
        fontSize: 12.sp,
        fontWeight: _semiBold,
        fontFamily: FontFamily.nunitoSansSemiBold,
        color: colorScheme.blue150045,
      ),
      bodySmall: TextStyle(
        fontSize: 12.sp,
        fontWeight: _regular,
        fontFamily: FontFamily.nunitoSansRegular,
        color: colorScheme.blue150045,
      ),
      bodyMedium: TextStyle(
        fontSize: 14.sp,
        fontWeight: _bold,
        fontFamily: FontFamily.nunitoSansBold,
        color: colorScheme.blue150045,
      ),
      bodyLarge: TextStyle(
        fontSize: 20.sp,
        fontWeight: _extraBold,
        fontFamily: FontFamily.nunitoSansExtraBold,
        color: colorScheme.blue150045,
      ),
    );
  }
}

extension AppThemeColors on ColorScheme {
  Color get deepTeal19455E => const Color(0xff19455E);
  Color get lavenderGreyD4D4E8 => const Color(0xffD4D4E8);
  Color get blue150045 => const Color(0xFF150045);
  Color get subTextGrey6A6A6A => const Color(0xff6A6A6A);
  Color get warningEBA92A => const Color(0xffEBA92A);
  Color get success3FB68E => const Color(0xFF3FB68E);
  Color get greenB5E1B1 => const Color(0xffB5E1B1);
  Color get greenF0FFF0 => const Color(0xffF0FFF0);
  Color get redEFD0C7 => const Color(0xffEFD0C7);
  Color get redFBEFEB => const Color(0xffFBEFEB);
  Color get white => const Color(0xffffffff);
  Color get lightGreyDDDDDD => const Color(0xffDDDDDD);
  Color get lightGrey8F8F8F => const Color(0xff8F8F8F);
  Color get lightGreyD0D8DE => const Color(0xffD0D8DE);
  Color get lightGreyF5F5F5 => const Color(0xffF5F5F5);
  // Color get disablePrimaryB7CAEB => const Color(0xffB7CAEB);
  Color get lightBrownFBF4EF => const Color(0xffFBF4EF);
  Color get lightBrownF7ECE3 => const Color(0xffF7ECE3);
  Color get lightBrownF6E9DF => const Color(0xffF6E9DF);
  Color get blue140042 => const Color(0xFF140042);
  Color get orangeF0AB75 => const Color(0xFFF0AB75);
  Color get greenE9F7F2 => const Color(0xFFE9F7F2);
  Color get green379F7D => const Color(0xFF379F7D);
  Color get whiteF7F7F7 => const Color(0xFFF7F7F7);
  Color get green329071 => const Color(0xFF329071);
  Color get brownE8CAB0 => const Color(0xFFE8CAB0);
  Color get blu1A3D65 => const Color(0xFF1A3D65);
  Color get greyEDEDED => const Color(0xFFEDEDED);
  Color get grey787878 => const Color(0xFF787878);
  Color get greenD9F0E8 => const Color(0xFFD9F0E8);

  Color get shimmerColor => Colors.white;
  Color get shimmerBaseColor => Colors.grey[300]!;
  Color get shimmerHighlightColor => Colors.grey[100]!;
  Color get lightBrownFCEEE3 => const Color(0xffFCEEE3);
  Color get lightBrownF7D2B5 => const Color(0xffF7D2B5);
  Color get redD60000 => const Color(0xFFD60000);
  Color get green595959 => const Color(0xFF595959);

  Color get redFCE3E3 => const Color(0xFFFCE3E3);
  Color get redEF8F8F => const Color(0xFFEF8F8F);
  Color get orangeF5C7A3 => const Color(0xFFF5C7A3);
  Color get orangeFAE3D1 => const Color(0xFFFAE3D1);
  Color get greenE8F7F2 => const Color(0xFFE8F7F2);
  Color get lightGreyDEDEDE => const Color(0xFFDEDEDE);
  Color get lightGreenB6E2D3 => const Color(0xFFB6E2D3);
  Color get lightGreyD5D5D5 => const Color(0xFFD5D5D5);
  Color get grey575757 => const Color(0xFF575757);
  Color get blueEFF4FB => const Color(0xFFEFF4FB);
  Color get lightGreyD5D7DA => const Color(0xFFD5D7DA);
  Color get lightGrey6B6B6B => const Color(0xFF6B6B6B);
  Color get redFFE0E0 => const Color(0xFFFFE0E0);
  Color get lightGrey61738A => const Color(0xFF61738A);
  Color get orangeF9E0CC => const Color(0xFFF9E0CC);
  Color get orangeEB8F47 => const Color(0xFFEB8F47);
  Color get orangeFCEEE3 => const Color(0xFFFCEEE3);
  Color get blueAECAEA => const Color(0xFFAECAEA);
  Color get blueE7EFF9 => const Color(0xFFE7EFF9);
  Color get lightGreenF0FAF6 => const Color(0xFFF0FAF6);
}

class FontFamily {
  static const String nunitoSansRegular = "NunitoSans-Regular";
  static const String nunitoSansMedium = "NunitoSans-Medium";
  static const String nunitoSansSemiBold = "NunitoSans-SemiBold";
  static const String nunitoSansBold = "NunitoSans-Bold";
  static const String nunitoSansExtraBold = "NunitoSans-ExtraBold";
  static const String nunitoSansBlack = "NunitoSans-Black";
  static const String playwriteIE = 'PlaywriteIE';
  static const String epilogueExtraBold = 'Epilogue-ExtraBold';
  static const String nunitoSansMediumItalic = 'NunitoSans-Medium-Italic';
  static const String nunitoSansExtraBoldItalic = 'NunitoSans-ExtraBold-Italic';
}
