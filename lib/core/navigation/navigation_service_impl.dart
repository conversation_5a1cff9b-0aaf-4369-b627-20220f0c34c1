import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import '../api/api_service.dart';
import '../route_observer/route_observer_impl.dart';
import '../routes/app_routes.dart';
import '../services/hive/hive_keys.dart';
import '../services/hive/hive_storage_helper.dart';
import 'navigation_service.dart';

class NavigationServiceImpl extends NavigationService {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  static NavigationServiceImpl? _instance;

  static NavigationService? getInstance() {
    _instance ??= NavigationServiceImpl._();
    return _instance;
  }

  NavigationServiceImpl._();

  @override
  GlobalKey<NavigatorState> getGlobalKey() {
    return _navigatorKey;
  }

  @override
  Future<void> doLogout() async {
    Navigator.of(_navigatorKey.currentContext!).pushNamedAndRemoveUntil(
      AppRoute.login,
      (Route<dynamic> route) => false,
    );
    await HiveStorageHelper.clearBox(HiveBoxName.user);
    ApiService.instance.clearPartnerGatewayToken();
    await FirebaseMessaging.instance.deleteToken();
  }

  @override
  void doNavigation() {}

  @override
  Future<void> navigateTo(String routeName, {Object? arguments}) async {
    final routeObserver = MyRouteObserver.getInstance();
    final currentRoute = routeObserver.stack.last;
    if (currentRoute.settings.name == routeName && currentRoute.settings.arguments == arguments) return;
    await Navigator.pushNamed(_navigatorKey.currentContext!, routeName, arguments: arguments);
  }
}
