import 'dart:developer';

import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfcard.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfcardpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfupi.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfupipayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfwebcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentgateway/cfpaymentgatewayservice.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfsession/cfsession.dart';
import 'package:flutter_cashfree_pg_sdk/api/cftheme/cftheme.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfupi/cfupiutils.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfexceptions.dart';

import '../../features/service_booking/data/model/upi_app_data.dart';
import '../config/app_config.dart';

class CashfreeService {
  static final CashfreeService _instance = CashfreeService._internal();
  CashfreeService._internal();

  static bool _isInitialized = false;

  late void Function(String) _onVerifyPayment;
  late void Function(CFErrorResponse, String?) _onError;

  factory CashfreeService({
    required void Function(String) onVerifyPayment,
    required void Function(CFErrorResponse, String?) onError,
  }) {
    if (!_isInitialized) {
      _instance._initializeCashfreeService(onVerifyPayment, onError);
      _isInitialized = true;
    }
    return _instance;
  }

  final cfPaymentGatewayService = CFPaymentGatewayService();
  final CFEnvironment cashfreeEnvironment = AppConfig.getInstance().cashfreeEnv;

  String selectedId = "";
  final List<UpiAppData> _upiAppsList = [];
  List<UpiAppData> get upiAppsList => List.unmodifiable(_upiAppsList);

  void _initializeCashfreeService(
    void Function(String) onVerifyPayment,
    void Function(CFErrorResponse, String?) onError,
  ) {
    _onVerifyPayment = onVerifyPayment;
    _onError = onError;
    cfPaymentGatewayService.setCallback(_onVerifyPayment, _onError);

    CFUPIUtils().getUPIApps().then((value) {
      for(var i = 0; i < (value?.length ?? 0); i++) {
        _upiAppsList.add(
          UpiAppData(
            iconBytes: value?[i]["base64Icon"] as String? ?? '',
            displayName: value?[i]["displayName"] as String? ?? '',
            id: value?[i]["id"] as String? ?? '',
          )
        );
      }
    });
  }

  CFSession? createSession({
    required String orderId,
    required String paymentSessionId,
  }) {
    try {
      var session = CFSessionBuilder().setEnvironment(cashfreeEnvironment).setOrderId(orderId).setPaymentSessionId(paymentSessionId).build();
      return session;
    } on CFException catch (e) {
      log(e.message);
    }
    return null;
  }

  Future<void> webCheckout({
    required String orderId,
    required String paymentSessionId,
  }) async {
    try {
      var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
      var theme = CFThemeBuilder()
          .setNavigationBarBackgroundColorColor("#ffffff")
          .setNavigationBarTextColor("#ffffff")
          .setNavigationBarBackgroundColorColor('#2f6db5')
          .setButtonBackgroundColor('#2f6db5')
          .build();
      var cfWebCheckout = CFWebCheckoutPaymentBuilder().setSession(session!).setTheme(theme).build();
      cfPaymentGatewayService.doPayment(cfWebCheckout);
    } on CFException catch (e) {
      log(e.message);
    }
  }

  Future<void> upiIntentPayTapped({required String appUpiId, required orderId, required String paymentSessionId}) async {
    try {
      var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
      var upi = CFUPIBuilder().setChannel(CFUPIChannel.INTENT).setUPIID(appUpiId).build();
      var upiPayment = CFUPIPaymentBuilder().setSession(session!).setUPI(upi).build();
      cfPaymentGatewayService.doPayment(upiPayment);
    } on CFException catch (e) {
      log(e.message);
    }
  }

  Future<void> cardPay({required String orderId, required String paymentSessionId, required String instrumentId}) async {
    try {
      var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
      var card = CFCardBuilder().setInstrumentId(instrumentId).build();
      var cardPayment = CFCardPaymentBuilder().setSession(session!).setCard(card).build();
      cfPaymentGatewayService.doPayment(cardPayment);
    } on CFException catch (e) {
      log(e.message);
    }
  }

  // Future<void> upiCollectPay({required String upiId, required orderId, required String paymentSessionId}) async {
  //   try {
  //     var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
  //     var upi = CFUPIBuilder().setChannel(CFUPIChannel.COLLECT).setUPIID(upiId).build();
  //     var upiPayment = CFUPIPaymentBuilder().setSession(session!).setUPI(upi).build();
  //     cfPaymentGatewayService.doPayment(upiPayment);
  //   } on CFException catch (e) {
  //     log(e.message);
  //   }
  // }

  // Future<void> netBankingPay({required orderId, required String paymentSessionId}) async {
  //   try {
  //     var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
  //     var netbanking = CFNetbankingBuilder().setChannel("link").setBankCode(3003).build();
  //     var netbankingPayment = CFNetbankingPaymentBuilder().setSession(session!).setNetbanking(netbanking).build();
  //     cfPaymentGatewayService.doPayment(netbankingPayment);
  //   } on CFException catch (e) {
  //     log(e.message);
  //   }
  // }

  // Future<void> upiIntentPay({required orderId, required String paymentSessionId}) async {
  //   try {
  //     var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
  //     var upi = CFUPIBuilder().setChannel(CFUPIChannel.INTENT_WITH_UI).build();
  //     var upiPayment = CFUPIPaymentBuilder().setSession(session!).setUPI(upi).build();
  //     cfPaymentGatewayService.doPayment(upiPayment);
  //   } on CFException catch (e) {
  //     log(e.message);
  //   }
  // }

  // Future<void> pay({required orderId, required String paymentSessionId}) async {
  //   try {
  //     var session = createSession(orderId: orderId, paymentSessionId: paymentSessionId);
  //     List<CFPaymentModes> components = <CFPaymentModes>[];
  //     components.add(CFPaymentModes.UPI);
  //     var paymentComponent = CFPaymentComponentBuilder().setComponents(components).build();

  //     var theme = CFThemeBuilder().setNavigationBarBackgroundColorColor("#FF0000").setPrimaryFont("Menlo").setSecondaryFont("Futura").build();

  //     var cfDropCheckoutPayment = CFDropCheckoutPaymentBuilder().setSession(session!).setPaymentComponent(paymentComponent).setTheme(theme).build();

  //     cfPaymentGatewayService.doPayment(cfDropCheckoutPayment);
  //   } on CFException catch (e) {
  //     log(e.message);
  //   }
  // }
}
