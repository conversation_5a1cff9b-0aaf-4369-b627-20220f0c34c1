import '../../features/authentication/data/authentication_req_model.dart';
import '../../features/booking_type/data/instant_service_available_request_model.dart';
import '../../features/common/feedback/data/submit_feedback_req_model.dart';
import '../../features/my_addresses/data/save_address_req_model.dart';
import '../../features/my_bookings/data/get_bookings_filters_req_model.dart';
import '../../features/profile/data/enums/policy_type.dart';
import '../../features/profile/data/model/user_data_model.dart';
import '../../features/service_booking/data/model/booking_request_model.dart';
import '../../utils/common_models/api_response_model.dart';
import '../config/app_config.dart';
import '../services/hive/hive_keys.dart';
import '../services/hive/hive_storage_helper.dart';
import 'api_connectivity.dart';
import 'api_endpoints.dart';
import 'api_params.dart';
import 'api_service_interface.dart';

class ApiService extends APIConnectivity implements ApiServiceInterface {
  static ApiService? _apiService;

  ApiService._internal() : super(baseURL: AppConfig.getInstance().baseUrl);

  static void _initApiService() {
    if (_apiService == null) {
      _apiService = ApiService._internal();
    } else {
      throw StateError("API service is already initialised");
    }
  }

  static ApiService get instance {
    if (_apiService == null) {
      _initApiService();
    }
    return _apiService!;
  }

  @override
  Future<ApiResponse> sendOtpLoginSignUp(
      {required String mobileNumber,}) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.sendOtp, data: {
      Params.mobileNumber: mobileNumber,
    }));
  }

  @override
  Future<ApiResponse> verifyOtp({
    required AuthenticationReqModel authenticationReqModel,
  }) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.verifyOtp, data: authenticationReqModel.toJson()));
  }

  @override
  Future<ApiResponse> resendOtp({
    String? mobileNumber,
  }) async {
    return await apiRequest(
        request: dio.post(ApiEndpoints.sendOtp,
            data: {
        Params.mobileNumber: mobileNumber
    }));
  }

  @override
  Future<ApiResponse> logout() async {
    final refreshToken = await HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.refreshToken);
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.logout,
        data: {
          Params.refreshToken: refreshToken,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> refreshToken({
    required String refreshToken,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.refreshToken,
        data: {
          Params.refresh: refreshToken,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> getQuickBookServices() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.quickBookServices,
      ),
    );
  }

  @override
  Future<ApiResponse> getCaregiverServices({required String type}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.caregiverServices,
        queryParameters: {
          Params.type: type,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> getDashboardOffers() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.bannerOffers,
      ),
    );
  }

  @override
  Future<ApiResponse> getAccordionTestimonials() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.accordionTestimonials,
      ),
    );
  }


  @override
  Future<ApiResponse> getAddresses() async {
    return await apiRequest(
        request: dio.get(
      ApiEndpoints.getAddresses,
    ));
  }

  @override
  Future<ApiResponse> addAddress(
      {required AddressReqModel addressReqModel}) async {
    return await apiRequest(
        request:
            dio.post(ApiEndpoints.addAddress, data: addressReqModel.toJson()));
  }

  @override
  Future<ApiResponse> editAddress(
      {required String addressId,
      required AddressReqModel addressReqModel}) async {
    return await apiRequest(
        request: dio.patch(ApiEndpoints.editAddress(addressId),
            data: addressReqModel.toJson()));
  }

  @override
  Future<ApiResponse> deleteAddress({required String addressId}) async {
    return await apiRequest(
        request: dio.delete(ApiEndpoints.deleteAddress(addressId)));
  }

  @override
  Future<ApiResponse> makeDefaultAddress({required String addressId}) async {
    return await apiRequest(
        request: dio.patch(ApiEndpoints.markDefaultAddress(addressId), data: {
      Params.isDefault: true,
    }));
  }

  @override
  Future<ApiResponse> getUserTestimonials() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.testimonials,
      ),
    );
  }

  @override
  Future<ApiResponse> getSubServiceById({required int serviceId}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.subServices,
        queryParameters: {
          Params.serviceType: serviceId,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> getInstructionsByServiceId({required int serviceId}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.serviceInstructions,
        queryParameters: {
          Params.serviceType: serviceId,
        },
      ),
    );
  }


  @override
  Future<ApiResponse> isInstantServiceAvailable({required InstantServiceAvailableReqModel instantServiceAvailableReqModel}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.isInstantServiceAvailable,
        queryParameters: instantServiceAvailableReqModel.toJson(),
      ),
    );
  }

  @override
  Future<ApiResponse> createNewBooking({required BookingRequestModel bookingRequestModel}) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.bookings,
        data: bookingRequestModel.toJson(isBooking: true),
      ),
    );
  }

  @override
  Future<ApiResponse> updateBookingById({required BookingRequestModel bookingRequestModel, required int bookingId}) async {
    return await apiRequest(
      request: dio.patch(
        ApiEndpoints.bookingDetails(bookingId),
        data: bookingRequestModel.toJson(isBooking: true),
      ),
    );
  }

  @override
  Future<ApiResponse> generateCashfreeOrder({
    required BookingRequestModel bookingRequestModel,
    required int bookingId,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.cashfreeOrder(bookingId),
        data: bookingRequestModel.toJson(isBooking: false),
      ),
    );
  }

  @override
  Future<ApiResponse> verifyCashfreePayment({required int bookingId}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.cashfreeOrder(bookingId),
      ),
    );
  }

  @override
  Future<ApiResponse> cmsDropDown() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.cmsDropdown,
      ),
    );
  }

  @override
  Future<ApiResponse> getBookingDetailsById({required int bookingId}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.bookingDetails(bookingId),
      ),
    );
  }

  @override
  Future<ApiResponse> getBookings({String? status}) async {
    Map<String, dynamic> queryParameters = {};
    if (status != null) {
      queryParameters[Params.status] = status;
    }
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.bookings,
        queryParameters: queryParameters,
      ),
    );
  }

  @override
  Future<ApiResponse> discardPendingBooking({required int bookingId}) async {
    return await apiRequest(
      request: dio.delete(
        ApiEndpoints.bookingDetails(bookingId),
      ),
    );
  }

  @override
  Future<ApiResponse> cancelBookingById({required int bookingId}) async {
    return await apiRequest(
      request: dio.patch(
        ApiEndpoints.cancelBooking(bookingId),
      ),
    );
  }

  @override
  Future<ApiResponse> getProfile() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.profile,
      ),
    );
  }

  @override
  Future<ApiResponse> deleteAccount() async {
    return await apiRequest(request: dio.delete(ApiEndpoints.deleteAccount));
  }

  @override
  Future<ApiResponse> getUnratedBooking() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.unratedBooking,
      ),
    );
  }

  @override
  Future<ApiResponse> submitFeedback({
    required SubmitFeedbackReqModel submitFeedbackReqModel,
  }) async {
    return await apiRequest(
      request: dio.post(
        ApiEndpoints.submitFeedback,
        data: submitFeedbackReqModel.toJson(),
      ),
    );
  }

  @override
  Future<ApiResponse> getMyBookings(
    {GetBookingsFiltersReqModel? getBookingsFiltersReqModel,
  }) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.myBookings,
        queryParameters: getBookingsFiltersReqModel?.toJson(),
      ),
    );
  }

  @override
  Future<ApiResponse> getFAQs() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.faqs,
      ),
    );
  }

  @override
  Future<ApiResponse> getPlatformPolicy({required PolicyType policyType}) async {
    return await apiRequest(
      request: dio.get(
        policyType == PolicyType.privacyPolicy
            ? ApiEndpoints.privacyPolicy
            : policyType == PolicyType.refundPolicy
                ? ApiEndpoints.refundPolicy
                : ApiEndpoints.termsAndConditions,
      ),
    );
  }

  @override
  Future<ApiResponse> getIsAreaServiceable({required double lat, required double lng}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.serviceableAreaCheck,
        queryParameters: {
          Params.lat: lat,
          Params.lng: lng,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> getAppUpdateDetails() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.appUpdateDetails,
      ),
    );
  }

  @override
  Future<ApiResponse> getCouponOffersByItemTotal({required String itemTotal}) async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.couponOffers,
        queryParameters: {
          Params.cartAmount: itemTotal,
        },
      ),
    );
  }

  @override
  Future<ApiResponse> updateProfileDetails({
    required UserDataModel userDataModel,
  }) async {
    return await apiRequest(
      request: dio.patch(
        ApiEndpoints.user,
        data: userDataModel.toJson(),
      ),
    );
  }

  @override
  Future<ApiResponse> getInAppNotifications() async {
    return await apiRequest(
      request: dio.get(
        ApiEndpoints.inAppNotifications,
      ),
    );
  }
}
