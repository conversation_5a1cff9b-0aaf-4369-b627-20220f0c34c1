import 'dart:io';
import 'package:dio/dio.dart';

import '../../../features/splash/bloc/splash_bloc.dart';
import '../../dependency_injection/service_locator.dart';
import '../../navigation/navigation_service.dart';

class AuthInterceptor extends Interceptor {
  final Dio _dio;

  AuthInterceptor(this._dio);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final requestPath = err.requestOptions.path;
    if (err.response?.statusCode == 401 &&
        !requestPath.contains("/token/refresh/")) {
      await _handleTokenRefresh(err, handler);
    } else if (_isConnectionError(err)) {
      // _handleConnectionError(err, handler);
    } else {
      handler.reject(err);
    }
  }

  Future<void> _handleTokenRefresh(
      DioException err, ErrorInterceptorHandler handler) async {
    try {
      final verifyUserData = await serviceLocator<SplashScreenCubit>().refreshToken();
      if (verifyUserData.access != null && verifyUserData.refresh != null) {
        err.requestOptions.headers["Authorization"] = verifyUserData.access!;
        final clonedResponse = await retryRequest(err.requestOptions);
        return handler.resolve(clonedResponse);
      } else {
        serviceLocator<NavigationService>().doLogout();
      }
    } catch (e) {
      serviceLocator<NavigationService>().doLogout();
    }
  }

  bool _isConnectionError(DioException err) {
    return (err.type == DioExceptionType.unknown ||
            err.type == DioExceptionType.connectionError) &&
        (err.error is SocketException || err.error is HandshakeException);
  }

  // void _handleConnectionError(DioException err, ErrorInterceptorHandler handler) {
  //   serviceLocator<NavigationService>().navigateToNoInternetPage(
  //     apiEndpoint: err.requestOptions.path,
  //     checktype: err.type.toString(),
  //     tryAgain: () async {
  //       final response = await retryRequest(err.requestOptions);
  //       handler.resolve(response);
  //     },
  //   );
  // }

  Future<Response<dynamic>> retryRequest(RequestOptions requestOptions) async {
    try {
      return await _dio.request(
        requestOptions.path,
        options: Options(
          method: requestOptions.method,
          headers: requestOptions.headers,
        ),
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
      );
    } catch (e) {
      rethrow;
    }
  }
}
