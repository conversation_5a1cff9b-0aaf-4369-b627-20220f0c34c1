import '../../features/authentication/data/authentication_req_model.dart';
import '../../features/booking_type/data/instant_service_available_request_model.dart';
import '../../features/common/feedback/data/submit_feedback_req_model.dart';
import '../../features/my_addresses/data/save_address_req_model.dart';
import '../../features/my_bookings/data/get_bookings_filters_req_model.dart';
import '../../features/profile/data/enums/policy_type.dart';
import '../../features/profile/data/model/user_data_model.dart';
import '../../features/service_booking/data/model/booking_request_model.dart';
import '../../utils/common_models/api_response_model.dart';

abstract class ApiServiceInterface {

  Future<ApiResponse> sendOtpLoginSignUp({
    required String mobileNumber,
  });

  Future<ApiResponse> verifyOtp({
    required AuthenticationReqModel authenticationReqModel,
  });

  Future<ApiResponse> resendOtp({
    required String mobileNumber,
  });

  Future<ApiResponse> logout();

  Future<ApiResponse> refreshToken({required String refreshToken});

  Future<ApiResponse> getQuickBookServices();

  Future<ApiResponse> getCaregiverServices({required String type});

  Future<ApiResponse> getDashboardOffers();

  Future<ApiResponse> getAccordionTestimonials();

  Future<ApiResponse> addAddress({required AddressReqModel addressReqModel});

  Future<ApiResponse> editAddress({required String addressId, required AddressReqModel addressReqModel});

  Future<ApiResponse> deleteAddress({required String addressId});

  Future<ApiResponse> getAddresses();

  Future<ApiResponse> makeDefaultAddress({required String addressId});

  Future<ApiResponse> getUserTestimonials();

  Future<ApiResponse> getSubServiceById({required int serviceId});

  Future<ApiResponse> getInstructionsByServiceId({required int serviceId});

  Future<ApiResponse> isInstantServiceAvailable({required InstantServiceAvailableReqModel instantServiceAvailableReqModel});

  Future<ApiResponse> createNewBooking({required BookingRequestModel bookingRequestModel});

  Future<ApiResponse> generateCashfreeOrder({required BookingRequestModel bookingRequestModel, required int bookingId});

  Future<ApiResponse> verifyCashfreePayment({required int bookingId});

  Future<ApiResponse> cmsDropDown();

  Future<ApiResponse> getBookingDetailsById({required int bookingId});

  Future<ApiResponse> getBookings({String? status});

  Future<ApiResponse> discardPendingBooking({required int bookingId});

  Future<ApiResponse> updateBookingById({required BookingRequestModel bookingRequestModel, required int bookingId});

  Future<ApiResponse> cancelBookingById({required int bookingId});

  Future<ApiResponse> getProfile();

  Future<ApiResponse> deleteAccount();


  Future<ApiResponse> getUnratedBooking();

  Future<ApiResponse> submitFeedback({required SubmitFeedbackReqModel submitFeedbackReqModel});

  Future<ApiResponse> getMyBookings({GetBookingsFiltersReqModel? getBookingsFiltersReqModel});

  Future<ApiResponse> getFAQs();

  Future<ApiResponse> getPlatformPolicy({required PolicyType policyType});

  Future<ApiResponse> getIsAreaServiceable({required double lat, required double lng});

  Future<ApiResponse> getAppUpdateDetails();

  Future<ApiResponse> getCouponOffersByItemTotal({required String itemTotal});

  Future<ApiResponse> updateProfileDetails({required UserDataModel userDataModel});

  Future<ApiResponse> getInAppNotifications();
}
