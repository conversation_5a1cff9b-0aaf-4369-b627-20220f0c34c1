import 'dart:io';
import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:homeservice_app/core/services/hive/hive_keys.dart';
import 'package:homeservice_app/core/services/hive/hive_storage_helper.dart';
import 'package:homeservice_app/utils/app_info.dart';
import 'interceptor/auth_interceptor.dart';

const int _connectionTimeout = 60 * 1000;
const int _receiveTimeout = 60 * 1000;

class APIConnectivity {
  late final Dio dio;
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  APIConnectivity({required String baseURL}) {
    dio = Dio(BaseOptions(
      baseUrl: baseURL,
      connectTimeout: Duration(milliseconds: _connectionTimeout),
      receiveTimeout: Duration(milliseconds: _receiveTimeout),
    ));

    if (kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        responseBody: true,
        responseHeader: true,
        requestBody: true,
        error: true,
        requestHeader: true,
        request: true,
      ));
      dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
    }
    dio.interceptors.add(AuthInterceptor(dio));
  }

  Future<Map<String, String>> getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        var androidInfo = await _deviceInfoPlugin.androidInfo;
        return {
          "osName": "Android",
          "osVersion": androidInfo.version.release,
        };
      } else if (Platform.isIOS) {
        var iosInfo = await _deviceInfoPlugin.iosInfo;
        return {
          "osName": iosInfo.systemName,
          "osVersion": iosInfo.systemVersion,
        };
      }
    } catch (e) {
      debugPrint("Error getting device info: $e");
    }
    return {"osName": "Unknown", "osVersion": "Unknown"};
  }

  Future<void> setPartnerGatewayToken(String token, String refreshToken) async {
    try {
      // Cache token & refreshToken
      await HiveStorageHelper.saveData(HiveBoxName.user, HiveKeys.userToken, token);
      await HiveStorageHelper.saveData(HiveBoxName.user, HiveKeys.refreshToken, refreshToken);

      final Map<String, String> osInfo = await getDeviceInfo();

      final deviceInfo = (await _deviceInfoPlugin.deviceInfo).data;
      final String? brand = deviceInfo['brand'];
      final String? model = deviceInfo['model'];
      final String? deviceName = (deviceInfo['name'] ?? '$brand $model')?.replaceAll("’", "") ?? "Unknown";

      dio.options.headers.addAll({
        "Authorization": "Bearer $token",
        "x-device-name": deviceName,
        "x-app-version": AppPackageInfo.version,
        "x-os-version": osInfo["osVersion"] ?? "",
        "x-os": osInfo["osName"] ?? "",
        "x-build-number": AppPackageInfo.buildNumber,
      });
    } catch (e) {
      debugPrint("Error setting Partner Gateway Token: $e");
    }
  }

  void clearPartnerGatewayToken() async {
    dio.options.headers.clear();
  }
}
