class ApiEndpoints {
  static String userBaseUrl = "/service-user/api/accounts/v1";
  static String caregiverBaseUrl = "/service-caregiver/api/caregiver/v1";
  static String caregiverAccountsBaseUrl = "/service-caregiver/api/accounts/v1";
  static String bookingBaseUrl = "/service-booking/api/booking/v1";
  static String paymentsBaseUrl = "/service-payment/api/payment/v1";

  static String sendOtp = "$userBaseUrl/send-otp/";

  static String verifyOtp = "$userBaseUrl/verify-otp/";

  static String resendOtp = "$userBaseUrl/resend-otp/";

  static String user = "$userBaseUrl/profile/";

  static String logout = "$userBaseUrl/logout/";

  static String refreshToken = "$userBaseUrl/token/refresh/";

  static String quickBookServices = '$caregiverBaseUrl/quickbook-services/';

  static String caregiverServices = '$caregiverBaseUrl/services/';

  static String bannerOffers = '$userBaseUrl/banners/';

  static String accordionTestimonials = '$userBaseUrl/accordions/';

  static String getAddresses = "$userBaseUrl/address/";

  static String addAddress = "$userBaseUrl/address/";

  static String editAddress(String id) => "$userBaseUrl/address/$id/";

  static String deleteAddress(String id) => "$userBaseUrl/address/$id/";

  static String markDefaultAddress(String id) => "$userBaseUrl/mark-address/default/$id/";

  static String testimonials = "$userBaseUrl/testimonials/";

  static String subServices = "$caregiverBaseUrl/sub-services/";

  static String serviceInstructions = "$caregiverBaseUrl/service-instructions/";
  
  static String isInstantServiceAvailable = "$caregiverBaseUrl/caregiver-availability/";

  static String bookings = "$bookingBaseUrl/bookings/";
  static String bookingDetails(int bookingId) => "$bookingBaseUrl/bookings/$bookingId/";

  static String cashfreeOrder(int bookingId) => "$paymentsBaseUrl/cashfree-order/$bookingId/";

  static String cmsDropdown = '$caregiverAccountsBaseUrl/cms-dropdown/';

  static String cancelBooking(int bookingId) => "$paymentsBaseUrl/refund/$bookingId/";

  static String profile = "$userBaseUrl/profile-information/";

  static String deleteAccount = "$userBaseUrl/soft-delete/";

  static String unratedBooking = '$bookingBaseUrl/bookings/review-popup/';

  static String submitFeedback = '$bookingBaseUrl/bookings/reviews/';

  static String myBookings = '$bookingBaseUrl/my-bookings/';

  static String faqs = '$userBaseUrl/faq/';

  static String termsAndConditions = '$userBaseUrl/terms-and-conditions/';

  static String privacyPolicy = '$userBaseUrl/privacy-policy/';

  static String serviceableAreaCheck = '$caregiverBaseUrl/serviceable-area-check/';

  static String appUpdateDetails = '$userBaseUrl/force-update-check/';

  static String refundPolicy = '$userBaseUrl/refund-and-cancellation-policy/';

  static String couponOffers = '$bookingBaseUrl/applicable-coupons/';

  static String inAppNotifications = '$bookingBaseUrl/in-app-notifications/';
}
