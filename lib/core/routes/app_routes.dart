import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:homeservice_app/features/booking_type/presentation/pages/booking_type_screen.dart';
import 'package:homeservice_app/features/common/feedback/bloc/get_unrated_booking_cubit.dart';
import 'package:homeservice_app/features/service_booking/presentation/service_payments_screen.dart';
import 'package:homeservice_app/features/verifyOtp/bloc/resend_otp_bloc.dart';
import 'package:homeservice_app/features/verifyOtp/bloc/verify_otp_bloc.dart';

import '../../features/authentication/bloc/auth_bloc.dart';
import '../../features/authentication/data/authentication_req_model.dart';
import '../../features/authentication/presentation/pages/auth_screen.dart';
import '../../features/booking_details/bloc/booking_details_cubit.dart';
import '../../features/booking_details/presentation/booking_details_screen.dart';
import '../../features/common/feedback/bloc/submit_feedback_cubit.dart';
import '../../features/dashboard/bloc/app_bottom_nav_bar_cubit.dart';
import '../../features/dashboard/bloc/is_area_serviceable_cubit.dart';
import '../../features/dashboard/data/enums/caregiver_service_type.dart';
import '../../features/dashboard/data/model/caregiver_service_model.dart';
import '../../features/dashboard/presentation/pages/app_bottom_nav_bar.dart';
import '../../features/my_addresses/data/add_complete_address_nav_data.dart';
import '../../features/my_addresses/presentation/pages/add_complete_address_screen.dart';
import '../../features/my_addresses/presentation/pages/pick_address_screen.dart';
import '../../features/my_bookings/bloc/update_booking_bloc.dart';
import '../../features/profile/bloc/platform_policy_cubit.dart';
import '../../features/profile/data/enums/policy_type.dart';
import '../../features/profile/presentation/screens/help_and_support_screen.dart';
import '../../features/profile/presentation/screens/platform_policy_screen.dart';
import '../../features/profile/presentation/screens/profile_account_details_screen.dart';
import '../../features/profile/presentation/screens/profile_saved_addresses_screen.dart';
import '../../features/service_booking/presentation/coupon_offers_screen.dart';
import '../../features/service_booking/presentation/service_booking_screen.dart';
import '../../features/splash/presentation/splash_screen.dart';
import '../../features/verifyOtp/presentation/verify_otp_screen.dart';

class AppRoute {

  static const splash = "/splash";
  static const login = "/login";
  static const verifyOtp = "/verify_otp";
  static const pickAddressScreen = "/pick_address_screen";
  static const addCompleteAddress = "/add_complete_address";
  static const instantService = "/instant_service";
  static const onDemandBookingType = "/on_demand_booking_type";
  static const paymentScreen = "/payment_screen";
  static const couponsOffers = "/coupons_offers";
  static const bookingDetails = "/booking_details";
  static const profileAccountDetails = "/profile_account_details";
  static const appBottomNavBar = "/app_bottom_nav_bar";
  static const profileSavedAddresses = "/profile_saved_addresses";
  static const helpAndSupport = "/help_and_support";
  static const platformPolicy = "/platform_policy";
  
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoute.splash:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => const SplashScreen(),
        );
      case AppRoute.appBottomNavBar:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => AppBottomNavBarCubit(),
              ),
              BlocProvider(
                create: (context) => GetUnratedBookingCubit(),
              ),
              BlocProvider(
                create: (context) => SubmitFeedbackCubit(),
              ),
              BlocProvider(
                create: (context) => UpdateBookingBloc(),
              ),
            ],
            child: const AppBottomNavBar(),
          ),
        );
      case AppRoute.login:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => BlocProvider(
            create: (_) => AuthBloc(),
            child: const AuthScreen(),
          ),
        );
      case AppRoute.verifyOtp:
        final authenticationReqModel =
            settings.arguments as AuthenticationReqModel;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (_) => VerifyOtpBloc(),
              ),
              BlocProvider(
                create: (context) => ResendOtpBloc(),
              ),
            ],
            child: VerifyOtpScreen(
              authenticationReqModel: authenticationReqModel,
            ),
          ),
        );
      case AppRoute.pickAddressScreen:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => const PickAddressScreen(),
        );
      case AppRoute.onDemandBookingType:
        final serviceModel = settings.arguments as CaregiverServiceModel;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => ODBookingTypeScreen(serviceModel: serviceModel),
        );
      case AppRoute.paymentScreen:
        final serviceModelJson = (settings.arguments as Map)['serviceModel'] as Map<String, dynamic>;

        final serviceModel = CaregiverServiceModel.fromJson( serviceModelJson, CaregiverServiceType.getCaregiverServiceTypeFromString(serviceModelJson['caregiver_service_type']));
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(create: (_) => IsAreaServiceableCubit()),
            ],
            child: ServicePaymentsScreen(serviceModel: serviceModel),
          ),
        );
      case AppRoute.addCompleteAddress:
        final addCompleteAddressNavData =
            settings.arguments as AddCompleteAddressNavData?;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => AddCompleteAddressScreen(
            navData: addCompleteAddressNavData,
          ),
        );
      case AppRoute.instantService:
        final serviceModel = settings.arguments as CaregiverServiceModel;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => ServiceBookingScreen(serviceModel: serviceModel),
        );
      case AppRoute.bookingDetails:
        final bookingId = settings.arguments as int;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => SubmitFeedbackCubit(),
              ),
              BlocProvider(
                create: (_) => BookingDetailsCubit(),
              ),
            ],
            child: BookingDetailsScreen(bookingId: bookingId),
          ),
        );
      case AppRoute.profileAccountDetails:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => const ProfileAccountDetailsScreen(),
        );
      case AppRoute.profileSavedAddresses:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => const ProfileSavedAddressesScreen(),
        );
      case AppRoute.helpAndSupport:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => const HelpAndSupportScreen(),
        );
      case AppRoute.platformPolicy:
        final policyType = settings.arguments as PolicyType;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => BlocProvider(
            create: (context) => PlatformPolicyCubit(),
            child: PlatformPolicyScreen(
              policyType: policyType,
            ),
          ),
        );
      case AppRoute.couponsOffers:
        final itemTotal = settings.arguments as String;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => CouponOffersScreen(itemTotal: itemTotal),
        );

      default:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => Container(),
        );
    }
  }
}
