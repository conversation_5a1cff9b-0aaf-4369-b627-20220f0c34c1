import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../features/verifyOtp/data/auth_response_model.dart';
import '../../utils/app_info.dart';
import '../navigation/navigation_service.dart';
import '../navigation/navigation_service_impl.dart';
import '../route_observer/app_route_observer.dart';
import '../route_observer/app_route_observer_impl.dart';
import '../services/hive/hive_keys.dart';
import '../services/hive/hive_storage_helper.dart';

GetIt serviceLocator = GetIt.instance;

Future<void> setupLocator() async {
  await Hive.initFlutter();
  Hive.registerAdapter(UserAdapter());

  serviceLocator.registerSingleton<NavigationService>(NavigationServiceImpl.getInstance()!);

  serviceLocator.registerSingleton<AppRouteObserver>(AppRouteObserverImpl.getInstance()!);

  await AppPackageInfo.init();

  // initialize Hive Boxes
  await HiveStorageHelper.init([HiveBoxName.user]);
}
