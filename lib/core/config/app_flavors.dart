import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';

enum Flavor { prod, uat, dev }

class BaseUrl {
  static const String devUrl = "https://dev-api.homeservice.in";
  static const String uatUrl = "https://stage-api.homeservice.in";
  static const String prodUrl = "https://prod-api.homeservice.in";

  static String getBaseUrl(Flavor flavor) {
    switch (flavor) {
      case Flavor.prod:
        return prodUrl;
      case Flavor.uat:
        return uatUrl;
      case Flavor.dev:
        return devUrl;
      }
  }
}

class CashfreeEnvironment {
  static CFEnvironment getCashfreeEnvironment(Flavor flavor) {
    switch (flavor) {
      case Flavor.prod:
        return CFEnvironment.PRODUCTION;
      case Flavor.uat:
        return CFEnvironment.SANDBOX;
      case Flavor.dev:
        return CFEnvironment.SANDBOX;
      }
  }
}
