import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';

import 'app_flavors.dart';
import 'metadata_model.dart';

class AppConfig {
  final Flavor flavor;
  final String baseUrl;
  final CFEnvironment cashfreeEnv;
  final MetadataModel metadata;

  static AppConfig? _instance;

  AppConfig._internal({
    required this.baseUrl,
    required this.flavor,
    required this.cashfreeEnv,
    required this.metadata,
  });

  static void init({
    required Flavor flavor,
    required String baseUrl,
    required CFEnvironment cashfreeEnv,
    required MetadataModel metadata,
  }) {
    _instance = _instance ??
        AppConfig._internal(
          baseUrl: baseUrl,
          flavor: flavor,
          cashfreeEnv: cashfreeEnv,
          metadata: metadata,
        );
  }

  static AppConfig getInstance() {
    if (_instance == null) {
      throw StateError("App config not initialised");
    }
    return _instance!;
  }
}
