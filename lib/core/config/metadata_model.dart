import 'app_flavors.dart';

class MetadataModel {
  final String mediaBaseUrl;
  final String accordionVideoUrl;
  final String accordionThumbnailUrl;
  final String longTermRequestServiceImageUrl;
  final List<String> accordionImageUrls;
  final String contactSupportImageUrl;
  final String contactNumber;
  final String contactEmail;

  const MetadataModel({
    required this.mediaBaseUrl,
    required this.accordionVideoUrl,
    required this.accordionThumbnailUrl,
    required this.longTermRequestServiceImageUrl,
    required this.accordionImageUrls,
    required this.contactSupportImageUrl,
    required this.contactNumber,
    required this.contactEmail,
  });

  static MetadataModel getMetadata(Flavor flavor) {
    String baseUrl;

    switch (flavor) {
      case Flavor.uat:
        baseUrl = 'https://stage-media.homeservice.in/';
        break;
      case Flavor.prod:
        baseUrl = 'https://prod-media.homeservice.in/';
        break;
      case Flavor.dev:
      baseUrl = 'https://dev-media.homeservice.in/';
    }

    return MetadataModel(
      mediaBaseUrl: baseUrl,
      accordionVideoUrl: '${baseUrl}media/App+Static+Media/accordion-default-static.mp4',
      // TODO: Fix this
      accordionThumbnailUrl: 'https://dummyimage.com/640x360/000/fff.png&text=Video+Thumbnail',
      longTermRequestServiceImageUrl: '${baseUrl}media/App+Static+Media/lt+service+default+img.jpg',
      accordionImageUrls: [
        '${baseUrl}media/App+Static+Media/caregiver+default+image+1.webp',
        '${baseUrl}media/App+Static+Media/caregiver+default+image+2.webp',
        '${baseUrl}media/App+Static+Media/caregiver+default+image+3.webp',
      ],
      contactSupportImageUrl: '${baseUrl}media/App+Static+Media/caregiver+contact+us+support.png',
      contactNumber: '+918296708984',
      contactEmail: '<EMAIL>',
    );
  }
}
