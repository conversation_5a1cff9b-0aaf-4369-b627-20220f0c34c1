
import 'app_route_observer.dart';
import 'route_observer_impl.dart';

class AppRouteObserverImpl implements AppRouteObserver {
  final MyRouteObserver? _routeObserver;
  static AppRouteObserverImpl? _instance;
  int bottomNavBarIndex=0;
  static AppRouteObserver? getInstance() {
    _instance ??= AppRouteObserverImpl._(MyRouteObserver.getInstance());
    return _instance;
  }

  AppRouteObserverImpl._(this._routeObserver);

  @override
  String getCurrentRoute() {
    final screenName=_routeObserver?.stack.last.settings.name;
    return screenName ?? "unknown_route";
  }

  @override
  List<String?> getAllRoutes() {
    List<String?> allRoutes = [];
    _routeObserver?.stack.forEach((route) {
      allRoutes.add(route.settings.name);
    });
    return allRoutes;
  }
}