import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/instant_service_available_model.dart';
import '../data/instant_service_available_request_model.dart';

part 'instant_service_available_state.dart';

class InstantServiceAvailableCubit extends Cubit<InstantServiceAvailableState> {
  InstantServiceAvailableCubit() : super(InstantServiceAvailableInitState());

  Future<void> getIsInstantServiceAvailable({required InstantServiceAvailableReqModel instantServiceAvailableReqModel}) async {
    emit(InstantServiceAvailableLoadingState(instantServiceAvailableReqModel: instantServiceAvailableReqModel));
    final response = await ApiService.instance.isInstantServiceAvailable(instantServiceAvailableReqModel: instantServiceAvailableReqModel);
    try {
      if (response.success) {
        final InstantServiceAvailableModel instantServiceAvailableModel = InstantServiceAvailableModel.fromJson(response.data);
        emit(InstantServiceAvailableSuccessState(
          instantServiceAvailableModel: instantServiceAvailableModel,
          instantServiceAvailableReqModel: instantServiceAvailableReqModel,
        ));
      } else {
        emit(InstantServiceAvailableErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(InstantServiceAvailableErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
