part of 'instant_service_available_cubit.dart';

sealed class InstantServiceAvailableState extends Equatable {
  const InstantServiceAvailableState();

  @override
  List<Object> get props => [];
}

final class InstantServiceAvailableInitState extends InstantServiceAvailableState {
  @override
  List<Object> get props => [];
}

final class InstantServiceAvailableLoadingState extends InstantServiceAvailableState {
  final InstantServiceAvailableReqModel instantServiceAvailableReqModel;

  const InstantServiceAvailableLoadingState({required this.instantServiceAvailableReqModel});

  @override
  List<Object> get props => [instantServiceAvailableReqModel];
}

final class InstantServiceAvailableSuccessState extends InstantServiceAvailableState {
  final InstantServiceAvailableModel instantServiceAvailableModel;
  final InstantServiceAvailableReqModel instantServiceAvailableReqModel;

  const InstantServiceAvailableSuccessState({
    required this.instantServiceAvailableModel,
    required this.instantServiceAvailableReqModel,
  });

  @override
  List<Object> get props => [instantServiceAvailableModel, instantServiceAvailableReqModel];
}

final class InstantServiceAvailableErrorState extends InstantServiceAvailableState {
  final String errorMsg;

  const InstantServiceAvailableErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
