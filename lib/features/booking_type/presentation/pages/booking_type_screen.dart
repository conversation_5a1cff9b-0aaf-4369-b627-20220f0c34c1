// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/widgets/app_toast.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/continue_booking/bloc/continue_booking_cubit.dart';
import '../../../dashboard/data/model/caregiver_service_model.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../service_booking/bloc/booking_cubit.dart';
import '../../../service_booking/bloc/payment_service_cubit.dart';
import '../../../service_booking/data/enums/arrival_time_type.dart';
import '../../bloc/instant_service_available_cubit.dart';
import '../../data/instant_service_available_request_model.dart';
import '../widgets/caregiver_arrival_time_widget.dart';
import '../widgets/selected_service_widget.dart';

class ODBookingTypeScreen extends StatefulWidget {
  final CaregiverServiceModel serviceModel;

  const ODBookingTypeScreen({
    super.key,
    required this.serviceModel,
  });

  @override
  State<ODBookingTypeScreen> createState() => _ODBookingTypeScreenState();
}

class _ODBookingTypeScreenState extends State<ODBookingTypeScreen> {
  ArrivalTimeType? _selectedArrivalOption;
  DateTime _scheduledDateTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;
    final myAddressBloc = context.read<MyAddressesBloc>();
    final myAddressState = myAddressBloc.state;
    if (myAddressState is MyAddressesSuccessState) {
      final myAddress = myAddressState.selectedAddress;
      if (myAddress != null) {
        context.read<InstantServiceAvailableCubit>().getIsInstantServiceAvailable(
              instantServiceAvailableReqModel: InstantServiceAvailableReqModel(
                language: bookingRequestModel.language,
                serviceAttributes: bookingRequestModel.serviceAttributesIds?.join(","),
                serviceType: bookingRequestModel.serviceType?.toString(),
                subService: bookingRequestModel.serviceSubtype?.toString(),
                workType: widget.serviceModel.caregiverServiceType?.toString(),
                duration: bookingRequestModel.duration?.toString(),
                lat: myAddress.location?.first.toString(),
                lng: myAddress.location?.last.toString(),
              ),
            );
      }
    }
    final now = DateTime.now();
    _scheduledDateTime = DateTime(now.year, now.month, now.day, 10, 0);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return BlocBuilder<BookingCubit, BookingState>(
      builder: (context, state) {
        return Scaffold(
          appBar: CustomAppbar(
            title: AppStrings.instantCare(widget.serviceModel.name ?? ''),
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  20.ph,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: SelectedServiceWidget(
                      bookingRequestModel: state.bookingRequestModel,
                      serviceModel: widget.serviceModel,
                    ),
                  ),
                  20.ph,
                  ColoredBox(
                    color: colorScheme.surface,
                    child: SizedBox(
                      height: 8.h,
                      width: double.infinity,
                    ),
                  ),
                  16.ph,
                  Padding(
                    padding: EdgeInsets.only(left: 20.w),
                    child: Text14Bold(
                      AppStrings.caregiverArrivalTime,
                      fontSize: 16.sp,
                    ),
                  ),
                  24.ph,
                  CaregiverArrivalTimeWidget(
                    key: ValueKey(_selectedArrivalOption),
                    serviceModel: widget.serviceModel,
                    selectedArrivalOption: _selectedArrivalOption,
                    scheduledDateTime: _scheduledDateTime,
                    onDateTimeChanged: (newDateTime) {
                      setState(() {
                        _scheduledDateTime = newDateTime;
                      });
                    },
                    onBookingTypeChange: (selectedArrivalOption) {
                      if (selectedArrivalOption == _selectedArrivalOption) return;
                      setState(() {
                        _selectedArrivalOption = selectedArrivalOption;
                        final now = DateTime.now();
                        _scheduledDateTime = DateTime(now.year, now.month, now.day, 10, 0);
                      });
                    },
                  ),
                  32.ph,
                ],
              ),
            ),
          ),
        
          // Bottom Button
          bottomNavigationBar: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.w),
            child: PrimaryButton(
              buttonText: AppStrings.proceedToPayment,
              onPressed: _selectedArrivalOption == null
                  ? null
                  : () {
                      if (!isValidTimeSelected(_scheduledDateTime)) {
                        CustomToast.showToast(message: AppStrings.invalidTimeSelected);
                        return;
                      }
                      final cubit = context.read<BookingCubit>();
                      final state = cubit.state;

                      final updatedBookingRequestModel = state.bookingRequestModel.copyWith(
                        arrivalType: _selectedArrivalOption,
                        startDateTime: _scheduledDateTime,
                      );
                      cubit.updateBookingRequestModel(updatedBookingRequestModel);
                      context.read<PaymentServiceCubit>().createOrUpdateBookingStep(
                            bookingRequestModel: updatedBookingRequestModel,
                            onSuccess: () {
                              context.read<ContinueBookingCubit>().getLastBookingDetails();
                            },
                          );

                      Navigator.pushNamed(
                        context,
                        AppRoute.paymentScreen,
                        arguments: {
                          'serviceModel': widget.serviceModel.toJson()
                        },
                      ).then((_) {
                        final booking = context.read<BookingCubit>().state.bookingRequestModel;

                        setState(() {
                          if (booking.arrivalType != _selectedArrivalOption) {
                            _selectedArrivalOption = booking.arrivalType;
                          }
                          if (booking.startDateTime != null && booking.startDateTime != _scheduledDateTime) {
                            _scheduledDateTime = booking.startDateTime!;
                          }
                        });
                      });
                    },
            ),
          ),
        );
      },
    );
  }

  bool isValidTimeSelected(DateTime time) {
    final serviceableStart = DateTime(time.year, time.month, time.day, 10); // 10:00 AM
    final serviceableEnd = DateTime(time.year, time.month, time.day, 20); // 8:00 PM

    return (time.isAtSameMomentAs(serviceableStart) || time.isAfter(serviceableStart)) &&
        (time.isAtSameMomentAs(serviceableEnd) || time.isBefore(serviceableEnd.add(const Duration(minutes: 1))));
  }
}
