import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:shimmer/shimmer.dart';

class BookingTypeShimmer extends StatelessWidget {
  const BookingTypeShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final double titleHeight = 16.0;
    final double textHeight = 16.0;
    final double smallTextHeight = 14.0;
    final double radioSize = 20.0;
    final double paddingValue = 16.0;
    final double horizontalPadding = 20.0;
    final double borderRadiusValue = 8.0;
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Shimmer.fromColors(
      baseColor: colorScheme.shimmerBaseColor,
      highlightColor: colorScheme.shimmerHighlightColor,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding), // Use 20.w if using screenutil
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            4.ph,
            // Title Placeholder
            Container(
              height: titleHeight,
              width: 180.w, // Use 180.w if using screenutil
              decoration: BoxDecoration(
                color: Colors.white, // Shimmer needs a base color to overlay
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            36.ph,
            // Instant Option Placeholder
            Container(
              padding:
                  EdgeInsets.all(paddingValue), // Use 16.w if using screenutil
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.shimmerHighlightColor,
                ),
                borderRadius: BorderRadius.circular(borderRadiusValue),
              ),
              child: Row(
                children: [
                  Container(
                    width: radioSize, // Use 20.h if using screenutil
                    height: radioSize, // Use 20.h if using screenutil
                    margin: EdgeInsets.only(
                        right: 12.w), // Use 12.w if using screenutil
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white, // Shimmer needs a base color
                    ),
                  ),
                  Expanded(
                    child: Container(
                      height: textHeight,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  20.pw,
                  Container(
                    height: textHeight,
                    width: 100.w, // Use 100.w if using screenutil
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
            24.ph,

            // Schedule for Later Option Placeholder
            Container(
              padding:
                  EdgeInsets.all(paddingValue), // Use 16.w if using screenutil
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.shimmerHighlightColor,
                ),
                borderRadius: BorderRadius.circular(borderRadiusValue),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: radioSize, // Use 20.h if using screenutil
                        height: radioSize, // Use 20.h if using screenutil
                        margin: EdgeInsets.only(
                            right: 12.w), // Use 12.w if using screenutil
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white, // Shimmer needs a base color
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: textHeight,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ],
                  ),
                  20.ph,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date Picker Row Placeholder
                      Container(
                        height:
                            40.h, // Adjust height as needed for date picker row
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      24.ph,

                      // Start Time Section Title Placeholder
                      Container(
                        height: textHeight,
                        width: 160.w, // Use 160.w if using screenutil
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      16.ph,

                      // TimePickerWidget Placeholder
                      Container(
                        height: 48.h, // Adjust height as needed for time picker
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      20.ph,

                      // Booking Slot Text Placeholder
                      Container(
                        height: smallTextHeight,
                        width: 200.w, // Use 200.w if using screenutil
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Add more spacing or elements if needed
          ],
        ),
      ),
    );
  }
}
