import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/extensions/list_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/custom_date_time_picker.dart';
import '../../../../widgets/error_state_widget.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/cms_dropdown_data/bloc/cms_dropdown_data_cubit.dart';
import '../../../common/cms_dropdown_data/data/enums/cms_drop_down_sub_type.dart';
import '../../../common/cms_dropdown_data/data/model/dropdown_response_model.dart';
import '../../../dashboard/data/model/caregiver_service_model.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../service_booking/bloc/booking_cubit.dart';
import '../../../service_booking/data/enums/arrival_time_type.dart';
import '../../bloc/instant_service_available_cubit.dart';
import '../../data/instant_service_available_request_model.dart';
import 'booking_type_shimmer.dart';

class CaregiverArrivalTimeWidget extends StatelessWidget {
  final ArrivalTimeType? selectedArrivalOption;
  final void Function(ArrivalTimeType) onBookingTypeChange;
  final DateTime scheduledDateTime;
  final void Function(DateTime) onDateTimeChanged;
  final CaregiverServiceModel serviceModel;

  const CaregiverArrivalTimeWidget({
    super.key,
    this.selectedArrivalOption,
    required this.onBookingTypeChange,
    required this.scheduledDateTime,
    required this.onDateTimeChanged,
    required this.serviceModel,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final lightBorderColor = colorScheme.greenD9F0E8;
    final selectedBorderColor = colorScheme.primary;
    return BlocConsumer<InstantServiceAvailableCubit, InstantServiceAvailableState>(
      listener: (context, state) {
        if (state is InstantServiceAvailableSuccessState) {
          if (state.instantServiceAvailableModel.available == false) {
            onBookingTypeChange(ArrivalTimeType.schedule);
          }
        }
      },
      builder: (context, state) {
        if (state is InstantServiceAvailableLoadingState) {
          return BookingTypeShimmer();
        }
        if (state is InstantServiceAvailableErrorState) {
          return ErrorStateWidget(
            errorMessage: state.errorMsg,
            onRetry: () {
              final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;
              final myAddressBloc = context.read<MyAddressesBloc>();
              final myAddressState = myAddressBloc.state;
              if (myAddressState is MyAddressesSuccessState) {
                final myAddress = myAddressState.selectedAddress;
                if (myAddress != null) {
                  context.read<InstantServiceAvailableCubit>().getIsInstantServiceAvailable(
                        instantServiceAvailableReqModel: InstantServiceAvailableReqModel(
                          language: bookingRequestModel.language,
                          serviceAttributes: bookingRequestModel.serviceAttributesIds?.join(","),
                          serviceType: bookingRequestModel.serviceType?.toString(),
                          subService: bookingRequestModel.serviceSubtype?.toString(),
                          workType: serviceModel.caregiverServiceType?.toString(),
                          duration: bookingRequestModel.duration?.toString(),
                          lat: myAddress.location?.first.toString(),
                          lng: myAddress.location?.last.toString(),
                        ),
                      );
                }
              }
            },
          );
        }
        if (state is InstantServiceAvailableSuccessState) {
          bool isInstantServiceAvailable =
              state.instantServiceAvailableModel.available ?? false;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppGestureDetector(
                  // Make the whole container tappable
                  onTap: isInstantServiceAvailable
                      ? () {
                          onBookingTypeChange(ArrivalTimeType.instant);
                        }
                      : null,
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color:
                            selectedArrivalOption == ArrivalTimeType.instant
                                ? selectedBorderColor
                                : lightBorderColor,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.all(16.w),
                          child: Row(
                            children: [
                              Container(
                                width: 20.h,
                                height: 20.h,
                                margin: EdgeInsets.only(right: 12.w),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: isInstantServiceAvailable
                                      ? null
                                      : colorScheme.lightGreyD5D7DA,
                                  border: Border.all(
                                    color: selectedArrivalOption == ArrivalTimeType.instant
                                        ? colorScheme.primary
                                        : colorScheme.lightGreyD5D7DA,
                                    width: selectedArrivalOption == ArrivalTimeType.instant
                                        ? 6
                                        : 1,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text14SemiBold(
                                  AppStrings.instant,
                                  fontSize: 16.sp,
                                ),
                              ),
                              Text14Bold(
                                isInstantServiceAvailable
                                    ? AppStrings.within15mins
                                    : AppStrings.notAvailable,
                                fontSize: 16.sp,
                                color: isInstantServiceAvailable
                                    ? colorScheme.success3FB68E
                                    : colorScheme.redD60000,
                              ),
                            ],
                          ),
                        ),
                        if (!isInstantServiceAvailable)
                          Container(
                            alignment: Alignment.center,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border(
                                top: BorderSide(
                                  color: lightBorderColor,
                                ),
                              ),
                              color: colorScheme.success3FB68E,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(4),
                                bottomRight: Radius.circular(4),
                              ),
                            ),
                            padding: EdgeInsets.symmetric(vertical: 4.h),
                            child: Text14SemiBold(
                              "Next available tomorrow 10am to 8pm",
                              color: colorScheme.white,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                16.ph,
                // Schedule for Later Option
                AppGestureDetector(
                  // Make the whole container tappable
                  onTap: () {
                    onBookingTypeChange(ArrivalTimeType.schedule);
                  },
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: selectedArrivalOption == ArrivalTimeType.schedule
                                ? selectedBorderColor
                                : lightBorderColor,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 20.h,
                              height: 20.h,
                              margin: EdgeInsets.only(right: 12.w),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: selectedArrivalOption == ArrivalTimeType.schedule
                                      ? colorScheme.primary
                                      : colorScheme.lightGreyD5D7DA,
                                  width: selectedArrivalOption == ArrivalTimeType.schedule
                                      ? 6
                                      : 1,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Text14SemiBold(
                                AppStrings.scheduleForLater,
                                fontSize: 16.sp,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                16.ph,
                        Builder(
                          builder: (context) {
                            final cmsDropdownDataCubit = context.read<CMSDropdownDataCubit>();
                            final cmsDropdownState = cmsDropdownDataCubit.state;
                            DropDownItem? cmsDropdownDataInstant;
                            DropDownItem? cmsDropdownDataNonInstant;
                            if (cmsDropdownState is CMSDropdownDataSuccessState) {
                              cmsDropdownDataInstant = cmsDropdownState.dropDownResponseModel?.items?.firstWhereOrNull(
                                (element) => element.subtype == CMSDropDownSubType.instant,
                              );
                              cmsDropdownDataNonInstant = cmsDropdownState.dropDownResponseModel?.items?.firstWhereOrNull(
                                (element) => element.subtype == CMSDropDownSubType.nonInstant,
                              );
                            }

                            return DateTimePickerWidget(
                              isInstantServiceSelected: selectedArrivalOption == ArrivalTimeType.instant,
                              initialDateTime: scheduledDateTime,
                              minDate: DateTime.now(),
                              maxDate: DateTime.now().add(const Duration(days: 7)),
                              onDateTimeChanged: onDateTimeChanged,
                              minimumDelay: isInstantServiceAvailable
                                  ? int.tryParse(cmsDropdownDataInstant?.value?.first ?? '') ?? 0
                                  : int.tryParse(cmsDropdownDataNonInstant?.value?.first ?? '') ?? 0,
                            );
                          },
                        ),
                        16.ph,
                        Text14SemiBold(
                          AppStrings.bookingSlot10amTo8pm,
                  color: colorScheme.lightGrey8F8F8F,
                ),
              ],
            ),
          );
        }
        return SizedBox();
      },
    );
  }
}
