// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../dashboard/data/model/caregiver_service_model.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../service_booking/bloc/booking_cubit.dart';
import '../../../service_booking/data/model/booking_request_model.dart';
import '../../../service_booking/widgets/change_sub_service_bottom_sheet.dart';
import '../../bloc/instant_service_available_cubit.dart';
import '../../data/instant_service_available_request_model.dart';

class SelectedServiceWidget extends StatelessWidget {
  final BookingRequestModel bookingRequestModel;
  final CaregiverServiceModel serviceModel;
  final bool allowChange;

  const SelectedServiceWidget({
    super.key,
    required this.bookingRequestModel,
    required this.serviceModel,
    this.allowChange = true,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 20.r,
          backgroundImage: NetworkImage(bookingRequestModel.subServiceImage ?? ''),
          backgroundColor: Colors.grey,
        ),
        8.pw,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text14Medium(
                serviceModel.name ?? '',
              ),
              Text14Bold(
                bookingRequestModel.subServiceName ?? '',
                fontSize: 16.sp,
              ),
              Row(
                children: [
                  Text14SemiBold(
                    '${bookingRequestModel.duration} hours',
                  ),
                  8.pw,
                  Text14SemiBold(
                    "•",
                    color: colorScheme.lightGreyD0D8DE,
                  ),
                  8.pw,
                  Text14SemiBold(
                    bookingRequestModel.language ?? '',
                  ),
                ],
              ),
            ],
          ),
        ),
        if (allowChange)
          AppGestureDetector(
            onTap: () async {
              final result = await changeSubServiceBottomSheet(
                context: context,
                serviceModel: serviceModel,
                bookingRequestModel: bookingRequestModel,
              );
              if (result == true) {
                final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;
                final myAddressBloc = context.read<MyAddressesBloc>();
                final myAddressState = myAddressBloc.state;
                if (myAddressState is MyAddressesSuccessState) {
                  final myAddress = myAddressState.selectedAddress;
                  if (myAddress != null) {
                    context.read<InstantServiceAvailableCubit>().getIsInstantServiceAvailable(
                          instantServiceAvailableReqModel: InstantServiceAvailableReqModel(
                            language: bookingRequestModel.language,
                            serviceAttributes: bookingRequestModel.serviceAttributesIds?.join(","),
                            serviceType: bookingRequestModel.serviceType?.toString(),
                            subService: bookingRequestModel.serviceSubtype?.toString(),
                            workType: serviceModel.caregiverServiceType?.toString(),
                            duration: bookingRequestModel.duration?.toString(),
                            lat: myAddress.location?.first.toString(),
                            lng: myAddress.location?.last.toString(),
                          ),
                        );
                  }
                }
              }
            },
            child: Text14Bold(
              AppStrings.change,
              color: colorScheme.primary,
            ),
          ),
      ],
    );
  }
}
