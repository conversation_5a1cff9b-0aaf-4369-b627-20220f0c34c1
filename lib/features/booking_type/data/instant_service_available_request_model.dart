class InstantServiceAvailableReqModel {
  String? workType;
  String? serviceType;
  String? subService;
  String? serviceAttributes;
  String? language;
  String? duration;
  String? lat;
  String? lng;
  int? addressId;

  InstantServiceAvailableReqModel({
    this.workType,
    this.serviceType,
    this.subService,
    this.serviceAttributes,
    this.language,
    this.duration,
    this.lat,
    this.lng,
    this.addressId,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (workType != null) {
      data['work_type'] = workType;
    }
    if (serviceType != null) {
      data['service_type'] = serviceType;
    }
    if (subService != null) {
      data['sub_service'] = subService;
    }
    if (serviceAttributes != null) {
      data['service_attributes'] = serviceAttributes;
    }
    if (language != null) {
      data['language'] = language;
    }
    if (duration != null) {
      data['duration'] = duration;
    }
    if (lat != null) {
      data['lat'] = lat;
    }
    if (lng != null) {
      data['lng'] = lng;
    }
    // if (addressId != null) {
    //   data['address_id'] = addressId;
    // }
    return data;
  }
}
