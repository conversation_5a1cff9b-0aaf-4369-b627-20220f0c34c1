import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class LocationUnavailableWidget extends StatelessWidget {
  const LocationUnavailableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.greenD9F0E8),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              children: [
                Text14SemiBold(
                  AppStrings.locationUnavailableTitle,
                  fontSize: 16.sp,
                ),
                2.ph,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Text14Medium(
                    AppStrings.locationUnavailableDescription,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                4.ph,
                Material(
                  color: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    side: BorderSide(
                      color: colorScheme.greenD9F0E8,
                    ),
                  ),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8.r),
                    splashColor: colorScheme.greenD9F0E8.withOpacity(0.2),
                    highlightColor: Colors.transparent,
                    onTap: () {
                      Navigator.pushNamed(context, AppRoute.pickAddressScreen);
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                      child: Text14Bold(
                        AppStrings.selectALocation,
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
