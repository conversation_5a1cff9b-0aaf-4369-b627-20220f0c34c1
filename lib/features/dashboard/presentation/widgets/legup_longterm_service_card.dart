import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/utils/string_constants/app_strings.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/custom_cache_image.dart';
import '../../../../widgets/texts/app_text.dart';

class LegupLongtermServiceCard extends StatelessWidget {
  final String title;
  final String description;
  final String price;
  final String buttonText;
  final String imageUrl;
  final VoidCallback onPressed;
  const LegupLongtermServiceCard({
    super.key,
    required this.title,
    required this.description,
    required this.price,
    required this.buttonText,
    required this.imageUrl,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF7D5B9), Color(0xFF86D5BA)],
          stops: [0.0, 0.65],
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.all(1),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.lightBrownF7ECE3,
          borderRadius: BorderRadius.circular(11.r),
        ),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 5,
                child: Padding(
                  padding: EdgeInsets.all(16.0.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text20Bold(
                            title,
                            maxLines: price.isNotEmpty ? 1 : 3,
                            overflow: TextOverflow.ellipsis,
                            lineHeight: 1.2,
                          ),
                          10.ph,
                          Text14Medium(
                            description,
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            color: colorScheme.blue140042,
                          ),
                          if (price.isNotEmpty &&
                              double.tryParse(price) != null) ...[
                            10.ph,
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: AppStrings.startingAt,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontFamily.nunitoSansMedium,
                                      color: colorScheme.green379F7D,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        ' ₹${double.parse(price).toInt()}/day',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w700,
                                      fontFamily: FontFamily.nunitoSansBold,
                                      color: colorScheme.green379F7D,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                      8.ph,
                      PrimaryButton(
                        backgroundColor: colorScheme.white,
                        onPressed: onPressed,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          side: BorderSide(
                            color: colorScheme.lightGreenB6E2D3,
                          ),
                        ),
                        child: Text14Bold(
                          buttonText,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 4,
                child: Padding(
                  padding: EdgeInsets.only(right: 16.w, bottom: 16.w),
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12.r),
                      bottomRight: Radius.circular(12.r),
                    ),
                    child: CustomCacheImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
