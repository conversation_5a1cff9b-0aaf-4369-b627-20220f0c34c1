import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/error_state_widget.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/inapp_notifications_cubit.dart';
import '../../data/enums/notification_navigation_type.dart';
import 'pending_action_card.dart';
import 'shimmer/pending_actions_shimmer.dart';

class PendingActions extends StatelessWidget {
  const PendingActions({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => InappNotificationsCubit()..getInappNotifications(),
      child: <PERSON><PERSON><PERSON>er<InappNotificationsCubit, InappNotificationsState>(
        builder: (context, state) {
          if (state is InappNotificationsLoadingState) {
            return const PendingActionsShimmer();
          }

          if (state is InappNotificationsErrorState) {
            return Column(
              children: [
                ErrorStateWidget(
                  errorMessage: state.errorMsg,
                  onRetry: () => context.read<InappNotificationsCubit>().getInappNotifications(),
                ),
                20.ph,
              ],
            );
          }

          if (state is InappNotificationsSuccessState) {
            final notifications = state.notificationsList;

            if (notifications.isEmpty) {
              return const SizedBox.shrink();
            }

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w).copyWith(top: 4.h, bottom: 32.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text14Bold(
                    AppStrings.pendingActionsCount(notifications.length),
                  ),
                  8.ph,
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: notifications.asMap().entries.map((entry) {
                        final index = entry.key;
                        final notification = entry.value;
                        return Row(
                          children: [
                            PendingActionCard(
                              width: notifications.length > 1
                                  ? ScreenUtil().screenWidth * 0.77
                                  : ScreenUtil().screenWidth - 50.w,
                              notification: notification,
                              onTap: () {
                                if (notification.navigationType != NotificationNavigationType.bookingDetails) return;
                                Navigator.pushNamed(
                                  context,
                                  AppRoute.bookingDetails,
                                  arguments: notification.extraData?.bookingId ?? 0,
                                );
                              },
                              evenColor: index.isEven,
                            ),
                            10.pw,
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}
