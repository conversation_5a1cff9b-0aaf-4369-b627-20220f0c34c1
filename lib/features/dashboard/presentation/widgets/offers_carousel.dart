import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../bloc/offers_cubit.dart';
import 'discount_count.dart';
import 'shimmer/offers_shimmer.dart';
import 'verified_caregiver.dart';

class DiscountCarousel extends StatefulWidget {
  const DiscountCarousel({super.key});

  @override
  State<DiscountCarousel> createState() => _DiscountCarouselState();
}

class _DiscountCarouselState extends State<DiscountCarousel> {
  final CarouselSliderController _controller = CarouselSliderController();
  int _current = 0;

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final aspectRatio = MediaQuery.of(context).size.aspectRatio;
    return BlocProvider(
      create: (_) => OffersCubit()..getDashboardOffers(),
      child: BlocBuilder<OffersCubit, OffersState>(
        builder: (context, state) {
          if (state is OffersLoadingState) {
            return OffersShimmer();
          }

          if (state is OffersSuccessState) {
            final offersList = state.offersList;
            final List<Widget> cardList = [
              VerifiedCaregiver(),
              ...offersList.asMap().entries.map((entry) {
                int index = entry.key;
                var offer = entry.value;
                final backgroundColor = hexToColor(offer.boxColor ?? '');
                final textColor = hexToColor(offer.textColor ?? '');

                if (offer.isActive == true) {
                  return DiscountCard(
                    title: offer.title ?? '',
                    subtitle: offer.subTitle ?? '',
                    description: offer.description ?? '',
                    buttonLabel: offer.buttonLabel ?? '',
                    backgroundColor: backgroundColor ??
                        (index % 2 != 0
                            ? colorScheme.orangeF0AB75
                            : colorScheme.success3FB68E),
                    textColor: textColor ?? colorScheme.white,
                    onTap: () {
                      if (offer.navigationType?.toLowerCase() == 'outside' &&
                          offer.buttonLink != null) {
                        launchUrl(Uri.parse(offer.buttonLink ?? ''));
                      } else if (offer.navigationType?.toLowerCase() == 'inside' &&
                          offer.buttonLink != null) {
                        Navigator.pushNamed(context, offer.buttonLink ?? '');
                      }
                    },
                  );
                }
                return SizedBox.shrink();
              }),
            ];
            return Column(
              children: [
                CarouselSlider(
                  items: cardList,
                  carouselController: _controller,
                  options: CarouselOptions(
                    height: aspectRatio <= 0.5 ? 145.h : 173.h,
                    autoPlay: true,
                    aspectRatio: aspectRatio,
                    autoPlayInterval: Duration(seconds: 3),
                    onPageChanged: (index, reason) {
                      setState(() {
                        _current = index;
                        for (int i = 0; i < cardList.length; i++) {}
                      });
                    },
                  ),
                ),
                Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: cardList.asMap().entries.map((entry) {
                        return AppGestureDetector(
                          onTap: () => _controller.animateToPage(entry.key),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            width: _current == entry.key ? 12.0 : 4.0,
                            height: 4.h,
                            margin: EdgeInsets.symmetric(horizontal: 1.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.r),
                              shape: BoxShape.rectangle,
                              color: _current == entry.key ? colorScheme.primary : colorScheme.primary.withOpacity(0.5),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            );
          }

          return SizedBox.shrink();
        },
      ),
    );
  }
}

Color? hexToColor(String? hex) {
  if (hex == null) return null;

  try {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }

    if (hex.length != 8) {
      return null;
    }

    final intColor = int.parse('0x$hex');
    return Color(intColor);
  } catch (e) {
    return null;
  }
}
