import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/texts/app_text.dart';

void showDismissContinueBookingDialog(
  BuildContext context, {
  required VoidCallback onConfirmPressed,
  bool isComingFromInstant = false,
}) {
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;
  showDialog(
    context: context,
    builder: (ctx) => Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
      backgroundColor: colorScheme.white,
      insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 24.r,
                  backgroundColor: colorScheme.redFFE0E0,
                  child: Center(
                    child: Text(
                      '!',
                      style: TextStyle(
                        fontSize: 26.sp,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.redD60000,
                      ),
                    ),
                  ),
                ),
                AppGestureDetector(
                  onTap: () => Navigator.pop(ctx),
                  child: Icon(
                    Icons.close_rounded,
                    color: colorScheme.blue150045,
                  ),
                ),
              ],
            ),
            20.ph,
            Flexible(
              child: Text20Bold(
                isComingFromInstant ? AppStrings.discardBookingTitleInstant : AppStrings.discardBookingTitle,
              ),
            ),
            12.ph,
            Flexible(
              child: Text14Medium(
                isComingFromInstant ? AppStrings.discardBookingDescriptionInstant : AppStrings.discardBookingDescription,
              ),
            ),
            20.ph,
            Divider(color: colorScheme.lightGreyD5D5D5),
            20.ph,
            PrimaryButton(
              width: 150.w,
              onPressed: onConfirmPressed,
              child: Text14Bold(
                AppStrings.discardBooking,
                fontSize: 16.sp,
                color: colorScheme.white,
              ),
            ),
            18.ph,
          ],
        ),
      ),
    ),
  );
}
