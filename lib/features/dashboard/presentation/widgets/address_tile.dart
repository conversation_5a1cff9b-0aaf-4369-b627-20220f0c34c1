import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../booking_type/data/instant_service_available_request_model.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../bloc/caregiver_count_cubit.dart';
import '../../bloc/is_area_serviceable_cubit.dart';
import '../../data/enums/caregiver_service_type.dart';

class AddressAppBar extends StatelessWidget implements PreferredSizeWidget {
  const AddressAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    return BlocConsumer<MyAddressesBloc, MyAddressesState>(
      listener: (context, state) {
        if (state is MyAddressesSuccessState) {
          final isLocationEnabled = state.selectedAddress != null;
          final selectedAddress = isLocationEnabled ? state.selectedAddress : null;
          if (selectedAddress != null) {
            context.read<IsAreaServiceableCubit>().getIsAreaServiceableService(lat: selectedAddress.location!.first, lng: selectedAddress.location!.last);
            context.read<CaregiverCountCubit>().getCaregiverCountForInstantService(instantServiceAvailableReqModel: InstantServiceAvailableReqModel(
              lat: selectedAddress.location!.first.toString(),
              lng: selectedAddress.location!.last.toString(),
              workType: CaregiverServiceType.onDemand.toString(),
            ));
          }
        }
      },
      builder: (context, state) {
        if (state is MyAddressesLoadingState) {
          return Container(
            padding: EdgeInsets.only(
              left: 20.0.w,
              right: 20.0.w,
              top: MediaQuery.of(context).padding.top,
              bottom: 10.0.h,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Shimmer.fromColors(
                        baseColor: colorScheme.shimmerBaseColor,
                        highlightColor: colorScheme.shimmerHighlightColor,
                        child: Container(
                          width: 150.0.w,
                          height: 16.0.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                            color: Colors.white,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.0.h),
                      Shimmer.fromColors(
                        baseColor: colorScheme.shimmerBaseColor,
                        highlightColor: colorScheme.shimmerHighlightColor,
                        child: Container(
                          width: 0.7.sw,
                          height: 12.0.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Shimmer.fromColors(
                  baseColor: colorScheme.shimmerBaseColor,
                  highlightColor: colorScheme.shimmerHighlightColor,
                  child: Container(
                    width: 24.0.w,
                    height: 24.0.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        final isLocationEnabled = state is MyAddressesSuccessState && state.selectedAddress != null;
        final selectedAddress = isLocationEnabled ? state.selectedAddress : null;
        final backgroundColor = isLocationEnabled ? colorScheme.white : colorScheme.primary;

        return Container(
          color: backgroundColor,
          padding: EdgeInsets.only(
            left: 20.0.w,
            right: 20.0.w,
            top: MediaQuery.of(context).padding.top,
          ),
          child: AppGestureDetector(
            onTap: () {
              Navigator.pushNamed(context, AppRoute.pickAddressScreen);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (isLocationEnabled) ...[
                        if (selectedAddress!.title != null && selectedAddress.title!.isNotEmpty)
                          Text14Bold(
                            selectedAddress.title!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        SizedBox(
                          width: 0.7.sw,
                          child: Text12Medium(
                            selectedAddress.address ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ] else ...[
                        Text14Bold(
                          AppStrings.selectLocation,
                          color: colorScheme.white,
                        ),
                        Text12Medium(
                          AppStrings.locationUnavailableDescription,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          color: colorScheme.white,
                        ),
                      ],
                    ],
                  ),
                ),
                SvgPicture.asset(
                  AppImages.myLocationIc,
                  colorFilter: isLocationEnabled ? null : ColorFilter.mode(
                    colorScheme.white,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(44.h);
}
