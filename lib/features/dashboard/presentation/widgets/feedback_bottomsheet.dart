import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_data.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/common_feedback_widget.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';
import '../../../common/feedback/data/feedback_reason_model.dart';

Future<List<FeedbackReason>> loadFeedbackReasons() async {
  final String response = await rootBundle.loadString(AppData.feedbackData);
  final List<dynamic> data = json.decode(response);
  return data.map((json) => FeedbackReason.fromJson(json)).toList();
}

void showFeedbackBottomSheet(
  BuildContext context, {
  BookingDetailsResponseModel? bookingDetailsResponseModel,
  void Function(String? selectedReasons, double ratingVal, String? otherReason)?
      onSubmit,
  void Function()? onClosed,
}) {
  showModalBottomSheet(
    context: context,
    isDismissible: false,
    enableDrag: false,
    isScrollControlled: true,
    useSafeArea: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    builder: (BuildContext ctx) {
      return FeedbackBottomSheet(
        bookingDetailsResponseModel: bookingDetailsResponseModel,
        onSubmit: onSubmit,
        onClosed: onClosed,
      );
    },
  );
}

class FeedbackBottomSheet extends StatefulWidget {
  final BookingDetailsResponseModel? bookingDetailsResponseModel;
  final void Function(
      String? selectedReasons, double ratingVal, String? otherReason)? onSubmit;
  final void Function()? onClosed;
  const FeedbackBottomSheet(
      {super.key,
      this.bookingDetailsResponseModel,
      this.onSubmit,
      this.onClosed});

  @override
  State<FeedbackBottomSheet> createState() => _FeedbackBottomSheetState();
}

class _FeedbackBottomSheetState extends State<FeedbackBottomSheet> {
  @override
  Widget build(BuildContext context) {
      final colorScheme = Theme.of(context).colorScheme;
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.only(
          left: 20.h,
          right: 20.h,
          top: 20.h,
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: colorScheme.greenE8F7F2,
                    child: Center(
                      child: SvgPicture.asset(
                        AppImages.successTickIcon,
                        width: 20.w,
                        height: 20.h,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close_rounded, size: 28),
                    onPressed: widget.onClosed,
                  ),
                ],
              ),
              16.ph,
              Text20Bold(
                  widget.bookingDetailsResponseModel?.subServiceDetails?.name ??
                      ''),
              Text20Bold(AppStrings.serviceCompleted),
              12.ph,
              Divider(
                color: colorScheme.lightGreyDEDEDE,
                height: 1.h,
              ),
              12.ph,
              CommonFeedbackWidget(
                caregiverName:
                    widget.bookingDetailsResponseModel?.caregiver?.fullName,
                caregiverImage: widget
                    .bookingDetailsResponseModel?.caregiver?.profilePicture,
                caregiverServiceType:
                    widget.bookingDetailsResponseModel?.subServiceDetails?.name,
                rating: widget.bookingDetailsResponseModel?.rating?.rating
                    ?.toDouble(),
                onSubmit: (selectedReasons, ratingVal, otherReason) {
                  widget.onSubmit
                      ?.call(selectedReasons, ratingVal, otherReason);
                },
              ),
              12.ph,
            ],
          ),
        ),
      ),
    );
  }
}
