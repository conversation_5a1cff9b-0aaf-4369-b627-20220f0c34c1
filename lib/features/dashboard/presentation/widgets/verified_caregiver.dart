import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class VerifiedCaregiver extends StatelessWidget {
  const VerifiedCaregiver({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.success3FB68E.withOpacity(0.2),
        ),
      ),
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.legUp,
            style: TextStyle(
              fontWeight: FontWeight.w800,
              fontSize: 14.sp,
              fontFamily: FontFamily.epilogueExtraBold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          4.ph,
          Text20ExtraBold(
            AppStrings.verifiedProfessions100,
            color: Theme.of(context).colorScheme.success3FB68E,
          ),
          8.ph,
          Center(
            child: Transform.scale(
              scale: 1.25,
              child: SvgPicture.asset(AppImages.supportImg2),
            ),
          ),
        ],
      ),
    );
  }
}
