import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../../utils/extensions/empty_space_extn.dart';

class QuickBookShimmer extends StatelessWidget {
  const QuickBookShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Shimmer.fromColors(
        baseColor: colorScheme.shimmerBaseColor,
        highlightColor: colorScheme.shimmerHighlightColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            20.ph,
            // Header section with title and badge
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 36.h,
                      width: 180.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    4.ph,
                    Container(
                      height: 16.h,
                      width: 150.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ],
                ),
                Container(
                  height: 40.h,
                  width: 100.w,
                  decoration: BoxDecoration(
                    color: colorScheme.shimmerColor,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ],
            ),

            20.ph,

            // Category buttons
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  2,
                  (index) => Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: Container(
                      height: 35.h,
                      width: 100.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            20.ph,

            // Features list
            Column(
              children: List.generate(
                3,
                (index) => Padding(
                  padding: EdgeInsets.only(bottom: 8.h, right: 105.w),
                  child: Row(
                    children: [
                      Container(
                        height: 20.h,
                        width: 20.w,
                        decoration: BoxDecoration(
                          color: colorScheme.shimmerColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      10.pw,
                      Expanded(
                        child: Container(
                          height: 16.h,
                          decoration: BoxDecoration(
                            color: colorScheme.shimmerColor,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            20.ph,

            // Price and button row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 20.h,
                      width: 80.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 14.h,
                      width: 60.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                Expanded(
                  child: Container(
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: colorScheme.shimmerColor,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
