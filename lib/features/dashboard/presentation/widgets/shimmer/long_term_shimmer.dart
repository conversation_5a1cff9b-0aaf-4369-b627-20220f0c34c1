import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../../utils/extensions/empty_space_extn.dart';

class LongTermShimmer extends StatelessWidget {
  const LongTermShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: Container(
              width: 120.w,
              height: 20.h,
              decoration: BoxDecoration(
                color: colorScheme.shimmerColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
          4.ph,
          Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: Container(
              width: double.infinity,
              height: 14.h,
              decoration: BoxDecoration(
                color: colorScheme.shimmerColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
          20.ph,
          ...List.generate(3, (_) => const LegupLongtermServiceCardShimmer())
        ],
      ),
    );
  }
}

class LegupLongtermServiceCardShimmer extends StatelessWidget {
  const LegupLongtermServiceCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        border: Border.all(
          color: colorScheme.shimmerBaseColor,
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(16.0.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 135.h,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        shimmerBox(context, width: 180.w, height: 20.h),
                        10.ph,
                        shimmerBox(context, width: 220.w, height: 14.h),
                        10.ph,
                        shimmerBox(context, width: 160.w, height: 14.h),
                        10.ph,
                        shimmerBox(context, width: 120.w, height: 14.h),
                      ],
                    ),
                  ),
                  10.ph,
                  shimmerBox(context, width: 100.w, height: 36.h),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.w),
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.r),
                bottomRight: Radius.circular(12.r),
              ),
              child: Shimmer.fromColors(
                baseColor: colorScheme.shimmerBaseColor,
                highlightColor: colorScheme.shimmerHighlightColor,
                child: Container(
                  width: 124.w,
                  height: 200.h,
                  color: colorScheme.shimmerColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget shimmerBox(BuildContext context, {required double width, required double height}) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Shimmer.fromColors(
      baseColor: colorScheme.shimmerBaseColor,
      highlightColor: colorScheme.shimmerHighlightColor,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: colorScheme.shimmerColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }
}
