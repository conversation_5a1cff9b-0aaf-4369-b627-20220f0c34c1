import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../../core/theme/app_theme.dart';

class OffersShimmer extends StatelessWidget {
  const OffersShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final aspectRatio = MediaQuery.of(context).size.aspectRatio;
    final int itemCount = 3;

    final List<Widget> shimmerItems = List.generate(
      itemCount,
      (index) => Container(
        decoration: BoxDecoration(
          color: colorScheme.shimmerColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        margin: EdgeInsets.symmetric(horizontal: 5.w),
        padding: EdgeInsets.only(left: 16.w, top: 13.5.h),
        width: MediaQuery.of(context).size.width * 0.75,
      ),
    );

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: CarouselSlider(
              options: CarouselOptions(
                height: aspectRatio <= 0.5 ? 145.h : 173.h,
                viewportFraction: 0.85,
                enableInfiniteScroll: true,
                autoPlay: true,
              ),
              items: shimmerItems,
            ),
          ),
        ),

        // Shimmer Indicators
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(itemCount, (index) {
              return Shimmer.fromColors(
                baseColor: colorScheme.shimmerBaseColor,
                highlightColor: colorScheme.shimmerHighlightColor,
                child: Container(
                  width: 4.w,
                  height: 4.h,
                  margin: EdgeInsets.symmetric(horizontal: 1.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.r),
                    shape: BoxShape.rectangle,
                    color: colorScheme.shimmerColor,
                  ),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}
