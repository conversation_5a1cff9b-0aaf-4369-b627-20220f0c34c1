import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../../utils/extensions/empty_space_extn.dart';

class LegupInstantShimmer extends StatelessWidget {
  final int itemCount;
  const LegupInstantShimmer({super.key, this.itemCount = 3});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Row Shimmer: Text and small container
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  shimmerBox(width: 120.w, height: 30.h, colorScheme: colorScheme),
                  8.ph,
                  shimmerBox(width: 180.w, height: 22.h, colorScheme: colorScheme),
                ],
              ),
              shimmerBox(
                width: 106.w,
                height: 42.h,
                colorScheme: colorScheme,
                borderRadius: BorderRadius.circular(12.r),
              ),
            ],
          ),
          16.ph,
          // GridView Shimmer
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              mainAxisExtent: 135.h,
              crossAxisCount: itemCount < 3 ? itemCount : 3,
              crossAxisSpacing: 10.w,
              mainAxisSpacing: 10.h,
            ),
            itemCount: itemCount,
            itemBuilder: (context, index) {
              return shimmerCard(colorScheme);
            },
          ),
          20.ph,
        ],
      ),
    );
  }

  Widget shimmerBox({
    required double width,
    required double height,
    required ColorScheme colorScheme,
    BorderRadius? borderRadius,
  }) {
    return Shimmer.fromColors(
      baseColor: colorScheme.shimmerBaseColor,
      highlightColor: colorScheme.shimmerHighlightColor,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: colorScheme.shimmerColor,
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
        ),
      ),
    );
  }

  Widget shimmerCard(ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: colorScheme.shimmerBaseColor,
        ),
        borderRadius: BorderRadius.circular(12.r),
        color: colorScheme.shimmerColor,
      ),
      child: Shimmer.fromColors(
        baseColor: colorScheme.shimmerBaseColor,
        highlightColor: colorScheme.shimmerHighlightColor,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Circle shimmer for avatar
            Container(
              width: 64.w,
              height: 64.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: colorScheme.shimmerColor,
              ),
            ),
            8.ph,
            // Text shimmer
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Container(
                width: 70.w,
                height: 12.h,
                decoration: BoxDecoration(
                  color: colorScheme.shimmerColor,
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
