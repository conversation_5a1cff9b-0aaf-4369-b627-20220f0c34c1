import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../../utils/extensions/empty_space_extn.dart';

class PendingActionsShimmer extends StatelessWidget {
  const PendingActionsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w).copyWith(top: 4.h, bottom: 32.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: Container(
              height: 20.h,
              width: 180.w,
              decoration: BoxDecoration(
                color: colorScheme.shimmerColor,
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
          8.ph,
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(2, (index) {
                return Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: colorScheme.shimmerBaseColor,
                      highlightColor: colorScheme.shimmerHighlightColor,
                      child: Container(
                        width: 300.w,
                        height: 80.h,
                        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
                        decoration: BoxDecoration(
                          color: colorScheme.shimmerColor,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                    ),
                    12.pw,
                  ],
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}
