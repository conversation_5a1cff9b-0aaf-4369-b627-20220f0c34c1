import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/continue_booking/bloc/continue_booking_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../service_booking/bloc/booking_cubit.dart';
import '../../bloc/caregiver_count_cubit.dart';
import '../../bloc/caregiver_services_cubit.dart';
import '../../data/enums/caregiver_service_type.dart';
import '../../data/model/caregiver_service_model.dart';
import 'caregiver_count_widget.dart';
import 'instant_not_available_widget.dart';
import 'legup_instant_service_card.dart';
import 'location_unavailable_widget.dart';
import 'shimmer/legup_instant_shimmer.dart';
import 'show_dismiss_continue_booking_dialog.dart';

class LegupInstant extends StatefulWidget {
  const LegupInstant({super.key});

  @override
  State<LegupInstant> createState() => _LegupInstantState();
}

class _LegupInstantState extends State<LegupInstant> {

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocProvider(
      create: (_) => CaregiverServicesCubit()..getCaregiverServicesService(type: CaregiverServiceType.onDemand),
      child: BlocBuilder<CaregiverServicesCubit, CaregiverServicesState>(
        builder: (context, state) {
          if (state is CaregiverServicesLoadingState) {
            return LegupInstantShimmer();
          }
          if (state is CaregiverServicesSuccessState) {
            final services = state.caregiverServiceList;
            return BlocBuilder<MyAddressesBloc, MyAddressesState>(
              builder: (context, state) {
                final isLocationEnabled = state is MyAddressesSuccessState && state.selectedAddress != null;
                return Padding(
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text20Bold(
                                AppStrings.legUpInstant,
                              ),
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: isLocationEnabled
                                          ? AppStrings.getServiceIn
                                          : AppStrings.getServicesWithInMinutes,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: FontFamily.nunitoSansRegular,
                                        color: colorScheme.blue140042,
                                      ),
                                    ),
                                    if (isLocationEnabled)
                                      TextSpan(
                                        text: AppStrings.mins15,
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w700,
                                          fontFamily: FontFamily.nunitoSansExtraBold,
                                          color: colorScheme.blue140042,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          CaregiverCountWidget(isLocationEnabled: isLocationEnabled),],
                      ),
                      16.ph,
                      GridView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          mainAxisExtent: 105.h,
                          crossAxisCount: services.length < 3 ? services.length : 3,
                          crossAxisSpacing: 10.w,
                          mainAxisSpacing: 10.h,
                        ),
                        itemCount: services.length,
                        itemBuilder: (context, index) {
                          return LegupInstantServiceCard(
                            isLocationEnabled: isLocationEnabled,
                            title: services[index].name ?? '',
                            image: services[index].image ?? '',
                            onTap: () {
                              final continueBookingCubit = context.read<ContinueBookingCubit>();
                              final continueBookingState = continueBookingCubit.state;
                              if (continueBookingState is ContinueBookingSuccessState && continueBookingState.bookingDetailsResponseModel != null) {
                                showDismissContinueBookingDialog(
                                  context,
                                  isComingFromInstant: true,
                                  onConfirmPressed: () {
                                    Navigator.pop(context);
                                    context.read<ContinueBookingCubit>().discardPendingBooking(bookingId: continueBookingState.bookingDetailsResponseModel!.id!);
                                    handleServiceTap(services[index]);
                                  },
                                );
                              } else {
                                handleServiceTap(services[index]);
                              }
                            },
                          );
                        },
                      ),
                      if (!isLocationEnabled) ... [
                        12.ph,
                        LocationUnavailableWidget(),
                        20.ph,
                      ],
                      BlocBuilder<CaregiverCountCubit, CaregiverCountState>(
                        builder: (context, state) {
                          final isInstantServiceAvailable = isLocationEnabled && state is CaregiverCountSuccessState && state.instantServiceAvailableModel.count != null && state.instantServiceAvailableModel.count! > 0;
                          if (!isInstantServiceAvailable) {
                            return Column(
                              children: [
                                12.ph,
                                InstantNotAvailableWidget(),
                                20.ph,
                              ],
                            );
                          }
                          return SizedBox.shrink();
                        },
                      ),
                      20.ph,
                    ],
                  ),
                );
              },
            );
          }
          return SizedBox.shrink();
        },
      ),
    );
  }

  void handleServiceTap(CaregiverServiceModel service) {
    context.read<BookingCubit>().resetBookingRequestModel();
    Navigator.pushNamed(context, AppRoute.instantService, arguments: service);
  }
}
