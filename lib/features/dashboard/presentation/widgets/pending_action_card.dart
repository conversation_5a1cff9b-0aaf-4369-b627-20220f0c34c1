import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../data/model/notifications_model.dart';

class PendingActionCard extends StatelessWidget {
  final NotificationsModel notification;
  final VoidCallback onTap;
  final bool evenColor;
  final double width;

  const PendingActionCard({
    super.key,
    required this.notification,
    required this.onTap,
    this.evenColor = false,
    required this.width,
  });

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    final gradientColors = evenColor
        ? [colorScheme.orangeFAE3D1, colorScheme.orangeF5C7A3]
        : [colorScheme.redFCE3E3, colorScheme.redEF8F8F];

    return Container(
      width: width,
      height: 85.h,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text12Medium(
                  notification.trigger ?? '',
                  color: colorScheme.green595959,
                  fontSize: 10.sp,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                4.ph,
                Text14SemiBold(
                  notification.title ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (notification.createdAt != null) ...[
                  4.ph,
                  Text12Medium(
                    DateFormat('d MMM, y, h:mma').format(notification.createdAt!),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          8.pw,
          PrimaryButton(
            width: 50.w,
            height: 35.h,
            backgroundColor: colorScheme.white,
            onPressed: onTap,
            child: Text14Bold(
              AppStrings.view,
              color: colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }
}
