import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../../widgets/webview_screen.dart';
import '../../bloc/caregiver_services_cubit.dart';
import '../../data/enums/caregiver_service_type.dart';
import 'legup_longterm_service_card.dart';
import 'shimmer/long_term_shimmer.dart';

class LegupLongterm extends StatelessWidget {
  const LegupLongterm({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => CaregiverServicesCubit()..getCaregiverServicesService(type: CaregiverServiceType.longTerm),
      child: BlocBuilder<CaregiverServicesCubit, CaregiverServicesState>(
        builder: (context, state) {
          if (state is CaregiverServicesLoadingState) {
            return LongTermShimmer();
          }

          if (state is CaregiverServicesSuccessState) {
            final services = state.caregiverServiceList;
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text20Bold(
                    AppStrings.legUpLongTerm,
                  ),
                  Text14Medium(
                    AppStrings.legUpLongTermDescription,
                    color: Theme.of(context).colorScheme.blue140042,
                  ),
                  20.ph,
                  ...services.map((service) {
                    return LegupLongtermServiceCard(
                      title: service.name ?? '',
                      description: service.description ?? '',
                      price: service.basePrice ?? '',
                      buttonText: AppStrings.scheduleNow,
                      imageUrl: service.image ?? '',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => WebViewScreen(
                              webViewArgumentModel: WebViewArgumentModel(
                                url: "https://forms.homeservice.in/homeserviceplans",
                                title: "HomeService Long Term Service",
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  }),
                  // LegupLongtermServiceCard(
                  //   title: AppStrings.findYourIdealLongTermCareGiver,
                  //   description: AppStrings.customizedJustForYou,
                  //   price: '',
                  //   buttonText: AppStrings.requestService,
                  //   imageUrl: AppConfig.getInstance().metadata.longTermRequestServiceImageUrl,
                  // ),
                ],
              ),
            );
          }
          return SizedBox.shrink();
        },
      ),
    );
  }
}
