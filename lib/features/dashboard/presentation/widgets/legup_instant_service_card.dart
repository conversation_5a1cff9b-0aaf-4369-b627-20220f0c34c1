import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';
import 'package:homeservice_app/widgets/custom_cache_image.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../widgets/texts/app_text.dart';

class LegupInstantServiceCard extends StatelessWidget {
  final String title;
  final String image;
  final bool isLocationEnabled;
  final VoidCallback onTap;

  const LegupInstantServiceCard({
    super.key,
    required this.title,
    required this.image,
    required this.isLocationEnabled,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return AppGestureDetector(
      onTap: isLocationEnabled ? onTap : null,
      child: Container(
        width: 105.w,
        decoration: BoxDecoration(
          color: isLocationEnabled ? colorScheme.lightBrownFBF4EF : colorScheme.greyEDEDED,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Opacity(
              opacity: isLocationEnabled ? 1 : 0.4,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(32.w),
                child: CustomCacheImage(
                  imageUrl: image,
                  width: 64.w,
                  height: 64.w,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            4.ph,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text14Medium(
                title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
