import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/custom_cache_image.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../../widgets/video_player_widget.dart';
import '../../bloc/accordion_cubit.dart';
import '../../data/enums/media_type.dart';
import 'shimmer/our_caregivers_shimmer.dart';

class OurCaregivers extends StatefulWidget {
  const OurCaregivers({super.key});

  @override
  State<OurCaregivers> createState() => _OurCaregiversState();
}

class _OurCaregiversState extends State<OurCaregivers> {
  final List<bool> _expandedStates = List.filled(5, false);

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocProvider(
      create: (_) => AccordionCubit()..getAccordionTestimonials(),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            decoration: BoxDecoration(
              color: colorScheme.lightBrownFBF4EF,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32.r),
                bottomRight: Radius.circular(32.r),
              ),
              border: Border.all(
                width: 2,
                color: colorScheme.brownE8CAB0,
                strokeAlign: BorderSide.strokeAlignOutside,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  48.ph,
                  Text14Medium(
                    AppStrings.ourCareGivers,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w700,
                    color: colorScheme.green329071,
                  ),
                  8.ph,
                  Text14Medium(
                    AppStrings.onlyTheBestForYourLovedOnes,
                    fontSize: 18.sp,
                    color: colorScheme.green379F7D,
                  ),
                  42.ph,
                  // VideoPlayerWidget(
                  //   videoUrl: AppConfig.getInstance().metadata.accordionVideoUrl,
                  //   thumbnailUrl: AppConfig.getInstance().metadata.accordionThumbnailUrl,
                  // ),
                  // 20.ph,
                  BlocBuilder<AccordionCubit, AccordionState>(
                    builder: (context, state) {
                      if (state is AccordionLoadingState) {
                        return OurCaregiversShimmer();
                      }
      
                      if (state is AccordionSuccessState) {
                        final accordionList = state.accordionList;
                        return ListView.builder(
                          itemCount: accordionList.length,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (_, index) {
                            final item = accordionList[index];
                            return Container(
                              margin: EdgeInsets.only(bottom: 12.h),
                              decoration: BoxDecoration(
                                color: colorScheme.lightBrownF6E9DF,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: ListTileTheme(
                                contentPadding: EdgeInsets.zero,
                                dense: true,
                                horizontalTitleGap: 0.0,
                                minLeadingWidth: 0,
                                child: ExpansionTile(
                                  title: Text14Medium(
                                    item.title ?? '',
                                    fontSize: 18.sp,
                                    color: colorScheme.blu1A3D65,
                                  ),
                                  trailing: Icon(
                                    _expandedStates[index] ? Icons.remove : Icons.add,
                                    color: colorScheme.blu1A3D65,
                                  ),
                                  tilePadding: EdgeInsets.symmetric(horizontal: 12.w),
                                  childrenPadding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 12.h),
                                  onExpansionChanged: (bool expanded) {
                                    setState(() {
                                      _expandedStates[index] = expanded;
                                    });
                                  },
                                  children: [
                                    Container(
                                      width: double.infinity,
                                      height: 1.5.h,
                                      color: colorScheme.success3FB68E,
                                    ),
                                    10.ph,
                                    Text12Regular(
                                      item.description ?? '',
                                      fontSize: 14.sp,
                                    ),
                                    10.ph,
                                    if (item.mediaType != null && item.media != null) ...[
                                      if (item.mediaType == MediaType.video) ...[
                                        VideoPlayerWidget(
                                          videoUrl: item.media ?? '',
                                          // TODO: Fix this
                                          thumbnailUrl: AppConfig.getInstance().metadata.accordionThumbnailUrl,
                                        ),
                                      ] else ...[
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(12.r),
                                          child: CustomCacheImage(
                                            imageUrl: item.media ?? '',
                                            width: double.infinity,
                                            height: 200.h,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ],
                                    ],
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      }
                      return SizedBox.shrink();
                    },
                  ),
                  24.ph,
                ],
              ),
            ),
          ),
          Positioned(
            right: 68.w,
            top: -17.h,
            child: CircleAvatar(
              radius: 42.5.r,
              backgroundColor: colorScheme.white,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(42.5.r),
                child: CustomCacheImage(
                  height: 38.5.r * 2,
                  width: 38.5.r * 2,
                  fit: BoxFit.cover,
                  imageUrl: AppConfig.getInstance().metadata.accordionImageUrls[0],
                ),
              ),
            ),
          ),
          Positioned(
            right: -21.w,
            top: 45.h,
            child: CircleAvatar(
              radius: 48.r,
              backgroundColor: colorScheme.white,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(48.r),
                child: CustomCacheImage(
                  height: 44.r * 2,
                  width: 44.r * 2,
                  fit: BoxFit.cover,
                  imageUrl: AppConfig.getInstance().metadata.accordionImageUrls[1],
                ),
              ),
            ),
          ),
          Positioned(
            right: 78.w,
            top: 93.h,
            child: CircleAvatar(
              radius: 52.5.r,
              backgroundColor: colorScheme.white,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(52.5.r),
                child: CustomCacheImage(
                  height: 48.5.r * 2,
                  width: 48.5.r * 2,
                  fit: BoxFit.cover,
                  imageUrl: AppConfig.getInstance().metadata.accordionImageUrls[2],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
