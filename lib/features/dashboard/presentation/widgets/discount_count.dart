import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/texts/app_text.dart';

class DiscountCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final String description;
  final String buttonLabel;
  final Color backgroundColor;
  final Color textColor;
  final Function()? onTap;

  const DiscountCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.buttonLabel,
    required this.backgroundColor,
    required this.textColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      padding: EdgeInsets.only(left: 16.w, top: 13.5.h),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(bottom: 13.5.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Html(
                    shrinkWrap: true,
                    data: title,
                    style: {
                      "*": Style(
                        fontFamily: FontFamily.nunitoSansExtraBold,
                        color: textColor,
                        maxLines: 1,
                        textOverflow: TextOverflow.ellipsis,
                        margin: Margins.all(0),
                      ),
                    },
                  ),
                  4.ph,
                  Text14ExtraBold(
                    subtitle,
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w800,
                    color: textColor,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  4.ph,
                  Text14SemiBold(
                    description,
                    color: textColor,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  10.ph,
                  if (buttonLabel.isNotEmpty)
                    AppGestureDetector(
                      onTap: onTap,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: textColor.withOpacity(0.12),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text14SemiBold(
                          buttonLabel,
                          color: textColor,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          Transform.translate(
            offset: Offset(3, 3),
            child: SvgPicture.asset(AppImages.offersImg),
          )
        ],
      ),
    );
  }
}
