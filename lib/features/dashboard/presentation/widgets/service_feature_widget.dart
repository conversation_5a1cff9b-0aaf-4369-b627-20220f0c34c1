import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../widgets/texts/app_text.dart';

class ServiceFeatureWidget extends StatelessWidget {
  final String text;

  const ServiceFeatureWidget({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppImages.infoStarIc),
        10.pw,
        Expanded(
          child: Text14Medium(
            text,
            fontSize: 14.sp,
          ),
        ),
      ],
    );
  }
}
