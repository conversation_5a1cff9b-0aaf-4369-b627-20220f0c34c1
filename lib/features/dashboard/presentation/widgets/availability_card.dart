import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class AvailabilityCard extends StatelessWidget {
  const AvailabilityCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.white,
            Theme.of(context).colorScheme.lightBrownF7ECE3,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.0.w),
        child: Row(
          children: [
            Container(
              width: 44.w,
              height: 2.h,
              color: Theme.of(context).colorScheme.success3FB68E,
            ),
            12.pw,
            SvgPicture.asset(
              AppImages.infoStarBoldIc,
              colorFilter: ColorFilter.mode(
                Theme.of(context).colorScheme.success3FB68E,
                BlendMode.srcIn,
              ),
            ),
            12.pw,
            Text14Bold(
              AppStrings.availabilityReplacementRefund,
              color: Theme.of(context).colorScheme.success3FB68E,
              textAlign: TextAlign.center,
            ),
            12.pw,
            SvgPicture.asset(
              AppImages.infoStarBoldIc,
              colorFilter: ColorFilter.mode(
                Theme.of(context).colorScheme.success3FB68E,
                BlendMode.srcIn,
              ),
            ),
            12.pw,
            Container(
              width: 44.w,
              height: 2.h,
              color: Theme.of(context).colorScheme.success3FB68E,
            ),
          ],
        ),
      ),
    );
  }
}
