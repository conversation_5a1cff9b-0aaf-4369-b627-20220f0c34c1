import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/category_button_widget.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/continue_booking/bloc/continue_booking_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../service_booking/bloc/booking_cubit.dart';
import '../../bloc/quick_book_cubit.dart';
import '../../data/model/caregiver_service_model.dart';
import '../../data/model/quick_book_response_model.dart';
import 'caregiver_count_widget.dart';
import 'location_unavailable_widget.dart';
import 'service_feature_widget.dart';
import 'shimmer/quick_book_shimmer.dart';
import 'show_dismiss_continue_booking_dialog.dart';

class BookingScreen extends StatefulWidget {
  const BookingScreen({
    super.key
  });

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  String? selectedServiceName;

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocProvider(
      create: (_) => QuickBookCubit()..getQuickBookService(),
      child: BlocBuilder<QuickBookCubit, QuickBookState>(
        builder: (context, state) {
          if (state is QuickBookLoadingState) {
            return QuickBookShimmer();
          }
          if (state is QuickBookSuccessState) {
            final services = state.quickBookServiceList;
            final selectedService = selectedServiceName != null
                ? services.firstWhere(
                    (service) => service.name == selectedServiceName,
                    orElse: () => services[0],
                  )
                : services.isNotEmpty
                    ? services[0]
                    : null;

            if (services.isEmpty) {
              return SizedBox.shrink();
            }

            return BlocBuilder<MyAddressesBloc, MyAddressesState>(
              builder: (context, state) {
                final isLocationEnabled = state is MyAddressesSuccessState && state.selectedAddress != null;
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      20.ph,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text20Bold(
                                AppStrings.quickBookWithLegUpInstant,
                                lineHeight: 0.9,
                              ),
                              2.ph,
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: isLocationEnabled
                                          ? AppStrings.getServiceIn
                                          : AppStrings.locationUnavailable,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: FontFamily.nunitoSansRegular,
                                        color: colorScheme.blue140042,
                                      ),
                                    ),
                                    if (isLocationEnabled)
                                      TextSpan(
                                        text: AppStrings.mins15,
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w700,
                                          fontFamily:
                                              FontFamily.nunitoSansExtraBold,
                                          color: colorScheme.blue140042,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          CaregiverCountWidget(isLocationEnabled: isLocationEnabled),
                        ],
                      ),
                      20.ph,
                      IgnorePointer(
                        ignoring: !isLocationEnabled,
                        child: Opacity(
                          opacity: isLocationEnabled ? 1 : 0.4,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: services.map((service) {
                                    return Padding(
                                      padding: EdgeInsets.only(right: 10.w),
                                      child: CategoryButtonRadioWidget(
                                        label: service.name ?? '',
                                        isSelected: selectedService?.name == service.name,
                                        onPressed: () {
                                          setState(() {
                                            selectedServiceName = service.name;
                                          });
                                        },
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                              20.ph,
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: (selectedService?.featureList ?? [])
                                    .map((feature) => Padding(
                                          padding: EdgeInsets.only(bottom: 8.h),
                                          child: ServiceFeatureWidget(text: feature),
                                        ))
                                    .toList(),
                              ),
                              20.ph,
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text20Bold(
                                        '₹${selectedService?.basePrice ?? ''}',
                                        fontSize: 18.sp,
                                      ),
                                      2.ph,
                                      const Text12Medium(
                                        AppStrings.onwards,
                                      ),
                                    ],
                                  ),
                                  Expanded(child: SizedBox()),
                                  Expanded(
                                    child: PrimaryButton(
                                      onPressed: () {
                                        final service = services.firstWhere(
                                          (service) => service.name == selectedServiceName,
                                          orElse: () => services[0],
                                        );

                                        final continueBookingCubit = context.read<ContinueBookingCubit>();
                                        final continueBookingState = continueBookingCubit.state;
                                        if (continueBookingState is ContinueBookingSuccessState && continueBookingState.bookingDetailsResponseModel != null) {
                                          showDismissContinueBookingDialog(
                                            context,
                                            isComingFromInstant: true,
                                            onConfirmPressed: () {
                                              Navigator.pop(context);
                                              context.read<ContinueBookingCubit>().discardPendingBooking(bookingId: continueBookingState.bookingDetailsResponseModel!.id!);
                                              handleServiceTap(service);
                                            },
                                          );
                                        } else {
                                          handleServiceTap(service);
                                        }
                                      },
                                      child: Text14Bold(
                                        AppStrings.bookNow,
                                        color: colorScheme.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (!isLocationEnabled) ...[
                        20.ph,
                        LocationUnavailableWidget(),
                      ],
                      20.ph,
                    ],
                  ),
                );
              },
            );
          }
          return SizedBox.shrink();
        },
      ),
    );
  }

  void handleServiceTap(QuickBookResponseModel service) {
    final caregiverService = CaregiverServiceModel.convertQuickBookToCaregiverService(service);
    context.read<BookingCubit>().resetBookingRequestModel();
    Navigator.pushNamed(context, AppRoute.instantService, arguments: caregiverService);
  }
}
