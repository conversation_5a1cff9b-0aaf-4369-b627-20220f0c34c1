import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/caregiver_count_cubit.dart';

class CaregiverCountWidget extends StatelessWidget {
  final bool isLocationEnabled;
  const CaregiverCountWidget({super.key, required this.isLocationEnabled});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocBuilder<CaregiverCountCubit, CaregiverCountState>(
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: colorScheme.greenE9F7F2,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isLocationEnabled) ...[
                if(state is CaregiverCountErrorState) SizedBox.shrink(),
                if (state is CaregiverCountLoadingState) ...[
                  Shimmer.fromColors(
                    baseColor: colorScheme.shimmerBaseColor,
                    highlightColor: colorScheme.shimmerHighlightColor,
                    child: Container(
                      width: 80.w,
                      height: 14.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                  8.ph,
                  Shimmer.fromColors(
                    baseColor: colorScheme.shimmerBaseColor,
                    highlightColor: colorScheme.shimmerHighlightColor,
                    child: Container(
                      width: 40.w,
                      height: 12.w,
                      decoration: BoxDecoration(
                        color: colorScheme.shimmerColor,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ],
                if (state is CaregiverCountSuccessState &&
                    state.instantServiceAvailableModel.count != null &&
                    state.instantServiceAvailableModel.count! == 0) ...[
                  Text14Bold(
                    AppStrings.notAvailable,
                    color: colorScheme.grey787878,
                  ),
                ],
                if (state is CaregiverCountSuccessState &&
                    state.instantServiceAvailableModel.count != null &&
                    state.instantServiceAvailableModel.count! > 0) ...[
                  Text14Bold(
                    "${state.instantServiceAvailableModel.count} ${AppStrings.caretakers(state.instantServiceAvailableModel.count!)}",
                    color: colorScheme.success3FB68E,
                  ),
                  Text12SemiBold(
                    AppStrings.nearYou,
                  ),
                ]
              ] else ...[
                Text14Bold(
                  AppStrings.locationNotSelected,
                  color: colorScheme.grey787878,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
