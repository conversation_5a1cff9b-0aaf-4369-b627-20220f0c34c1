import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';

import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class NotServiceableWidget extends StatelessWidget {
  const NotServiceableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.only(left: 20.w, right: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          24.ph,
          Text20Bold(
            AppStrings.ourServiceIsntAvailableInYourLocation,
          ),
          12.ph,
          Text16Medium(AppStrings.weAreWorkingOnExpanding, color: colorScheme.green379F7D,),
          12.ph,
          Image.asset(AppImages.notServiceableImage),
        ],
      ),
    );
  }
}
