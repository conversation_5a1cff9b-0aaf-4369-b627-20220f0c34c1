import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';

class ContinueBookingWidget extends StatelessWidget {
  final BookingDetailsResponseModel bookingDetails;
  final VoidCallback onContinue;
  final VoidCallback onClose;

  const ContinueBookingWidget({
    super.key,
    required this.onContinue,
    required this.onClose,
    required this.bookingDetails,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.blueEFF4FB,
                colorScheme.blueAECAEA,
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
            border: Border.all(
              color: colorScheme.primary.withOpacity(0.5),
              strokeAlign: BorderSide.strokeAlignOutside,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text14Medium(
                      AppStrings.continueBooking,
                    ),
                    4.ph,
                    Text14SemiBold(
                      bookingDetails.subServiceDetails?.name ?? '',
                      fontSize: 16.sp,
                    ),
                  ],
                ),
              ),
              16.pw,
              AppGestureDetector(
                onTap: onContinue,
                child: Container(
                  width: 85.w,
                  height: 35.h,
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Center(
                    child: Text14Bold(
                      AppStrings.continueTxt,
                      color: colorScheme.primary,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),

        Positioned(
          top: -10.h,
          right: 20.w,
          child: AppGestureDetector(
            onTap: onClose,
            child: Container(
              height: 20.h,
              width: 20.w,
              decoration: BoxDecoration(
                color: colorScheme.white,
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: colorScheme.greenD9F0E8,
                  strokeAlign: BorderSide.strokeAlignOutside,
                ),
              ),
              child: Icon(
                Icons.close_rounded,
                size: 16,
                weight: 2,
                color: colorScheme.blue150045,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
