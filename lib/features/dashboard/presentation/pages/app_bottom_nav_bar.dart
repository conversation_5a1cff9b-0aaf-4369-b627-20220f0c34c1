
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/features/dashboard/presentation/pages/dashboard.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../../../../core/services/hive/hive_keys.dart';
import '../../../../core/services/hive/hive_storage_helper.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../my_bookings/presentation/screens/my_booking_screen.dart';
import '../../../profile/bloc/profile_cubit.dart';
import '../../../profile/presentation/screens/profile_screen.dart';
import '../../../verifyOtp/data/auth_response_model.dart';
import '../../bloc/app_bottom_nav_bar_cubit.dart';

class AppBottomNavBar extends StatefulWidget {
  const AppBottomNavBar({super.key});

  @override
  State<AppBottomNavBar> createState() => _AppBottomNavBarState();
}

class _AppBottomNavBarState extends State<AppBottomNavBar> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final userData = HiveStorageHelper.getData<User>(HiveBoxName.user, HiveKeys.userData);
      if (userData == null) {
        context.read<ProfileCubit>().getProfile();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return BlocBuilder<AppBottomNavBarCubit, AppBottomNavBarState>(
      builder: (context, state) {
        final appBottomNavBarCubit = context.read<AppBottomNavBarCubit>();
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (_, _) async {
            appBottomNavBarCubit.handleWillPop();
          },
          child: Scaffold(
            body: PageView(
              clipBehavior: Clip.none,
              controller: appBottomNavBarCubit.pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Dashboard(),
                BookingsScreen(),
                ProfileScreen(),
              ],
            ),

            // The Bottom Navigation Bar
            bottomNavigationBar: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Divider(
                    color: colorScheme.lightGreyDEDEDE,
                    height: 1,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        BottomNavBarItem(
                          title: AppStrings.home,
                          iconPath: AppImages.homeNavIcon,
                          isSelected: state.selectedIndex == 0,
                          onTap: () => appBottomNavBarCubit.changeTab(0),
                        ),
                        BottomNavBarItem(
                          title: AppStrings.bookings,
                          iconPath: AppImages.bookingsNavIcon,
                          isSelected: state.selectedIndex == 1,
                          onTap: () => appBottomNavBarCubit.changeTab(1),
                        ),
                        BottomNavBarItem(
                          title: AppStrings.profile,
                          iconPath: AppImages.profileNavIcon,
                          isSelected: state.selectedIndex == 2,
                          onTap: () => appBottomNavBarCubit.changeTab(2),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class BottomNavBarItem extends StatelessWidget {
  final String title;
  final String iconPath;
  final bool isSelected;
  final VoidCallback onTap;
  const BottomNavBarItem(
      {super.key,
      required this.title,
      required this.iconPath,
      this.isSelected = false,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;
    return AppGestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            iconPath,
            colorFilter: isSelected
                ? ColorFilter.mode(
                    colorScheme.primary,
                    BlendMode.srcIn,
                  )
                : null,
          ),
          Text12SemiBold(
            title,
            color:
                isSelected ? colorScheme.primary : colorScheme.lightGrey61738A,
          ),
        ],
      ),
    );
  }
}
