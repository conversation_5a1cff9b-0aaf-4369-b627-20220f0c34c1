import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/routes/app_routes.dart';
import 'package:homeservice_app/widgets/app_toast.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';
import '../../../common/cms_dropdown_data/bloc/cms_dropdown_data_cubit.dart';
import '../../../common/continue_booking/bloc/continue_booking_cubit.dart';
import '../../../common/feedback/bloc/get_unrated_booking_cubit.dart';
import '../../../common/feedback/bloc/submit_feedback_cubit.dart';
import '../../../common/feedback/data/submit_feedback_req_model.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../my_bookings/bloc/update_booking_bloc.dart';
import '../../../service_booking/bloc/booking_cubit.dart';
import '../../../service_booking/bloc/payment_service_cubit.dart';
import '../../../service_booking/data/model/booking_request_model.dart';
import '../../bloc/is_area_serviceable_cubit.dart';
import '../widgets/address_tile.dart';
import '../widgets/availability_card.dart';
import '../widgets/book_now.dart';
import '../widgets/continue_booking_widget.dart';
import '../widgets/feedback_bottomsheet.dart';
import '../widgets/legup_instant.dart';
import '../widgets/legup_longterm.dart';
import '../widgets/not_serviceable_widget.dart';
import '../widgets/offers_carousel.dart';
import '../widgets/our_caregivers.dart';
import '../widgets/shimmer/legup_instant_shimmer.dart';
import '../widgets/show_dismiss_continue_booking_dialog.dart';

class Dashboard extends StatefulWidget {
  const Dashboard({super.key});

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  @override
  void initState() {
    super.initState();
    context.read<GetUnratedBookingCubit>().getUnratedBooking();
    context.read<MyAddressesBloc>().getAddresses(isGettingFromHome: true);
    context.read<CMSDropdownDataCubit>().getCMSDropDown();
    context.read<ContinueBookingCubit>().getLastBookingDetails();
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: const AddressAppBar(),
      body: MultiBlocListener(
        listeners: [
          BlocListener<GetUnratedBookingCubit, GetUnratedBookingState>(
            listener: (context, state) {
              if (state is GetUnratedBookingSuccessState) {
                if (mounted) {
                  showFeedbackBottomSheet(
                    context,
                    bookingDetailsResponseModel: state.bookingDetailsResponseModel,
                    onClosed: () {
                      Navigator.pop(context);
                      context.read<UpdateBookingBloc>().updateBooking(
                            updateBookingReqModel: BookingRequestModel(
                              popup: false,
                            ),
                            bookingId: state.bookingDetailsResponseModel?.id,
                          );
                    },
                    onSubmit: (selectedReasons, ratingVal, otherReason) {
                      Navigator.pop(context);
                      context.read<SubmitFeedbackCubit>().submitFeedback(
                            submitFeedbackReqModel: SubmitFeedbackReqModel(
                              bookingId:
                                  state.bookingDetailsResponseModel?.id ?? 0,
                              rating: ratingVal.toInt(),
                              listedComment: selectedReasons,
                              customComment: otherReason,
                            ),
                          );
                    },
                  );
                }
              }
            },
          ),
          BlocListener<SubmitFeedbackCubit, SubmitFeedbackState>(
            listener: (context, state) {
              if (state is SubmitFeedbackSuccessState) {
                CustomToast.showToast(
                  message: AppStrings.feedbackSubmitted,
                  isSuccess: true,
                );
              }
            },
          ),
        ],
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                8.ph,
                BlocBuilder<MyAddressesBloc, MyAddressesState>(
                  builder: (context, myAddressesState) {
                    return BlocBuilder<IsAreaServiceableCubit, IsAreaServiceableState>(
                      builder: (context, state) {
                        if (state is IsAreaServiceableSuccessState && myAddressesState is MyAddressesSuccessState) {
                          if (!state.isAreaServiceable) {
                            return const NotServiceableWidget();
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DiscountCarousel(),
                              // PendingActions(),
                              LegupInstant(),
                              LegupLongterm(),
                              BookingScreen(),
                              AvailabilityCard(),
                            ],
                          );
                        }
                        if (state is IsAreaServiceableLoadingState || myAddressesState is MyAddressesLoadingState) {
                          return LegupInstantShimmer();
                        }
                        return const SizedBox.shrink();
                      },
                    );
                  },
                ),
                48.ph,
                Padding(
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text20Bold(
                        AppStrings.onDemandOrLongTerm,
                        lineHeight: 0.85,
                      ),
                      10.ph,
                      Text(
                        AppStrings.weGotYouCovered,
                        style: TextStyle(
                          fontFamily: FontFamily.playwriteIE,
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w400,
                          color: colorScheme.success3FB68E,
                        ),
                      ),
                      12.ph,
                      Container(
                        width: 210.w,
                        height: 1.5.h,
                        color: colorScheme.success3FB68E,
                      ),
                      12.ph,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text20Bold(
                            AppStrings.trainedVerifiedCareGivers,
                            fontSize: 32.sp,
                            color: colorScheme.green329071,
                            lineHeight: 0.97,
                          ),
                          Transform.translate(
                            offset: Offset(2, -8),
                            child: SvgPicture.asset(AppImages.supportImg1),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                48.ph,
                OurCaregivers(),
                20.ph,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          10.ph,
                          Text(
                            AppStrings.madeWithLoveAndEmpathy,
                            style: TextStyle(
                              fontFamily: FontFamily.playwriteIE,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w400,
                              color: colorScheme.success3FB68E,
                            ),
                          ),
                        ],
                      ),
                      SvgPicture.asset(AppImages.supportImg3),
                    ],
                  ),
                ),
                20.ph,
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: BlocBuilder<IsAreaServiceableCubit, IsAreaServiceableState>(
        builder: (context, state) {
          if (state is IsAreaServiceableSuccessState && state.isAreaServiceable) {
            return BlocBuilder<ContinueBookingCubit, ContinueBookingState>(
              builder: (context, state) {
                if (state is ContinueBookingSuccessState) {
                  if (state.bookingDetailsResponseModel == null || state.bookingDetailsResponseModel?.id == null) {
                    return const SizedBox.shrink();
                  }
                  return ContinueBookingWidget(
                    bookingDetails: state.bookingDetailsResponseModel!,
                    onContinue: () {
                      final bookingRequestModel = convertToBookingRequest(state.bookingDetailsResponseModel!);
                      context.read<BookingCubit>().updateBookingRequestModel(bookingRequestModel);
                      context.read<PaymentServiceCubit>().updateBookingResponseModel(state.bookingDetailsResponseModel!);

                      Navigator.pushNamed(context, AppRoute.onDemandBookingType, arguments: state.bookingDetailsResponseModel?.serviceDetails);
                    },
                    onClose: () {
                      showDismissContinueBookingDialog(context, onConfirmPressed: () {
                        Navigator.pop(context);
                        context.read<ContinueBookingCubit>().discardPendingBooking(bookingId: state.bookingDetailsResponseModel!.id!);
                      });
                    },
                  );
                }
                return const SizedBox.shrink();
              },
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  BookingRequestModel convertToBookingRequest(BookingDetailsResponseModel response) {
    return BookingRequestModel(
      serviceType: response.serviceType,
      serviceSubtype: response.serviceSubtype,
      duration: response.duration,
      language: response.language,
      arrivalType: response.arrivalType,
      startDateTime: response.startDateTime,
      addressDetails: response.addressDetails,
      totalAmount: response.totalAmount,
      itemAmount: response.itemAmount,
      gstAmount: response.gstAmount,
      subServiceName: response.subServiceDetails?.name,
      subServiceImage: response.subServiceDetails?.image,
      serviceAttributesIds: response.serviceAttributes,
    );
  }
}
