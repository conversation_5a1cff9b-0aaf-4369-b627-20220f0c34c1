import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
part 'app_bottom_nav_bar_state.dart';

class AppBottomNavBarCubit extends Cubit<AppBottomNavBarState> {
  final PageController pageController = PageController();

  AppBottomNavBarCubit()
      : super(AppBottomNavBarState(
            selectedIndex: 0,));

  void changeTab(int newIndex) {
    if (state.selectedIndex != newIndex) {
      pageController.jumpToPage(newIndex);
      emit(state.copyWith(selectedIndex: newIndex));
    }
  }

  void keepCurrentTab(int newIndex) async {
    final oldIndex = state.selectedIndex;
    emit(state.copyWith(selectedIndex: newIndex));
    await Future.delayed(const Duration(milliseconds: 100));
    emit(state.copyWith(selectedIndex: oldIndex));
  }

  void handleWillPop() {
    if (state.selectedIndex != 0) {
      changeTab(0);
    } else {
      SystemNavigator.pop();
    }
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
