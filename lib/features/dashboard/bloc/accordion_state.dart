part of 'accordion_cubit.dart';

sealed class AccordionState extends Equatable {
  const AccordionState();

  @override
  List<Object> get props => [];
}

final class AccordionInitState extends AccordionState {
  @override
  List<Object> get props => [];
}

final class AccordionLoadingState extends AccordionState {
  @override
  List<Object> get props => [];
}

final class AccordionSuccessState extends AccordionState {
  final List<AccordionResponseModel> accordionList;

  const AccordionSuccessState(this.accordionList);

  @override
  List<Object> get props => [accordionList];
}

final class AccordionErrorState extends AccordionState {
  final String errorMsg;

  const AccordionErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
