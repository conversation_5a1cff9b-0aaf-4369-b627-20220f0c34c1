import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/quick_book_response_model.dart';

part 'quick_book_state.dart';

class QuickBookCubit extends Cubit<QuickBookState> {
  QuickBookCubit() : super(QuickBookInitState());

  Future<void> getQuickBookService() async {
    emit(QuickBookLoadingState());
    final response = await ApiService.instance.getQuickBookServices();

    try {
      if (response.success) {
        final List<QuickBookResponseModel> services = (response.data as List<dynamic>)
                .map((item) => QuickBookResponseModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(QuickBookSuccessState(services));
      } else {
        emit(QuickBookErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(QuickBookErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
