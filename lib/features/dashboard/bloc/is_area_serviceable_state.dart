part of 'is_area_serviceable_cubit.dart';

sealed class IsAreaServiceableState extends Equatable {
  const IsAreaServiceableState();

  @override
  List<Object> get props => [];
}

final class IsAreaServiceableInitState extends IsAreaServiceableState {
  @override
  List<Object> get props => [];
}

final class IsAreaServiceableLoadingState extends IsAreaServiceableState {
  @override
  List<Object> get props => [];
}

final class IsAreaServiceableSuccessState extends IsAreaServiceableState {
  final bool isAreaServiceable;

  const IsAreaServiceableSuccessState(this.isAreaServiceable);

  @override
  List<Object> get props => [isAreaServiceable];
}

final class IsAreaServiceableErrorState extends IsAreaServiceableState {
  final String errorMsg;

  const IsAreaServiceableErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
