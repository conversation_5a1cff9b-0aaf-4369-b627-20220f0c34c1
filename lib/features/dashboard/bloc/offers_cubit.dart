import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/offers_response_model.dart';

part 'offers_state.dart';

class OffersCubit extends Cubit<OffersState> {
  OffersCubit() : super(OffersInitState());

  Future<void> getDashboardOffers() async {
    emit(OffersLoadingState());
    final response = await ApiService.instance.getDashboardOffers();

    try {
      if (response.success) {
        final List<OffersResponseModel> services = (response.data as List<dynamic>)
                .map((item) => OffersResponseModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(OffersSuccessState(services));
      } else {
        emit(OffersErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(OffersErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
