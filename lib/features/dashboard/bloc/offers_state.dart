part of 'offers_cubit.dart';

sealed class OffersState extends Equatable {
  const OffersState();

  @override
  List<Object> get props => [];
}

final class OffersInitState extends OffersState {
  @override
  List<Object> get props => [];
}

final class OffersLoadingState extends OffersState {
  @override
  List<Object> get props => [];
}

final class OffersSuccessState extends OffersState {
  final List<OffersResponseModel> offersList;

  const OffersSuccessState(this.offersList);

  @override
  List<Object> get props => [offersList];
}

final class OffersErrorState extends OffersState {
  final String errorMsg;

  const OffersErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
