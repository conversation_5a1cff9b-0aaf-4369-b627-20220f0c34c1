import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/notifications_model.dart';

part 'inapp_notifications_state.dart';

class InappNotificationsCubit extends Cubit<InappNotificationsState> {
  InappNotificationsCubit() : super(InappNotificationsInitState());

  Future<void> getInappNotifications() async {
    emit(InappNotificationsLoadingState());
    final response = await ApiService.instance.getInAppNotifications();

    try {
      if (response.success) {
        final List<NotificationsModel> notificationsList = (response.data as List<dynamic>)
                .map((item) => NotificationsModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(InappNotificationsSuccessState(notificationsList));
      } else {
        emit(InappNotificationsErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(InappNotificationsErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
