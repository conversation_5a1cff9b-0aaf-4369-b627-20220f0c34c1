part of 'quick_book_cubit.dart';

sealed class <PERSON>BookState extends Equatable {
  const QuickBookState();

  @override
  List<Object> get props => [];
}

final class QuickBookInitState extends QuickBookState {
  @override
  List<Object> get props => [];
}

final class <PERSON>BookLoadingState extends QuickBookState {
  @override
  List<Object> get props => [];
}

final class QuickBookSuccessState extends QuickBookState {
  final List<QuickBookResponseModel> quickBookServiceList;

  const QuickBookSuccessState(this.quickBookServiceList);

  @override
  List<Object> get props => [quickBookServiceList];
}

final class QuickBookErrorState extends QuickBookState {
  final String errorMsg;

  const QuickBookErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
