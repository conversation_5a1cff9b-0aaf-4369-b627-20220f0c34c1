import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/is_area_serviceable_model.dart';
part 'is_area_serviceable_state.dart';

class IsAreaServiceableCubit extends Cubit<IsAreaServiceableState> {
  IsAreaServiceableCubit() : super(IsAreaServiceableInitState());

  void getIsAreaServiceableService({required double lat, required double lng}) async {
    emit(IsAreaServiceableLoadingState());
    try {
    final response = await ApiService.instance.getIsAreaServiceable(lat: lat, lng: lng);
      if (response.success) {
        final IsAreaServiceableModel isAreaServiceable = IsAreaServiceableModel.fromJson(response.data);
        emit(IsAreaServiceableSuccessState(isAreaServiceable.isAreaServiceable));
      } else {
        emit(IsAreaServiceableErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(IsAreaServiceableErrorState(AppStrings.genericErrorMsg));
    }
  }
}
