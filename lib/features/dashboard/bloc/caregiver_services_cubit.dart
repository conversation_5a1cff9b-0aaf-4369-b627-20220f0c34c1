import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/enums/caregiver_service_type.dart';
import '../data/model/caregiver_service_model.dart';

part 'caregiver_services_state.dart';

class CaregiverServicesCubit extends Cubit<CaregiverServicesState> {
  CaregiverServicesCubit() : super(CaregiverServicesInitState());

  Future<void> getCaregiverServicesService({required CaregiverServiceType type}) async {
    emit(CaregiverServicesLoadingState());
    final response = await ApiService.instance.getCaregiverServices(type: type.toString());

    try {
      if (response.success) {
        final List<CaregiverServiceModel> services = response.data is List
            ? (response.data as List<dynamic>).map((x) => CaregiverServiceModel.fromJson(x as Map<String, dynamic>, type)).toList()
            : [];
        emit(CaregiverServicesSuccessState(services));
      } else {
        emit(CaregiverServicesErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(CaregiverServicesErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
