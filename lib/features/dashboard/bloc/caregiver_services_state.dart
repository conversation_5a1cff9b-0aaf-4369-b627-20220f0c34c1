part of 'caregiver_services_cubit.dart';

sealed class CaregiverServicesState extends Equatable {
  const CaregiverServicesState();

  @override
  List<Object> get props => [];
}

final class CaregiverServicesInitState extends CaregiverServicesState {
  @override
  List<Object> get props => [];
}

final class CaregiverServicesLoadingState extends CaregiverServicesState {
  @override
  List<Object> get props => [];
}

final class CaregiverServicesSuccessState extends CaregiverServicesState {
  final List<CaregiverServiceModel> caregiverServiceList;

  const CaregiverServicesSuccessState(this.caregiverServiceList);

  @override
  List<Object> get props => [caregiverServiceList];
}

final class CaregiverServicesErrorState extends CaregiverServicesState {
  final String errorMsg;

  const CaregiverServicesErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
