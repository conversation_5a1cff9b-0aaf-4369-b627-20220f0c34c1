import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../booking_type/data/instant_service_available_model.dart';
import '../../booking_type/data/instant_service_available_request_model.dart';

part 'caregiver_count_state.dart';

class CaregiverCountCubit extends Cubit<CaregiverCountState> {
  CaregiverCountCubit() : super(CaregiverCountInitState());

  Future<void> getCaregiverCountForInstantService({required InstantServiceAvailableReqModel instantServiceAvailableReqModel}) async {
    emit(CaregiverCountLoadingState());

    final response = await ApiService.instance.isInstantServiceAvailable(instantServiceAvailableReqModel: instantServiceAvailableReqModel);

    try {
      if (response.success) {
        final InstantServiceAvailableModel instantServiceAvailableModel = InstantServiceAvailableModel.fromJson(response.data);
        emit(CaregiverCountSuccessState(instantServiceAvailableModel: instantServiceAvailableModel));
      } else {
        emit(CaregiverCountErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(CaregiverCountErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
