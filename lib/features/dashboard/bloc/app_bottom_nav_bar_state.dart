part of 'app_bottom_nav_bar_cubit.dart';

class AppBottomNavBarState extends Equatable {
  final int selectedIndex;

  const AppBottomNavBarState({
    required this.selectedIndex,
  });

  AppBottomNavBarState copyWith({
    int? selectedIndex,
    bool? hasClientTab,
  }) {
    return AppBottomNavBarState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
    );
  }

  @override
  List<Object> get props => [
        selectedIndex,
      ];
}
