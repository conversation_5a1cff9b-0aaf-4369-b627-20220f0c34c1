part of 'caregiver_count_cubit.dart';

sealed class CaregiverCountState extends Equatable {
  const CaregiverCountState();

  @override
  List<Object> get props => [];
}

final class CaregiverCountInitState extends CaregiverCountState {
  @override
  List<Object> get props => [];
}

final class CaregiverCountLoadingState extends CaregiverCountState {
  const CaregiverCountLoadingState();

  @override
  List<Object> get props => [];
}

final class CaregiverCountSuccessState extends CaregiverCountState {
  final InstantServiceAvailableModel instantServiceAvailableModel;

  const CaregiverCountSuccessState({
    required this.instantServiceAvailableModel,
  });

  @override
  List<Object> get props => [instantServiceAvailableModel];
}

final class CaregiverCountErrorState extends CaregiverCountState {
  final String errorMsg;

  const CaregiverCountErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
