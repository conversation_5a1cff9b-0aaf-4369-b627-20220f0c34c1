import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/accordion_response_model.dart';

part 'accordion_state.dart';

class AccordionCubit extends Cubit<AccordionState> {
  AccordionCubit() : super(AccordionInitState());

  Future<void> getAccordionTestimonials() async {
    emit(AccordionLoadingState());
    final response = await ApiService.instance.getAccordionTestimonials();

    try {
      if (response.success) {
        final List<AccordionResponseModel> accordionList = (response.data as List<dynamic>)
                .map((item) => AccordionResponseModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(AccordionSuccessState(accordionList));
      } else {
        emit(AccordionErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(AccordionErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
