part of 'inapp_notifications_cubit.dart';

sealed class InappNotificationsState extends Equatable {
  const InappNotificationsState();

  @override
  List<Object> get props => [];
}

final class InappNotificationsInitState extends InappNotificationsState {
  @override
  List<Object> get props => [];
}

final class InappNotificationsLoadingState extends InappNotificationsState {
  @override
  List<Object> get props => [];
}

final class InappNotificationsSuccessState extends InappNotificationsState {
  final List<NotificationsModel> notificationsList;

  const InappNotificationsSuccessState(this.notificationsList);

  @override
  List<Object> get props => [notificationsList];
}

final class InappNotificationsErrorState extends InappNotificationsState {
  final String errorMsg;

  const InappNotificationsErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
