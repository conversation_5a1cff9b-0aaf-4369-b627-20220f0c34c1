enum CaregiverServiceType {
  longTerm('LT'),
  onDemand('OD');

  final String _name;

  const CaregiverServiceType(this._name);

  @override
  String toString() => _name;

  static CaregiverServiceType? getCaregiverServiceTypeFromString(String? mediaType) {
    switch (mediaType) {
      case 'LT':
        return CaregiverServiceType.longTerm;
      case 'OD':
        return CaregiverServiceType.onDemand;
      default:
        return null;
    }
  }
}
