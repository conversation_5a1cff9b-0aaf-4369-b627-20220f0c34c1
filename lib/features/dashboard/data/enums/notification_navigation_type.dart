import '../../../../utils/string_constants/enum_strings.dart';

enum NotificationNavigationType {
  bookingDetails(EnumStrings.notificationNavigationTypeBookingDetails),
  home(EnumStrings.notificationNavigationTypeHome);

  final String apiValue;

  const NotificationNavigationType(this.apiValue);

  static NotificationNavigationType? getNotificationNavigationTypeFromString(String? notificationNavigationType) {
    switch (notificationNavigationType) {
      case EnumStrings.notificationNavigationTypeBookingDetails:
        return NotificationNavigationType.bookingDetails;
      case EnumStrings.notificationNavigationTypeHome:
        return NotificationNavigationType.home;
      default:
        return null;
    }
  }
}
