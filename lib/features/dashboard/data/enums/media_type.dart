import '../../../../utils/string_constants/app_strings.dart';

enum MediaType {
  image(AppStrings.image),
  video(AppStrings.video);

  final String _name;

  const MediaType(this._name);

  @override
  String toString() => _name;

  static MediaType? getMediaTypeFromString(String? mediaType) {
    switch (mediaType) {
      case AppStrings.image:
        return MediaType.image;
      case AppStrings.video:
        return MediaType.video;
      default:
        return null;
    }
  }
}
