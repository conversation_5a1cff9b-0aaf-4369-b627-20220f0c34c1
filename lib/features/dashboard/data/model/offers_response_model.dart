class OffersResponseModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isActive;
  final String? title;
  final String? subTitle;
  final String? description;
  final String? buttonLabel;
  final String? buttonText;
  final String? buttonLink;
  final String? navigationType;
  final int? order;
  final String? boxColor;
  final String? textColor;
  final int? createdBy;
  final int? updatedBy;

  const OffersResponseModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.isActive,
    this.title,
    this.subTitle,
    this.description,
    this.buttonLabel,
    this.buttonText,
    this.buttonLink,
    this.navigationType,
    this.order,
    this.boxColor,
    this.textColor,
    this.createdBy,
    this.updatedBy,
  });

  factory OffersResponseModel.fromJson(Map<String, dynamic> json) {
    return OffersResponseModel(
      id: json['id'] as int?,
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at']) : null,
      isActive: json['is_active'] as bool?,
      title: json['title'] as String?,
      subTitle: json['sub_title'] as String?,
      description: json['description'] as String?,
      buttonLabel: json['button_label'] as String?,
      buttonText: json['button_text'] as String?,
      buttonLink: json['button_link'] as String?,
      navigationType: json['navigation_type'] as String?,
      order: json['order'] as int?,
      boxColor: json['box_color'] as String?,
      textColor: json['text_color'] as String?,
      createdBy: json['created_by'] as int?,
      updatedBy: json['updated_by'] as int?,
    );
  }
}
