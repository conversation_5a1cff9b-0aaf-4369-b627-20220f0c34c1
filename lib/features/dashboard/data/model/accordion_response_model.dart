import '../enums/media_type.dart';

class AccordionResponseModel {
  final int? id;
  final String? title;
  final String? description;
  final String? media;
  final MediaType? mediaType;
  final DateTime? createdAt;

  const AccordionResponseModel({
    this.id,
    this.title,
    this.description,
    this.media,
    this.mediaType,
    this.createdAt,
  });

  factory AccordionResponseModel.fromJson(Map<String, dynamic> json) {
    return AccordionResponseModel(
      id: json['id'] as int?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      media: json['media'] as String?,
      mediaType: MediaType.getMediaTypeFromString(json['media_type'] as String?),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
    );
  }
}
