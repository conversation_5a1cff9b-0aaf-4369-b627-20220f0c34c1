class QuickBookResponseModel {
  final int? id;
  final String? name;
  final String? image;
  final String? description;
  final String? basePrice;
  final List<String>? featureList;

  const QuickBookResponseModel({
    this.id,
    this.name,
    this.image,
    this.description,
    this.basePrice,
    this.featureList,
  });

  factory QuickBookResponseModel.fromJson(Map<String, dynamic> json) {
    return QuickBookResponseModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      description: json['description'] as String?,
      basePrice: json['base_price'] as String?,
      featureList: (json['feature_list'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );
  }
}
