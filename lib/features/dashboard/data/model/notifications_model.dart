import '../enums/notification_navigation_type.dart';

class NotificationsModel {
  int? id;
  String? trigger;
  String? title;
  String? body;
  bool? isRead;
  ExtraData? extraData;
  NotificationNavigationType? navigationType;
  DateTime? createdAt;

  NotificationsModel({
    this.id,
    this.trigger,
    this.title,
    this.body,
    this.isRead,
    this.extraData,
    this.navigationType,
    this.createdAt,
  });

  factory NotificationsModel.fromJson(Map<String, dynamic> json) {
    return NotificationsModel(
      id: json['id'] as int?,
      trigger: json['trigger'] as String?,
      title: json['title'] as String?,
      body: json['body'] as String?,
      isRead: json['is_read'] as bool?,
      // due to the way the extra_data is received from the method channel, we need to convert it to a Map<String, dynamic> from Map<Object?, Object?>
      extraData: json['extra_data'] != null
          ? ExtraData.fromJson(Map<String, dynamic>.from(json['extra_data']))
          : null,
      navigationType: NotificationNavigationType.getNotificationNavigationTypeFromString(json['navigation_type'] as String?),
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'])?.toLocal() : null,
    );
  }
}

class ExtraData {
  int? bookingId;
  int? paymentId;

  ExtraData({
    this.bookingId,
    this.paymentId,
  });

  factory ExtraData.fromJson(Map<String, dynamic> json) {
    return ExtraData(
      bookingId: json['booking_id'] as int?,
      paymentId: json['payment_id'] as int?,
    );
  }
}
