import '../enums/caregiver_service_type.dart';
import 'quick_book_response_model.dart';

class CaregiverServiceModel {
  final int? id;
  final String? name;
  final String? image;
  final String? description;
  final String? basePrice;
  final CaregiverServiceType? caregiverServiceType;

  const CaregiverServiceModel({
    this.id,
    this.name,
    this.image,
    this.description,
    this.basePrice,
    this.caregiverServiceType,
  });

  factory CaregiverServiceModel.fromJson(Map<String, dynamic> json, CaregiverServiceType? type) {
    return CaregiverServiceModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      description: json['description'] as String?,
      basePrice: json['base_price']?.toString(),
      caregiverServiceType: type,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'description': description,
      'base_price': basePrice,
      'caregiver_service_type': caregiverServiceType?.toString(),
    };
  }

  static CaregiverServiceModel convertQuickBookToCaregiverService(QuickBookResponseModel quickBook) {
    return CaregiverServiceModel(
      id: quickBook.id,
      name: quickBook.name,
      image: quickBook.image,
      description: quickBook.description,
      basePrice: quickBook.basePrice,
      caregiverServiceType: CaregiverServiceType.onDemand,
    );
  }
}
