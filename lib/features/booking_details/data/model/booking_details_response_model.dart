import '../../../dashboard/data/enums/caregiver_service_type.dart';
import '../../../dashboard/data/model/caregiver_service_model.dart';
import '../../../service_booking/data/enums/arrival_time_type.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
import '../../../service_booking/data/enums/transaction_status_type.dart';
import '../../../service_booking/data/model/booking_request_model.dart';
import '../../../service_booking/data/model/sub_service_response_model.dart';

class BookingDetailsResponseModel {
  int? id;
  int? serviceType;
  int? serviceSubtype;
  List<int>? serviceAttributes;
  CaregiverModel? caregiver;
  BookingStatusType? status;
  int? duration;
  String? language;
  ArrivalTimeType? arrivalType;
  DateTime? startDateTime;
  DateTime? startDate;
  DateTime? endDateTime;
  PaymentDetails? paymentDetails;
  String? paymentMethod;
  TransactionStatusType? transactionStatus;
  DateTime? transactionDate;
  int? step;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? totalAmount;
  AddressDetails? addressDetails;
  String? itemAmount;
  String? gstAmount;
  SubServiceResponseModel? subServiceDetails;
  CaregiverServiceModel? serviceDetails;
  String? otp;
  bool? canReview;
  bool? hasReview;
  bool? popup;
  RatingModel? rating;
  String? offerDeductedAmount;

  BookingDetailsResponseModel({
    this.id,
    this.serviceType,
    this.serviceSubtype,
    this.serviceAttributes,
    this.caregiver,
    this.status,
    this.duration,
    this.language,
    this.arrivalType,
    this.startDateTime,
    this.startDate,
    this.endDateTime,
    this.paymentDetails,
    this.paymentMethod,
    this.transactionStatus,
    this.transactionDate,
    this.step,
    this.createdAt,
    this.updatedAt,
    this.totalAmount,
    this.addressDetails,
    this.itemAmount,
    this.gstAmount,
    this.subServiceDetails,
    this.serviceDetails,
    this.otp,
    this.canReview,
    this.hasReview,
    this.popup,
    this.rating,
    this.offerDeductedAmount,
  });

  factory BookingDetailsResponseModel.fromJson(Map<String, dynamic> json) {
    return BookingDetailsResponseModel(
      id: json["id"],
      serviceType: json["service_type"],
      serviceSubtype: json["service_subtype"],
      serviceAttributes: (json["service_attributes"] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      caregiver: json["caregiver"] != null ? CaregiverModel.fromJson(json["caregiver"]) : null,
      status: BookingStatusType.getBookingStatusTypeFromString(json["status"]),
      duration: json["duration"],
      language: json["language"],
      arrivalType: ArrivalTimeType.getArrivalTimeTypeFromString(json["arrival_type"]),
      startDateTime: json["start_date_time"] != null ? DateTime.parse(json["start_date_time"]).toLocal() : null,
      startDate: json["start_date_time"] != null ? onlyStartDate(DateTime.parse(json["start_date_time"]).toLocal()) : null,
      endDateTime: json["end_date_time"] != null ? DateTime.parse(json["end_date_time"]).toLocal() : null,
      paymentDetails: json["payment_details"] != null ? PaymentDetails.fromJson(json["payment_details"]) : null,
      paymentMethod: json["payment_method"],
      transactionStatus: TransactionStatusType.getTransactionStatusTypeFromString(json["transaction_status"]),
      transactionDate: json["transaction_date"] != null ? DateTime.parse(json["transaction_date"]).toLocal() : null,
      step: json["step"],
      createdAt: DateTime.parse(json["created_at"]),
      updatedAt: DateTime.parse(json["updated_at"]),
      totalAmount: json["total_amount"],
      addressDetails: json["address_details"] != null ? AddressDetails.fromJson(json["address_details"]) : null,
      itemAmount: json["item_amount"],
      gstAmount: json["gst_amount"],
      subServiceDetails: SubServiceResponseModel.fromJson(json["sub_service_details"]),
      // TODO: Fix this later
      serviceDetails: CaregiverServiceModel.fromJson(json["service_details"], CaregiverServiceType.onDemand),
      otp: json["otp"],
      canReview: json["can_review"],
      hasReview: json["has_review"],
      popup: json["popup"],
      rating: json["review"] != null ? RatingModel.fromJson(json["review"]) : null,
      offerDeductedAmount: json["offer_deducted_amount"],
    );
  }

 static DateTime? onlyStartDate(DateTime? startDateTime) {
    if (startDateTime == null) return null;
    return DateTime(startDateTime.year, startDateTime.month, startDateTime.day);
  }
}

class PaymentDetails {
  final CardDetails? card;
  final UpiDetails? upi;
  final NetBanking? netBanking;

  PaymentDetails({this.card, this.upi, this.netBanking});

  factory PaymentDetails.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('card')) {
      return PaymentDetails(card: CardDetails.fromJson(json['card']));
    } else if (json.containsKey('upi')) {
      return PaymentDetails(upi: UpiDetails.fromJson(json['upi']));
    } else if (json.containsKey('netbanking')) {
      return PaymentDetails(netBanking: NetBanking.fromJson(json['netbanking']));
    }
    return PaymentDetails();
  }
}

extension PaymentDetailsExtension on PaymentDetails {
  String get displayText {
    if (card != null) {
      final last4 = card!.cardNumber?.substring(card!.cardNumber!.length - 4) ?? '';
      return "Payment method: ${_cardTypeLabel(card!.cardType ?? '')} ending with $last4";
    } else if (upi != null) {
      return "Payment method: UPI via ${upi!.upiId}";
    } else if (netBanking != null) {
      return "Payment method: Net banking via ${netBanking!.bankName}";
    }
    return '';
  }

  String _cardTypeLabel(String cardType) {
    switch (cardType.toLowerCase()) {
      case 'credit_card':
        return 'Credit card';
      case 'debit_card':
        return 'Debit card';
      case 'net_banking':
        return 'Net banking';
      default:
        return '';
    }
  }
}

class CardDetails {
  String? cardNumber;
  String? cardType;

  CardDetails({
    this.cardNumber,
    this.cardType,
  });

  factory CardDetails.fromJson(Map<String, dynamic> json) {
    return CardDetails(
      cardNumber: json["card_number"],
      cardType: json["card_type"],
    );
  }
}

class UpiDetails {
  final String? upiId;

  UpiDetails({
    this.upiId,
  });

  factory UpiDetails.fromJson(Map<String, dynamic> json) {
    return UpiDetails(
      upiId: json["upi_id"],
    );
  }
}

class NetBanking {
  final String? bankName;

  NetBanking({this.bankName});

  factory NetBanking.fromJson(Map<String, dynamic> json) {
    return NetBanking(bankName: json["netbanking_bank_name"]);
  }
}

class CaregiverModel {
  int? id;
  String? fullName;
  String? mobileNumber;
  String? profilePicture;

  CaregiverModel({
    this.id,
    this.fullName,
    this.mobileNumber,
    this.profilePicture,
  });

  factory CaregiverModel.fromJson(Map<String, dynamic> json) {
    return CaregiverModel(
      id: json["id"],
      fullName: json["full_name"] ?? '',
      mobileNumber: json["mobile_number"],
      profilePicture: json["profile_picture"],
    );
  }
}

class RatingModel {
  final int? id;
  final int? rating;
  final String? listedComment;
  final String? customComment;

  RatingModel({
    this.id,
    this.rating,
    this.listedComment,
    this.customComment,
  });

  factory RatingModel.fromJson(Map<String, dynamic> json) {
    return RatingModel(
      id: json["id"],
      rating: json["rating"],
      listedComment: json["listed_comment"],
      customComment: json["custom_comment"],
    );
  }
}