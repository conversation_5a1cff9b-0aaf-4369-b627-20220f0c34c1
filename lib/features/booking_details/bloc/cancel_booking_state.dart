part of 'cancel_booking_cubit.dart';

sealed class CancelBookingState extends Equatable {
  const CancelBookingState();

  @override
  List<Object> get props => [];
}

final class CancelBookingInitState extends CancelBookingState {
  @override
  List<Object> get props => [];
}

final class CancelBookingLoadingState extends CancelBookingState {
  @override
  List<Object> get props => [];
}

final class CancelBookingSuccessState extends CancelBookingState {

  const CancelBookingSuccessState();

  @override
  List<Object> get props => [];
}

final class CancelBookingErrorState extends CancelBookingState {
  final String errorMsg;

  const CancelBookingErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
