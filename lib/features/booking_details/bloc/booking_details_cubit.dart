import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/booking_details_response_model.dart';

part 'booking_details_state.dart';

class BookingDetailsCubit extends Cubit<BookingDetailsState> {
  BookingDetailsCubit() : super(BookingDetailsInitState());

  void getBookingDetailsById({required int bookingId}) async {
    emit(BookingDetailsLoadingState());

    try {
      final response = await ApiService.instance.getBookingDetailsById(bookingId: bookingId);
      if (response.success) {
        final BookingDetailsResponseModel bookingDetailsResponseModel = BookingDetailsResponseModel.fromJson(response.data as Map<String, dynamic>);
        emit(BookingDetailsSuccessState(bookingDetailsResponseModel));
      } else {
        emit(BookingDetailsErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(BookingDetailsErrorState(AppStrings.genericErrorMsg));
    }
  }
}
