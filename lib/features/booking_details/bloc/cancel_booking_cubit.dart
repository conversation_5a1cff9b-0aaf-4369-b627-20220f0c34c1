import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';

part 'cancel_booking_state.dart';

class CancelBookingCubit extends Cubit<CancelBookingState> {
  CancelBookingCubit() : super(CancelBookingInitState());

  void getCancelBookingById({required int bookingId}) async {
    emit(CancelBookingLoadingState());

    try {
      final response = await ApiService.instance.cancelBookingById(bookingId: bookingId);
      if (response.success) {
        emit(CancelBookingSuccessState());
      } else {
        emit(CancelBookingErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(CancelBookingErrorState(AppStrings.genericErrorMsg));
    }
  }
}
