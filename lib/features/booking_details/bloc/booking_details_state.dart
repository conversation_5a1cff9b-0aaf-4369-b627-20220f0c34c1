part of 'booking_details_cubit.dart';

sealed class BookingDetailsState extends Equatable {
  const BookingDetailsState();

  @override
  List<Object> get props => [];
}

final class BookingDetailsInitState extends BookingDetailsState {
  @override
  List<Object> get props => [];
}

final class BookingDetailsLoadingState extends BookingDetailsState {
  @override
  List<Object> get props => [];
}

final class BookingDetailsSuccessState extends BookingDetailsState {
  final BookingDetailsResponseModel bookingDetailsResponseModel;

  const BookingDetailsSuccessState(this.bookingDetailsResponseModel);

  @override
  List<Object> get props => [bookingDetailsResponseModel];
}

final class BookingDetailsErrorState extends BookingDetailsState {
  final String errorMsg;

  const BookingDetailsErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
