import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../widgets/error_state_widget.dart';
import '../../service_booking/data/enums/booking_status_type.dart';
import '../bloc/booking_details_cubit.dart';
import 'widgets/booking_address_widget.dart';
import 'widgets/booking_details_app_bar.dart';
import 'widgets/booking_details_shimmer.dart';
import 'widgets/booking_help_widget.dart';
import 'widgets/booking_payment_summary_widget.dart';
import 'widgets/cancellation_timer_widget.dart';
import 'widgets/caregiver_details_widget.dart';
import 'widgets/service_details_widget.dart';

class BookingDetailsScreen extends StatefulWidget {
  final int bookingId;

  const BookingDetailsScreen({
    super.key,
    required this.bookingId,
  });

  @override
  State<BookingDetailsScreen> createState() => _BookingDetailsScreenState();
}

class _BookingDetailsScreenState extends State<BookingDetailsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<BookingDetailsCubit>().getBookingDetailsById(bookingId: widget.bookingId);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<BookingDetailsCubit, BookingDetailsState>(
          builder: (context, state) {
            if (state is BookingDetailsLoadingState) {
              return const BookingDetailsShimmer();
            }
            if (state is BookingDetailsErrorState) {
              return Center(
                child: ErrorStateWidget(
                  height: 270.h,
                  errorMessage: state.errorMsg,
                  onRetry: () {
                    context.read<BookingDetailsCubit>().getBookingDetailsById(bookingId: widget.bookingId);
                  },
                ),
              );
            }
            if (state is BookingDetailsSuccessState) {
              final bookingDetails = state.bookingDetailsResponseModel;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BookingDetailsAppBar(
                    bookingId: bookingDetails.id ?? 0,
                    bookingDateTime: bookingDetails.transactionDate,
                  ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async {
                        context.read<BookingDetailsCubit>().getBookingDetailsById(bookingId: widget.bookingId);
                      },
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ServiceDetailsWidget(bookingDetails: bookingDetails),
                            Builder(
                              builder: (context) {
                                final status = bookingDetails.status;
                                final dividerColor = (status == BookingStatusType.pending || status == BookingStatusType.booked)
                                    ? colorScheme.orangeEB8F47
                                    : (status == BookingStatusType.started || status == BookingStatusType.confirmed || status == BookingStatusType.completed)
                                        ? colorScheme.success3FB68E
                                        : colorScheme.error;
                                return Container(
                                  color: dividerColor,
                                  height: 2,
                                );
                              },
                            ),
                            Container(
                              color: colorScheme.surface,
                              height: 8,
                            ),
                            if (bookingDetails.status == BookingStatusType.pending || bookingDetails.status == BookingStatusType.booked)
                              CancellationTimerWidget(bookingDetails: bookingDetails),
                            if (bookingDetails.status != BookingStatusType.cancelled && bookingDetails.status != BookingStatusType.failed) ...[
                              CaregiverDetailsWidget(bookingDetails: bookingDetails),
                              Container(
                                color: colorScheme.surface,
                                height: 8,
                              ),
                            ],
                            BookingAddressWidget(bookingDetails: bookingDetails),
                            Container(
                              color: colorScheme.surface,
                              height: 8,
                            ),
                            BookingPaymentSummaryWidget(bookingDetails: bookingDetails),
                            Container(
                              color: colorScheme.surface,
                              height: 8,
                            ),
                            BookingHelpWidget(),
                            Container(
                              color: colorScheme.surface,
                              height: 8,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
