import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
import '../../../service_booking/data/enums/transaction_status_type.dart';
import '../../data/model/booking_details_response_model.dart';
import '../../utils/invoice_generator.dart';

class BookingPaymentSummaryWidget extends StatelessWidget {
  final BookingDetailsResponseModel bookingDetails;

  const BookingPaymentSummaryWidget({
    super.key,
    required this.bookingDetails,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (bookingDetails.transactionStatus == TransactionStatusType.refundInitiated ||
              bookingDetails.transactionStatus == TransactionStatusType.refundCompleted) ...[
            Builder(builder: (context) {
              if (bookingDetails.transactionStatus == TransactionStatusType.refundInitiated) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SvgPicture.asset(
                        AppImages.infoRedIcon,
                        width: 16.w,
                        height: 16.h,
                        colorFilter: ColorFilter.mode(
                          colorScheme.blue140042,
                          BlendMode.srcIn,
                        ),
                      ),
                      8.pw,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text14SemiBold(
                              AppStrings.refundInitiated,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            4.ph,
                            Text14SemiBold(
                              AppStrings.refundInitiatedDescription,
                              color: colorScheme.lightGrey6B6B6B,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }
              if (bookingDetails.transactionStatus == TransactionStatusType.refundCompleted) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    color: colorScheme.greenE9F7F2,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SvgPicture.asset(
                        AppImages.checkGreenIcon,
                        width: 16.w,
                        height: 16.h,
                        colorFilter: ColorFilter.mode(
                          colorScheme.blue140042,
                          BlendMode.srcIn,
                        ),
                      ),
                      4.pw,
                      Expanded(
                        child: Text14SemiBold(
                          AppStrings.refundCompletedDescription,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }
              return SizedBox.shrink();
            }),
            16.ph,
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text14Bold(
                bookingDetails.status == BookingStatusType.failed ? AppStrings.totalAmount : AppStrings.totalPaid,
                fontSize: 16.sp,
                color: colorScheme.blue140042,
              ),
              Text14Bold(
                "₹${bookingDetails.totalAmount}",
                fontSize: 16.sp,
              ),
            ],
          ),
          4.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text14SemiBold(
                AppStrings.itemTotal,
                color: colorScheme.lightGrey6B6B6B,
              ),
              Text14SemiBold(
                "₹${bookingDetails.itemAmount}",
                color: colorScheme.lightGrey6B6B6B,
              ),
            ],
          ),
          4.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text14SemiBold(
                AppStrings.gst18,
                color: colorScheme.lightGrey6B6B6B,
              ),
              Text14SemiBold(
                "₹${bookingDetails.gstAmount}",
                color: colorScheme.lightGrey6B6B6B,
              ),
            ],
          ),
          if (bookingDetails.offerDeductedAmount != null) ...[
            4.ph,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text14SemiBold(
                  AppStrings.couponDiscount,
                  color: colorScheme.success3FB68E,
                ),
                Text14SemiBold(
                  "-₹${bookingDetails.offerDeductedAmount}",
                  color: colorScheme.success3FB68E,
                ),
              ],
            ),
          ],
          Divider(color: colorScheme.lightGreyDEDEDE),
          if (bookingDetails.paymentDetails != null && bookingDetails.paymentDetails!.displayText.isNotEmpty) ...[
            8.ph,
            Text14SemiBold(
              bookingDetails.paymentDetails?.displayText ?? '',
              color: colorScheme.success3FB68E,
            ),
          ],
          if (bookingDetails.status == BookingStatusType.completed) ...[
            8.ph,
            CustomOutlinedButton(
              width: 175.w,
              onPressed: () {
                InvoiceGenerator.generateAndSavePDF(context, bookingDetails);
              },
              buttonWidget: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(AppImages.downloadIcon),
                  4.pw,
                  Text14Bold(
                    AppStrings.downloadInvoice,
                    color: colorScheme.primary,
                  ),
                ],
              ),
            ),    
          ],
        ],
      ),
    );
  }
}
