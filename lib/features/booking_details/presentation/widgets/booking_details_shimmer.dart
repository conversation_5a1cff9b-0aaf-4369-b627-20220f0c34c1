import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/theme/app_theme.dart';

class BookingDetailsShimmer extends StatefulWidget {
  const BookingDetailsShimmer({super.key});

  @override
  State<BookingDetailsShimmer> createState() => _BookingDetailsShimmerState();
}

class _BookingDetailsShimmerState extends State<BookingDetailsShimmer> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final shimmerBaseColor = colorScheme.shimmerBaseColor;
    final shimmerHighlightColor = colorScheme.shimmerHighlightColor;

    return Shimmer.fromColors(
      baseColor: shimmerBaseColor,
      highlightColor: shimmerHighlightColor,
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      _buildPlaceholder(height: 18.h, width: 150.w),
                      4.ph,
                      _buildPlaceholder(height: 14.h, width: 160.w),
                    ],
                  ),
                  _buildPlaceholder(height: 16.h, width: 50.w),
                ],
              ),
            ),

            8.ph,

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 7.h).copyWith(bottom: 28.h),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _buildPlaceholder(
                        height: 32.r,
                        width: 32.r,
                        shape: BoxShape.circle,
                      ),
                      8.pw,
                      _buildPlaceholder(
                        height: 18.h,
                        width: 150.w,
                      ),
                      Spacer(),
                      _buildPlaceholder(
                        height: 30.h,
                        width: 70.w,
                        borderRadius: 12.r,
                      ),
                    ],
                  ),
                  14.ph,

                  Row(
                    children: [
                      _buildPlaceholder(height: 16.h, width: 80.w),
                      8.pw,
                      Expanded(
                        child: _buildPlaceholder(
                          height: 16.h,
                          width: 120.w,
                        ),
                      ),
                    ],
                  ),
                  4.ph,

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildPlaceholder(height: 16.h, width: 80.w),
                      _buildPlaceholder(height: 16.h, width: 70.w),
                    ],
                  ),
                  4.ph,

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPlaceholder(height: 16.h, width: 50.w),
                      const Spacer(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          _buildPlaceholder(
                            height: 16.h,
                            width: 100.w,
                          ),
                          4.ph,
                          _buildPlaceholder(
                            height: 16.h,
                            width: 100.w,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: Colors.grey.shade300,
              indent: 16.w,
              endIndent: 16.w,
            ),
            15.ph,

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPlaceholder(height: 18.h, width: 150.w),
                        8.ph,
                        _buildPlaceholder(height: 14.h, width: 60.w),
                        6.ph,
                        _buildPlaceholder(height: 12.h, width: double.infinity),
                        4.ph,
                        _buildPlaceholder(height: 12.h, width: 200.w),
                      ],
                    ),
                  ),
                  16.pw,
                  _buildPlaceholder(height: 20.h, width: 60.w),
                ],
              ),
            ),
            20.ph,

            Divider(
              height: 8,
              thickness: 8,
              color: Colors.grey.shade200,
            ), // Thicker separator

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Row(
                children: [
                  _buildPlaceholder(
                      height: 30.h, width: 30.w, shape: BoxShape.circle),
                  12.pw,
                  Expanded(
                    child:
                        _buildPlaceholder(height: 14.h, width: double.infinity),
                  ),
                ],
              ),
            ),

            Divider(height: 8, thickness: 8, color: Colors.grey.shade200),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPlaceholder(height: 24.h, width: 24.w),
                  12.pw,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPlaceholder(height: 18.h, width: 60.w),
                        8.ph,
                        _buildPlaceholder(height: 12.h, width: double.infinity),
                        4.ph,
                        _buildPlaceholder(height: 12.h, width: 150.w),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            Divider(height: 8, thickness: 8, color: Colors.grey.shade200),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPlaceholder(height: 18.h, width: 100.w),
                  16.ph,
                  _buildRowPlaceholder(width1: 80.w, width2: 60.w),
                  8.ph,
                  _buildRowPlaceholder(width1: 70.w, width2: 60.w),
                  8.ph,
                  _buildRowPlaceholder(width1: 150.w, width2: 50.w),
                  8.ph,
                  _buildRowPlaceholder(width1: 120.w, width2: 50.w),
                  12.ph,
                  Divider(height: 1, thickness: 1, color: Colors.grey.shade300),
                  12.ph,
                  _buildRowPlaceholder(
                    width1: 80.w,
                    width2: 80.w,
                    height1: 16.h,
                    height2: 16.h,
                  ), // Total amount (bolder)
                  const SizedBox(height: 16),
                  _buildPlaceholder(height: 14.h, width: 250.w),
                  const SizedBox(height: 20),
                  Center(
                    child: _buildPlaceholder(
                      height: 45,
                      width: 200,
                      borderRadius: 25,
                    ),
                  ),
                ],
              ),
            ),

            Divider(height: 8, thickness: 8, color: Colors.grey.shade200),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPlaceholder(height: 18.h, width: 200.w),
                  8.ph,
                  _buildPlaceholder(height: 14.h, width: double.infinity),
                  16.ph,
                  Center(
                    child: _buildPlaceholder(
                      height: 45,
                      width: 150,
                      borderRadius: 25,
                    ),
                  ),
                ],
              ),
            ),
            20.ph,
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder({
    double? height,
    double? width,
    double borderRadius = 4.0,
    EdgeInsetsGeometry margin = EdgeInsets.zero,
    BoxShape shape = BoxShape.rectangle,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      height: height,
      width: width,
      margin: margin,
      decoration: BoxDecoration(
        color: colorScheme.shimmerColor,
        borderRadius: shape == BoxShape.rectangle
            ? BorderRadius.circular(borderRadius)
            : null,
        shape: shape,
      ),
    );
  }

  Widget _buildRowPlaceholder({
    double? width1,
    double? width2,
    double height1 = 14,
    double height2 = 14,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildPlaceholder(height: height1, width: width1),
        _buildPlaceholder(height: height2, width: width2),
      ],
    );
  }
}
