import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../service_booking/data/enums/arrival_time_type.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
import '../../data/model/booking_details_response_model.dart';

class ServiceDetailsWidget extends StatelessWidget {
  final BookingDetailsResponseModel bookingDetails;

  const ServiceDetailsWidget({
    super.key,
    required this.bookingDetails,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 14.h).copyWith(bottom: 28.h),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 16.r,
                backgroundColor: colorScheme.lightGrey6B6B6B,
                backgroundImage: bookingDetails.serviceDetails?.image != null ? NetworkImage(bookingDetails.serviceDetails?.image ?? '') : null,
              ),
              8.pw,
              Expanded(
                child: Text14Bold(
                  bookingDetails.serviceDetails?.name ?? '',
                  color: colorScheme.blue140042,
                  fontSize: 16.sp,
                ),
              ),
              10.pw,
              Builder(
                builder: (context) {
                  final status = bookingDetails.status;
                  final boxColor = (status == BookingStatusType.pending || status == BookingStatusType.booked)
                      ? colorScheme.orangeFCEEE3
                      : (status == BookingStatusType.started || status == BookingStatusType.confirmed || status == BookingStatusType.completed)
                          ? colorScheme.greenE9F7F2
                          : colorScheme.redFFE0E0;
                  final textColor = (status == BookingStatusType.pending || status == BookingStatusType.booked)
                      ? colorScheme.orangeEB8F47
                      : (status == BookingStatusType.started || status == BookingStatusType.confirmed || status == BookingStatusType.completed)
                          ? colorScheme.success3FB68E
                          : colorScheme.error;
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: boxColor,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text14Bold(
                      bookingDetails.status?.apiValue ?? '',
                      color: textColor,
                    ),
                  );
                },
              ),
            ],
          ),
          14.ph,
          Row(
            children: [
              Text14SemiBold(
                AppStrings.speciality,
                color: colorScheme.lightGrey6B6B6B,
              ),
              24.pw,
              Expanded(
                child: Text14SemiBold(
                  bookingDetails.subServiceDetails?.name ?? '',
                  color: colorScheme.lightGrey6B6B6B,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
          4.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text14SemiBold(
                AppStrings.language,
                color: colorScheme.lightGrey6B6B6B,
              ),
              Text14SemiBold(
                bookingDetails.language ?? '',
                color: colorScheme.lightGrey6B6B6B,
              ),
            ],
          ),
          4.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text14SemiBold(
                AppStrings.type,
                color: colorScheme.lightGrey6B6B6B,
              ),
              Expanded(
                child: Text14SemiBold(
                  getServiceTypeText(bookingDetails.arrivalType),
                  color: colorScheme.lightGrey6B6B6B,
                  textAlign: TextAlign.end,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String getServiceTypeText(ArrivalTimeType? arrivalType) {
    if (arrivalType == ArrivalTimeType.instant) return AppStrings.instant15mins;
    if (arrivalType == ArrivalTimeType.schedule && bookingDetails.startDateTime != null && bookingDetails.endDateTime != null) {
      return formatSchedule(bookingDetails.startDateTime!.toLocal(), bookingDetails.endDateTime!.toLocal());
    }
    return '';
  }

  String formatSchedule(DateTime start, DateTime end) {
    final datePart = DateFormat("dd MMMM yyyy").format(end);

    final startTime = DateFormat("h:mm a").format(start);
    final endTime = DateFormat("h:mm a").format(end);

    return "${AppStrings.scheduledFor}\n$datePart, $startTime - $endTime";
  }
}
