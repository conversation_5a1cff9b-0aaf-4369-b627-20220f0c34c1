import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';

import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../data/model/booking_details_response_model.dart';

class BookingAddressWidget extends StatelessWidget {
  final BookingDetailsResponseModel bookingDetails;

  const BookingAddressWidget({
    super.key,
    required this.bookingDetails,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(AppImages.homeIcon),
          8.pw,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text14Bold(
                  bookingDetails.addressDetails?.title ?? '',
                  fontSize: 16.sp,
                ),
                4.ph,
                Text14SemiBold(
                  bookingDetails.addressDetails?.address ?? '',
                  color: colorScheme.lightGrey6B6B6B,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
