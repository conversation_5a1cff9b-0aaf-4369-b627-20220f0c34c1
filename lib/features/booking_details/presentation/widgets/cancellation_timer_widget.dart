import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/booking_details_cubit.dart';
import '../../bloc/cancel_booking_cubit.dart';
import '../../data/model/booking_details_response_model.dart';
import 'cancel_booking_bottom_sheet.dart';

class CancellationTimerWidget extends StatefulWidget {
  final BookingDetailsResponseModel bookingDetails;
  const CancellationTimerWidget({super.key, required this.bookingDetails});

  @override
  State<CancellationTimerWidget> createState() => _CancellationTimerWidgetState();
}

class _CancellationTimerWidgetState extends State<CancellationTimerWidget> {
  Timer? _timer;
  final ValueNotifier<int> _remainingSeconds = ValueNotifier<int>(0);
  final ValueNotifier<bool> _showCancellationSection = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _initializeTimerBasedOnTransactionDate();
  }

  void _initializeTimerBasedOnTransactionDate() {
    final transactionDate = widget.bookingDetails.transactionDate;

    if (transactionDate == null) {
      _showCancellationSection.value = false;
      return;
    }

    final cancellationEndTime = transactionDate.add(const Duration(seconds: 60));
    final now = DateTime.now();
    final difference = cancellationEndTime.difference(now);

    if (!difference.isNegative && difference.inSeconds > 0) {
      _remainingSeconds.value = difference.inSeconds;
      _showCancellationSection.value = true;
      startTimer();
    } else {
      _showCancellationSection.value = false;
    }
  }

  void startTimer() {
    _timer?.cancel();
    if (_remainingSeconds.value <= 0) {
      _showCancellationSection.value = false;
      return;
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      if (_remainingSeconds.value > 0) {
        _remainingSeconds.value--;
      } else {
        timer.cancel();
        _showCancellationSection.value = false;
      }
    });
  }

  String formatTime(int secondsToShow) {
    int minutes = secondsToShow ~/ 60;
    int seconds = secondsToShow % 60;
    return "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return BlocListener<CancelBookingCubit, CancelBookingState>(
      listener: (context, state) {
        if (state is CancelBookingLoadingState) {
          Dialogs.showLoadingDialog(context);
        }
        if (state is CancelBookingSuccessState) {
          Navigator.pop(context);
          CustomToast.showToast(message: AppStrings.bookingCancelledSuccessfully);
          context.read<BookingDetailsCubit>().getBookingDetailsById(bookingId: widget.bookingDetails.id ?? 0);
          _timer?.cancel();
          _showCancellationSection.value = false;
        }
        if (state is CancelBookingErrorState) {
          Navigator.pop(context);
          CustomToast.showToast(message: state.errorMsg);
        }
      },
      child: ValueListenableBuilder<bool>(
        valueListenable: _showCancellationSection,
        builder: (context, showSection, _) {
          return AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: showSection
                ? Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text14Bold(
                                  AppStrings.changedYourMind,
                                  fontSize: 16.sp,
                                  color: colorScheme.blue140042,
                                ),
                                AppGestureDetector(
                                  onTap: () {
                                    showCancelBookingBottomSheet(
                                      context,
                                      onConfirmCancel: () {
                                        Navigator.pop(context);
                                        context.read<CancelBookingCubit>().getCancelBookingById(bookingId: widget.bookingDetails.id ?? 0);
                                      },
                                    );
                                  },
                                  child: Text14Bold(
                                    AppStrings.cancel,
                                    color: colorScheme.error,
                                  ),
                                ),
                              ],
                            ),
                            8.ph,
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ValueListenableBuilder<int>(
                                  valueListenable: _remainingSeconds,
                                  builder: (context, seconds, _) {
                                    return Text14SemiBold(
                                      formatTime(seconds),
                                      color: colorScheme.primary,
                                      textAlign: TextAlign.justify,
                                    );
                                  },
                                ),
                                10.pw,
                                Expanded(
                                  child: Text14SemiBold(
                                    AppStrings.cancelBookingTimerDescription,
                                    color: colorScheme.lightGrey6B6B6B,
                                  ),
                                ),
                                2.pw,
                              ],
                            ),
                          ],
                        ),
                      ),
                      Container(
                        color: colorScheme.surface,
                        height: 8,
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _remainingSeconds.dispose();
    _showCancellationSection.dispose();
    super.dispose();
  }
}
