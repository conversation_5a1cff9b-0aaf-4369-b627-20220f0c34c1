import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/texts/app_text.dart';

class BookingHelpWidget extends StatelessWidget {
  const BookingHelpWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text14Bold(
            AppStrings.needHelpWithBooking,
            fontSize: 16.sp,
            color: colorScheme.blue140042,
          ),
          4.ph,
          Text14Medium(
            AppStrings.ourServiceExpertIsJustOneCallAway,
            color: colorScheme.blue140042,
          ),
          12.ph,
          CustomOutlinedButton(
            width: 132.w,
            onPressed: () {
              launchUrl(Uri.parse('tel:${AppConfig.getInstance().metadata.contactNumber}'));
            },
            buttonWidget: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(AppImages.callIc),
                4.pw,
                Text14Bold(
                  AppStrings.contactUs,
                  color: colorScheme.primary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
