import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/texts/app_text.dart';

void showCancelBookingBottomSheet(
  BuildContext context, {
  required VoidCallback onConfirmCancel,
}) {
  showModalBottomSheet(
    context: context,
    isDismissible: false,
    enableDrag: false,
    isScrollControlled: true,
    useSafeArea: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    builder: (BuildContext ctx) {
      final colorScheme = Theme.of(ctx).colorScheme;
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 32.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: colorScheme.redFFE0E0,
                    child: Center(
                      child: Text(
                        '!',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.redD60000,
                        ),
                      ),
                    ),
                  ),
                  AppGestureDetector(
                    onTap: () => Navigator.pop(ctx),
                    child: Icon(
                      Icons.close_rounded,
                      color: colorScheme.blue150045,
                    ),
                  ),
                ],
              ),
              20.ph,
              Text20Bold(
                AppStrings.cancelBooking,
                textAlign: TextAlign.center,
              ),
              8.ph,
              Text14Medium(
                AppStrings.cancelBookingDescription,
                color: colorScheme.blue140042,
              ),
              20.ph,
              PrimaryButton(
                width: 196.w,
                onPressed: () {
                  Navigator.pop(ctx);
                },
                child: Text14Bold(
                  AppStrings.dontCancel,
                  fontSize: 16.sp,
                  color: colorScheme.white,
                ),
              ),
              20.ph,
              CustomOutlinedButton(
                width: 196.w,
                height: 48.h,
                onPressed: onConfirmCancel,
                buttonWidget: Text14Bold(
                  AppStrings.cancelBookingButton,
                  fontSize: 16.sp,
                  color: colorScheme.primary,
                ),
              ),
              20.ph,
            ],
          ),
        ),
      );
    },
  );
}
