import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:number_to_words_english/number_to_words_english.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

import '../../../../core/services/hive/hive_keys.dart';
import '../../../../core/services/hive/hive_storage_helper.dart';
import '../../../verifyOtp/data/auth_response_model.dart';
import '../../data/model/booking_details_response_model.dart';

Future<pw.Document> generateInvoicePdfBytes(BookingDetailsResponseModel bookingDetails) async {
  final pdf = pw.Document();

  final borderColor = PdfColors.grey300;

  pw.Widget labeledRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(width: 150, child: pw.Text(label, style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
          pw.Text(": "),
          pw.Expanded(child: pw.Text(value)),
        ],
      ),
    );
  }

  pw.Widget sectionTitle(String title) => pw.Padding(
        padding: const pw.EdgeInsets.symmetric(vertical: 8),
        child: pw.Text(title, style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
      );


  final fontDataRegular = await rootBundle.load('assets/fonts/NunitoSans-Regular.ttf');
  final ttfRegular = pw.Font.ttf(fontDataRegular.buffer.asByteData());

  final fontDataBold = await rootBundle.load('assets/fonts/NunitoSans-Bold.ttf');
  final ttfBold = pw.Font.ttf(fontDataBold.buffer.asByteData());

  final fallbackFont = await PdfGoogleFonts.notoSansRegular();

  final userData = HiveStorageHelper.getData<User>(HiveBoxName.user, HiveKeys.userData);

  final cgst = (double.tryParse(bookingDetails.gstAmount ?? "0") ?? 0) / 2;
  final sgst = (double.tryParse(bookingDetails.gstAmount ?? "0") ?? 0) / 2;

  pdf.addPage(
    pw.MultiPage(
      theme: pw.ThemeData.withFont(
        base: ttfRegular,
        bold: ttfBold,
        fontFallback: [fallbackFont],
      ),
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(16),
      build: (context) => [
        sectionTitle("INSTATAIL PRODUCTS PRIVATE LIMITED"),
        labeledRow("CIN", "U47990KL2024PTC087310"),
        labeledRow("GSTIN", "29AAHCI6412J1ZC"),
        pw.Text("Ananthapuri House, Alappuzha, Kerala, India - 688562"),

        pw.SizedBox(height: 20),
        sectionTitle("Invoice Details"),
        labeledRow("Invoice #", bookingDetails.id?.toString() ?? ""),
        labeledRow(
          "Issue Date",
          bookingDetails.endDateTime != null
              ? DateFormat.yMMMMd().format(bookingDetails.endDateTime!)
              : 'N/A',
        ),
        labeledRow("Place of Supply", "Karnataka"),
        labeledRow("Status", "Paid"),

        pw.SizedBox(height: 20),
        sectionTitle("Billed To"),
        labeledRow("Customer ID", userData?.id.toString() ?? ""),
        if (userData?.name != null) labeledRow("Name", userData?.name ?? ""),
        labeledRow("Phone", userData?.mobileNumber ?? ""),
        labeledRow("Billing Address", bookingDetails.addressDetails?.address ?? ""),
        labeledRow("Shipping Address", bookingDetails.addressDetails?.address ?? ""),

        pw.SizedBox(height: 20),
        sectionTitle("Description of Goods/Services"),
        pw.Table(
          border: pw.TableBorder.all(color: borderColor),
          columnWidths: {
            0: const pw.FlexColumnWidth(4),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Description", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Rate / hour", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Service Duration", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Total (Excl. Tax)", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text(bookingDetails.subServiceDetails?.name ?? "")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("₹${bookingDetails.subServiceDetails?.price ?? 0}")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text(bookingDetails.duration?.toString() ?? "")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("₹${bookingDetails.itemAmount ?? 0}")),
              ],
            ),
          ],
        ),

        pw.SizedBox(height: 20),
        sectionTitle("Tax Details"),
        pw.Table(
          border: pw.TableBorder.all(color: borderColor),
          columnWidths: {
            0: const pw.FlexColumnWidth(4),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(2),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Tax Type", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Rate", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("Amount", style: pw.TextStyle(fontWeight: pw.FontWeight.bold))),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("CGST")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("9%")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("₹$cgst")),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("SGST")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("9%")),
                pw.Padding(padding: const pw.EdgeInsets.all(8), child: pw.Text("₹$sgst")),
              ],
            ),
          ],
        ),

        pw.SizedBox(height: 20),
        sectionTitle("Amount Summary"),
        pw.Table(
          border: pw.TableBorder.all(color: borderColor),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(5),
          },
          children: [
            pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("Sub Total (Excl. Tax)", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("₹${bookingDetails.itemAmount ?? 0}"),
                ),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("Total Tax", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("₹${bookingDetails.gstAmount ?? 0}"),
                ),
              ],
            ),
            if (bookingDetails.offerDeductedAmount != null)
              pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text("Coupon discount", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text("-₹${bookingDetails.offerDeductedAmount ?? 0}"),
                  ),
                ],
              ),
            pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("Total", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("₹${bookingDetails.totalAmount ?? 0}"),
                ),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("Amount Paid", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("₹${bookingDetails.totalAmount ?? 0}"),
                ),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("Amount Due", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("₹0.00"),
                ),
              ],
            ),
            pw.TableRow(
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text("In Words:", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(convertAmountToWords(bookingDetails.totalAmount?.toString() ?? "0"), style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
              ],
            ),
          ],
        ),

        pw.SizedBox(height: 8),
        pw.Text("In Words: ${convertAmountToWords(bookingDetails.totalAmount?.toString() ?? "0")}", style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),

        pw.SizedBox(height: 20),
        sectionTitle("Notes"),
        pw.Text("Customer Notes:\n"),
        pw.Text("Terms and Conditions:\n"),
      ],
    ),
  );

  return pdf;
}

String convertAmountToWords(String? amount) {
  final amountValue = double.tryParse(amount ?? "0");
  if (amountValue == null || amountValue < 0) {
    return "Invalid Amount";
  }
  if (amountValue == 0) {
    return "Zero Rupees Only /-";
  }

  int rupees = amountValue.truncate();
  // Handle potential floating point inaccuracies for paise
  int paise = ((amountValue - rupees) * 100).round();

  String words = "";

  if (rupees > 0) {
    words += "${_capitalizeFirstLetter(NumberToWordsEnglish.convert(rupees))} Rupees";
  }

  if (paise > 0) {
    if (rupees > 0) {
      words += " and ";
    }
    words += "${_capitalizeFirstLetter(NumberToWordsEnglish.convert(paise))} Paise";
  }

  return "$words Only /-";
}

String _capitalizeFirstLetter(String text) {
  if (text.isEmpty) return text;
  return text[0].toUpperCase() + text.substring(1).toLowerCase();
}
