import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class BookingDetailsAppBar extends StatelessWidget {
  final int bookingId;
  final DateTime? bookingDateTime;

  const BookingDetailsAppBar({
    super.key,
    required this.bookingId,
    this.bookingDateTime,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                    } else {
                      SystemNavigator.pop();
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(top: bookingDateTime != null ? 4.h : 2.h),
                    child: SvgPicture.asset(
                      AppImages.leftArrowIc,
                      colorFilter: ColorFilter.mode(
                        colorScheme.blue150045,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
                8.pw,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text20Bold(AppStrings.bookingDetails),
                    if (bookingDateTime != null)
                      Text14SemiBold(
                        DateFormat('d MMMM y, h:mm a').format(bookingDateTime!),
                        color: colorScheme.lightGrey6B6B6B,
                      ),
                  ],
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(top: 4.h),
              child: Text14SemiBold(
                '#$bookingId',
                color: colorScheme.lightGrey6B6B6B,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
