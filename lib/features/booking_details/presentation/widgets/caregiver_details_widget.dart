import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/features/booking_details/bloc/booking_details_cubit.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/common_feedback_widget.dart';
import '../../../../widgets/custom_cache_image.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../common/feedback/bloc/submit_feedback_cubit.dart';
import '../../../common/feedback/data/submit_feedback_req_model.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
import '../../data/model/booking_details_response_model.dart';

class CaregiverDetailsWidget extends StatelessWidget {
  final BookingDetailsResponseModel bookingDetails;

  const CaregiverDetailsWidget({
    super.key,
    required this.bookingDetails,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BlocListener<SubmitFeedbackCubit, SubmitFeedbackState>(
      listener: (context, state) {
        if (state is SubmitFeedbackSuccessState) {
          CustomToast.showToast(
            message: AppStrings.feedbackSubmitted,
            isSuccess: true,
          );
          context
              .read<BookingDetailsCubit>()
              .getBookingDetailsById(bookingId: bookingDetails.id ?? 0);
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        child: Builder(
          builder: (context) {
            final caregiver = bookingDetails.caregiver;
            if (bookingDetails.status == BookingStatusType.completed && bookingDetails.caregiver != null) {
              return CommonFeedbackWidget(
                caregiverName: caregiver?.fullName,
                caregiverImage: caregiver?.profilePicture,
                caregiverServiceType: bookingDetails.subServiceDetails?.name,
                rating: bookingDetails.rating?.rating?.toDouble(),
                onSubmit: (selectedReasons, ratingVal, otherReason) {
                  context.read<SubmitFeedbackCubit>().submitFeedback(
                        submitFeedbackReqModel: SubmitFeedbackReqModel(
                          bookingId: bookingDetails.id ?? 0,
                          rating: ratingVal.toInt(),
                          listedComment: selectedReasons,
                          customComment: otherReason,
                        ),
                      );
                },
              );
            }
            if (caregiver == null) {
              return Row(
                children: [
                  SvgPicture.asset(AppImages.caregiverUserIcon),
                  10.pw,
                  Expanded(
                    child: Text14SemiBold(
                      AppStrings.caregiverDetailsDescription,
                    ),
                  ),
                ],
              );
            }
            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipOval(
                  child: CustomCacheImage(
                    imageUrl: caregiver.profilePicture ?? '',
                    width: 32.r,
                    height: 32.r,
                    errorWidget: SvgPicture.asset(
                      AppImages.caregiverUserIcon,
                      width: 32.r,
                      height: 32.r,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                10.pw,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text14Bold(
                        caregiver.fullName ?? '',
                        fontSize: 16.sp,
                        color: colorScheme.blue140042,
                      ),
                      Text14SemiBold(
                        '${bookingDetails.serviceDetails?.name} expert',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        color: colorScheme.lightGrey6B6B6B,
                      ),
                    ],
                  ),
                ),
                4.pw,
                AppGestureDetector(
                  onTap: () {
                    final uri = Uri.parse('tel:${caregiver.mobileNumber}');
                    launchUrl(uri);
                  },
                  child: Row(
                    children: [
                      SvgPicture.asset(AppImages.callIc),
                      4.pw,
                      Text14Bold(
                        "Contact",
                        color: colorScheme.primary,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
