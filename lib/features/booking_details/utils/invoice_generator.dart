import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../widgets/app_toast.dart';
import '../data/model/booking_details_response_model.dart';
import '../presentation/widgets/booking_invoice_widget.dart';

class InvoiceGenerator {
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid && await DeviceInfoPlugin().androidInfo.then((value) => value.version.sdkInt) < 33) {
      PermissionStatus status = await Permission.storage.request();
      if (status.isGranted) {
        return true;
      } else if (status.isPermanentlyDenied) {
        openAppSettings();
        return false;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  static Future<Directory?> _getDownloadDirectory() async {
    if (Platform.isIOS) {
      return await getApplicationDocumentsDirectory();
    }
    if (await DeviceInfoPlugin().androidInfo.then((value) => value.version.sdkInt) < 30) {
      return await getExternalStorageDirectory();
    }
    return Directory('/storage/emulated/0/Download');
  }

  static Future<void> generateAndSavePDF(BuildContext context, BookingDetailsResponseModel bookingDetails) async {
    try {
      if (!await _requestPermissions()) {
        throw Exception('Permission denied');
      }

      // Get the downloads directory
      final directory = await _getDownloadDirectory();
      if (directory == null) {
        throw Exception('Downloads directory not found');
      }

      bool hasExisted = await directory.exists();
      if (!hasExisted) {
        directory.create();
      }

      // Define the file path with a custom filename
      final fileName = 'booking_invoice_${bookingDetails.id}.pdf';
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);

      // Save the PDF to the file
      final pdf = await generateInvoicePdfBytes(bookingDetails);
      await file.writeAsBytes(await pdf.save());

      // Show success message
      CustomToast.showToast(message: 'PDF saved to Downloads: $fileName', isSuccess: true);

      await OpenFile.open(filePath);
    } catch (e) {
      CustomToast.showToast(message: e.toString(), isSuccess: false);
    }
  }
}
