import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/string_constants/app_strings.dart';
import 'package:homeservice_app/widgets/buttons/primary_button.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';

class AppUpdateWidget extends StatelessWidget {
  final bool isForceUpdate;
  final String? url;
  const AppUpdateWidget({super.key, this.isForceUpdate = false, this.url});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return PopScope(
      canPop: false,
      child: Dialog(
        backgroundColor: colorScheme.white,
        child: Padding(
          padding: EdgeInsets.all(20.0.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // SvgPicture.asset(AppImages.appUpdateImage, height: 200.h),
              // 20.ph,
              Text12Medium(
                isForceUpdate
                    ? AppStrings.newUpdateIsRequired
                    : AppStrings.newUpdateIsAvailable,
                color: colorScheme.grey787878,
              ),
              8.ph,
              Text20Bold(
                AppStrings.updateAvailable,
                textAlign: TextAlign.center,
              ),
              8.ph,
              Text14Medium(
                AppStrings.updateAvailableDescription,
                textAlign: TextAlign.center,
              ),
              20.ph,
              Row(
                children: [
                  if (!isForceUpdate) ...[
                    Expanded(
                        child: CustomOutlinedButton(
                      buttonText: AppStrings.skip,
                      onPressed: () => Navigator.pop(context),
                    )),
                    12.pw,
                  ],
                  Expanded(
                      child: PrimaryButton(
                    buttonText: AppStrings.update,
                    onPressed: () {
                      if (url == null || url!.isEmpty) {
                        return;
                      }
                      launchUrl(Uri.parse(url ?? '')).then((value) => log(value.toString()));
                      if (!isForceUpdate) {
                        Navigator.pop(context);
                      }
                    },
                  )),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
