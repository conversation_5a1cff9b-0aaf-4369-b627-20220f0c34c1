// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/core/routes/app_routes.dart';
import 'package:homeservice_app/features/splash/bloc/splash_state.dart';
import 'package:homeservice_app/features/splash/presentation/widgets/app_update_widget.dart';
import 'package:homeservice_app/utils/string_constants/app_images.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../bloc/splash_bloc.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool _showAnimation = false;
  bool _showText = false;
  bool _isAnimationDone = false;

  @override
  void initState() {
    super.initState();
    // Delay to start animation
    Future.delayed(const Duration(milliseconds: 1), () {
      setState(() {
        _showAnimation = true;
      });
      if (mounted) {
        context.read<SplashScreenCubit>().checkToken();
      }
    });
    Future.delayed(const Duration(milliseconds: 750), () {
      setState(() {
        _showText = true;
      });
      Future.delayed(const Duration(milliseconds: 1750), () {
        setState(() {
          _isAnimationDone = true;
        });
        doNavigation();
      });
    });
  }

  void doNavigation() async {
    if (!_isAnimationDone) return;
    if (!mounted) return;

    final state = context.read<SplashScreenCubit>().state;

    if (state is SplashScreenError) {
      Navigator.pushNamedAndRemoveUntil(context, AppRoute.login, (route) => false);
      return;
    }

    if (state is! SplashScreenSuccess) return;

    final isForceUpdate = state.isForceUpdate;
    final isSoftUpdate = state.isSoftUpdate;
    final isTokenValid = state.isTokenValid;
    final storeUrl = state.storeUrl;
    final updatedBuildNumber = state.updatedBuildNumber;
    final softUpdateShownForBuild = HiveStorageHelper.getData<int?>(HiveBoxName.user, HiveKeys.softUpdateShownForBuild);

    if (!isTokenValid) {
      if (isForceUpdate) {
        showAppUpdateDialog(isForceUpdate: isForceUpdate, url: storeUrl, updatedBuildNumber: updatedBuildNumber);
        return;
      }
      Navigator.pushNamedAndRemoveUntil(context, AppRoute.login, (route) => false);
      if (isSoftUpdate && (softUpdateShownForBuild == null || softUpdateShownForBuild < updatedBuildNumber)) {
        showAppUpdateDialog(isForceUpdate: isForceUpdate, url: storeUrl, updatedBuildNumber: updatedBuildNumber);
      }
      return;
    }
    if (isForceUpdate) {
      showAppUpdateDialog(isForceUpdate: isForceUpdate, url: storeUrl, updatedBuildNumber: updatedBuildNumber);
      return;
    }
    handleNavigation(context);
    if (isSoftUpdate && (softUpdateShownForBuild == null || softUpdateShownForBuild < updatedBuildNumber)) {
      showAppUpdateDialog(isForceUpdate: isForceUpdate, url: storeUrl, updatedBuildNumber: updatedBuildNumber);
    }
  }

  void handleNavigation(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(context, AppRoute.appBottomNavBar, (route) => false);
  }

  void showAppUpdateDialog({required bool isForceUpdate, String? url, int? updatedBuildNumber}) {
    HiveStorageHelper.saveData<int?>(
        HiveBoxName.user,
        HiveKeys.softUpdateShownForBuild,
        updatedBuildNumber);
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return AppUpdateWidget(
            isForceUpdate: isForceUpdate,
            url: url,
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SplashScreenCubit, SplashScreenState>(
      listener: (context, state) {
        doNavigation();
      },
      builder: (context, state) {
        return Scaffold(
          body: Stack(
            fit: StackFit.expand,
            children: [
              AnimatedPositioned(
                duration: const Duration(milliseconds: 750),
                curve: Curves.easeOutCubic,
                top: _showAnimation
                    ? MediaQuery.of(context).size.height / 2 - 30.h
                    : MediaQuery.of(context).size.height / 2 + 150.h,
                left: 0,
                right: 0,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 750),
                  opacity: _showAnimation ? 1.0 : 0.0,
                  child: Center(
                    child: Column(
                      children: [
                        SvgPicture.asset(AppImages.logoIc, height: 74.h),
                        AnimatedOpacity(
                          duration: const Duration(milliseconds: 300),
                          opacity: _showText ? 1.0 : 0.0,
                          child: Center(
                            child: const Text14Medium(
                              AppStrings.whereCareMeetsComfort,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // AnimatedPositioned(
              //   duration: const Duration(milliseconds: 1000),
              //   curve: Curves.easeOutCubic,
              //   bottom: _showAnimation ? -40.h : -200.h,
              //   right: _showAnimation ? -90.w : -300.w,
              //   child: AnimatedOpacity(
              //     duration: const Duration(milliseconds: 800),
              //     opacity: _showAnimation ? 1.0 : 0.0,
              //     child: Transform.rotate(
              //       angle: 100,
              //       child: SizedBox(
              //         height: 280.h,
              //         width: 372.w,
              //         child: SvgPicture.asset(
              //           'assets/images/splash_img.svg',
              //           fit: BoxFit.contain,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }
}
