class AppUpdateModel {
  AppDetails? android;
  AppDetails? ios;

  AppUpdateModel({this.android, this.ios});

  AppUpdateModel.fromJson(Map<String, dynamic> json) {
    android =
        json['android'] != null ? AppDetails.fromJson(json['android']) : null;
    ios = json['ios'] != null ? AppDetails.fromJson(json['ios']) : null;
  }
}

class AppDetails {
  int? currentBuildNumber;
  bool? isForceUpdate;
  String? storeUrl;

  AppDetails({this.currentBuildNumber, this.isForceUpdate, this.storeUrl});

  AppDetails.fromJson(Map<String, dynamic> json) {
    currentBuildNumber = json['current_build_number'];
    isForceUpdate = json['is_force_update'];
    storeUrl = json['store_url'];
  }
}
