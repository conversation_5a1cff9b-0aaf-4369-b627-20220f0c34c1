
import 'package:equatable/equatable.dart';

abstract class SplashScreenState extends Equatable {
  const SplashScreenState();
}

class SplashScreenInitial extends SplashScreenState {
  @override
  List<Object> get props => [];
}


class SplashScreenLoading extends SplashScreenState{
  @override
  List<Object> get props => [];
}


class SplashScreenSuccess extends SplashScreenState{
  final bool isForceUpdate;
  final bool isSoftUpdate;
  final String? storeUrl;
  final bool isTokenValid;
  final int updatedBuildNumber;
  const  SplashScreenSuccess({required this.isSoftUpdate, required this.isForceUpdate, this.storeUrl, this.isTokenValid = false, this.updatedBuildNumber = 0});

  @override
  List<Object> get props => [isSoftUpdate, isForceUpdate, isTokenValid, updatedBuildNumber];
}



class SplashScreenError extends SplashScreenState{
  @override
  List<Object> get props => [];
}