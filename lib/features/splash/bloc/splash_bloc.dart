import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homeservice_app/core/services/hive/hive_keys.dart';
import 'package:homeservice_app/core/services/hive/hive_storage_helper.dart';
import 'package:homeservice_app/utils/app_info.dart';
import '../../../core/api/api_service.dart';
import '../data/app_update_model.dart';
import '../data/refresh_token_model.dart';
import 'splash_state.dart';

class SplashScreenCubit extends Cubit<SplashScreenState> {
  SplashScreenCubit() : super(SplashScreenInitial());

  bool _isTokenValid(RefreshTokenModel response) {
    return response.access?.isNotEmpty == true &&
        response.refresh?.isNotEmpty == true;
  }

  Future<void> checkToken() async {
    String? storedRefreshToken = HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.refreshToken);

    emit(SplashScreenLoading());

    try {
      RefreshTokenModel? refreshTokenModel;
      if (storedRefreshToken != null && storedRefreshToken.isNotEmpty) {
        refreshTokenModel = await _refreshToken(storedRefreshToken);
      }
      final appDetails = await checkForAppUpdate();
      if (appDetails != null) {
        final currentBuild = int.tryParse(AppPackageInfo.buildNumber) ?? 0;
        if (Platform.isAndroid) {
          final androidBuild = appDetails.android?.currentBuildNumber ?? 0;
          final isForce = appDetails.android?.isForceUpdate ?? false;
          bool isUpdateAvailable = currentBuild < androidBuild;
            emit(SplashScreenSuccess(
              updatedBuildNumber: androidBuild,
              isForceUpdate: isUpdateAvailable && isForce,
              isSoftUpdate: isUpdateAvailable && !isForce,
              storeUrl: appDetails.android?.storeUrl,
              isTokenValid: storedRefreshToken != null && storedRefreshToken.isNotEmpty && refreshTokenModel != null,
            ));
            return;
        }
        if (Platform.isIOS) {
          final iosBuild = appDetails.ios?.currentBuildNumber ?? 0;
          final isForce = appDetails.ios?.isForceUpdate ?? false;
          bool isUpdateAvailable = currentBuild < iosBuild;
            emit(SplashScreenSuccess(
              updatedBuildNumber: iosBuild,
              isForceUpdate: isUpdateAvailable && isForce,
              isSoftUpdate: isUpdateAvailable && !isForce,
              storeUrl: appDetails.ios?.storeUrl,
              isTokenValid: storedRefreshToken != null && storedRefreshToken.isNotEmpty && refreshTokenModel != null,
            ));
            return;
        }
      } else {
        emit(SplashScreenError());
      }
    } catch (e) {
      emit(SplashScreenError());
    }
  }

  void resetState() {
    emit(SplashScreenInitial());
  }

  Future<AppUpdateModel?> checkForAppUpdate() async {
    final response = await ApiService.instance.getAppUpdateDetails();
    if (response.success) {
      return AppUpdateModel.fromJson(response.data);
    }
    return null;
  }

  Future<RefreshTokenModel?> _refreshToken(String refreshToken) async {
    try {
      final response =
          await ApiService.instance.refreshToken(refreshToken: refreshToken);
      if (response.success) {
        RefreshTokenModel refreshTokenModel =
            RefreshTokenModel.fromJson(response.data);
        if (_isTokenValid(refreshTokenModel)) {
          await ApiService.instance.setPartnerGatewayToken(
            refreshTokenModel.access ?? "",
            refreshTokenModel.refresh ?? "",
          );
          return refreshTokenModel;
        }
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  Future<RefreshTokenModel> refreshToken() async {
    String storedRefreshToken =
        HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.refreshToken);
    final refreshedData = await _refreshToken(storedRefreshToken);
    return refreshedData ?? RefreshTokenModel();
  }
}
