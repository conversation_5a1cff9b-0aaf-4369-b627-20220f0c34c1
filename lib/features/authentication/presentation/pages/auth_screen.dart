import 'dart:io';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:homeservice_app/core/routes/app_routes.dart';
import 'package:homeservice_app/core/services/hive/hive_keys.dart';
import 'package:homeservice_app/core/services/hive/hive_storage_helper.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/features/authentication/data/authentication_req_model.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/legup_logo.dart';
// import 'package:sms_autofill/sms_autofill.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/config/app_config.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/textfields/mobile_textfield.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../profile/data/enums/policy_type.dart';
import '../../bloc/auth_bloc.dart';
import '../../bloc/auth_state.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  late TextEditingController _mobileController;

  bool autoValidate = false;
  final formKey = GlobalKey<FormState>();

  String? countryAbbr;
  String? countryCode;
  String? countryName;
  String? deviceBrand;
  String? deviceModel;
  String? deviceOs;
  String? deviceName;
  FocusNode focusNode = FocusNode();

  bool isPhoneFetched = false;

  @override
  void initState() {
    super.initState();
    _mobileController = TextEditingController();
    CountryCode country = CountryCode(
      code: "IN",
      dialCode: '+91',
      name: 'India',
    );
    countryAbbr = country.code;
    countryCode = country.dialCode;
    countryName = country.name;
    Geolocator.requestPermission();
    getDeviceInfo();
  }

  Future<void> getDeviceInfo() async {
      final deviceInfo = (await DeviceInfoPlugin().deviceInfo).data;
      deviceBrand = deviceInfo['brand'];
      deviceModel = deviceInfo['model'];
      deviceName = (deviceInfo['name'] ?? '$deviceBrand $deviceModel')?.replaceAll("’", "") ?? "Unknown";
      deviceOs = Platform.isAndroid ? "Android" : Platform.isIOS ? "iOS" : "Unknown";
  }

  @override
  void dispose() {
    _mobileController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    TextTheme textTheme = themeData.textTheme;
    ColorScheme colorScheme = themeData.colorScheme;
    return Scaffold(
      body: SafeArea(
        child: Form(
          key: formKey,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 24.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LegUpLogo(),
                40.ph,
                const Text20ExtraBold(AppStrings.signIn, fontSize: 24),
                32.ph,
                MobileTextfield(
                  controller: _mobileController,
                  onCountryChanged: (country) {
                    countryAbbr = country.code;
                    countryCode = country.dialCode;
                    countryName = country.name;
                    setState(() {});
                  },
                  focusNode: focusNode,
                  onChangedValue: (str) {
                    if (str.isNotEmpty && str.length == 10) {
                      focusNode.unfocus();
                    }
                  },
                  isAutovalidateModeOn: autoValidate,
                  onTap: () async {
                    // if (isPhoneFetched) return;
                    // final String? phone = await SmsAutoFill().hint;
                    // setState(() {
                    //   isPhoneFetched = true;
                    // });
                    // if (phone != null) {
                    //   _mobileController.text = phone.substring(3);
                    // }
                  },
                ),
                32.ph,
                BlocListener<AuthBloc, AuthState>(
                    listener: (context, state) {
                      if (state is AuthLoadingState) {
                        Dialogs.showLoadingDialog(context,
                            loadingText: AppStrings.gettingOTP);
                      }
                      if (state is AuthSuccessState) {
                        Dialogs.hideDialog(context);
                        // Show Success Message
                        CustomToast.showToast(
                            message: state.successMsg, isSuccess: true);
                        // Navigate to OTP Screen
                        Navigator.pushNamed(context, AppRoute.verifyOtp, arguments: AuthenticationReqModel(
                          countryAbbr: countryAbbr,
                          countryCode: countryCode,
                          countryName: countryName,
                          mobileNumber: _mobileController.text.trim(),
                          deviceId: HiveStorageHelper.getData(HiveBoxName.user, HiveKeys.notificationToken),
                          deviceName: deviceName,
                          deviceOs: deviceOs,
                          deviceBrand: deviceBrand
                        )).then((val) {
                          if (val == true) {
                            focusNode.requestFocus();
                          }
                        });
                      }
                      if (state is AuthErrorState) {
                        Dialogs.hideDialog(context);
                        // Show Error Message
                        CustomToast.showToast(
                            message: state.errorMsg,
                            );
                      }
                    },
                    child: ValueListenableBuilder<TextEditingValue>(
                      valueListenable: _mobileController,
                      builder: (context, value, child) {
                        return PrimaryButton(
                          onPressed: value.text.trim().length >= 10
                              ? () {
                                  if (formKey.currentState!.validate()) {
                                    FocusManager.instance.primaryFocus?.unfocus();
                                    context.read<AuthBloc>().authenticate(mobileNumber: "$countryCode${_mobileController.text.trim()}");
                                  } else {
                                    setState(() {
                                      autoValidate = true;
                                    });
                                  }
                                }
                              : null,
                          buttonText: AppStrings.getOTP,
                        );
                      },
                    )),
                16.ph,
                RichText(
                  text: TextSpan(
                    text: AppStrings.acceptTAndC,
                    style: textTheme.displayMedium!.copyWith(color: colorScheme.blue150045, fontSize: 14.sp),
                    children: [
                      TextSpan(
                        text: AppStrings.tAndC,
                        style: textTheme.displayMedium?.copyWith(color: colorScheme.primary, fontSize: 14.sp),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(context, AppRoute.platformPolicy, arguments: PolicyType.termsAndConditions);
                          },
                      ),
                      TextSpan(
                        text: " and ",
                        style: textTheme.bodySmall,
                      ),
                      TextSpan(
                        text: AppStrings.privacyP,
                        style: textTheme.displaySmall?.copyWith(color: colorScheme.primary, fontSize: 14.sp),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(context, AppRoute.platformPolicy, arguments: PolicyType.privacyPolicy);
                          },
                      ),
                      TextSpan(
                        text: ".",
                        style: textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Spacer(),
                Align(
                  alignment: Alignment.center,
                  child: RichText(
                    text: TextSpan(
                      text: AppStrings.facingissue,
                      style: textTheme.bodySmall!.copyWith(color: colorScheme.blue150045, fontSize: 14.sp),
                      children: [
                        TextSpan(
                          text: AppStrings.contactUs,
                          style: textTheme.bodySmall?.copyWith(color: colorScheme.primary, fontSize: 14.sp),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              launchUrl(Uri.parse('tel:${AppConfig.getInstance().metadata.contactNumber}'));
                            },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
