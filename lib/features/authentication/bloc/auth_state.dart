import 'package:equatable/equatable.dart';

abstract class AuthState extends Equatable {}

class AuthInitState extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthLoadingState extends AuthState {
  @override
  List<Object> get props => [];
}

class AuthSuccessState extends AuthState {
  final String successMsg;
  AuthSuccessState(this.successMsg);
  @override
  List<Object> get props => [successMsg];
}

class AuthErrorState extends AuthState {
  final String errorMsg;
  AuthErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
