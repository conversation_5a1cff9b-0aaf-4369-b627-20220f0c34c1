import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import 'auth_state.dart';

class AuthBloc extends Cubit<AuthState> {
  @override
  AuthBloc() : super(AuthInitState());

  Future<void> authenticate({required String mobileNumber}) async {
  emit(AuthLoadingState());
  final response = await ApiService.instance.sendOtpLoginSignUp(mobileNumber: mobileNumber);
    
  try {
    if (response.success) {
      emit(AuthSuccessState(response.message ?? ""));
    } else {
      emit(AuthErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  } catch (e) {
    emit(AuthErrorState(response.message ?? AppStrings.genericErrorMsg));
  }
}
}
