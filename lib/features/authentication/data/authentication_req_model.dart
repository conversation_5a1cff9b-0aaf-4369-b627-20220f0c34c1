class AuthenticationReqModel {
  String? mobileNumber;
  String? otpCode;
  String? countryName;
  String? countryAbbr;
  String? countryCode;
  String? deviceId;
  String? deviceName;
  String? deviceOs;
  String? deviceBrand;

  AuthenticationReqModel(
      {this.mobileNumber,
      this.otpCode,
      this.countryName,
      this.countryAbbr,
      this.countryCode,
      this.deviceId,
      this.deviceName,
      this.deviceOs,
      this.deviceBrand});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mobile_number'] = mobileNumber;
    data['otp_code'] = otpCode;
    data['country_name'] = countryName;
    data['country_abbr'] = countryAbbr;
    data['country_code'] = countryCode;
    data['device_id'] = deviceId;
    data['device_name'] = deviceName;
    data['device_os'] = deviceOs;
    data['device_brand'] = deviceBrand;
    return data;
  }
}
