import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';
part 'get_unrated_booking_state.dart';

class GetUnratedBookingCubit extends Cubit<GetUnratedBookingState> {
  GetUnratedBookingCubit() : super(GetUnratedBookingInitState());

  void getUnratedBooking() async {
    emit(GetUnratedBookingLoadingState());
    try {
      final response = await ApiService.instance.getUnratedBooking();
      if (response.success) {
        emit(GetUnratedBookingSuccessState(
            bookingDetailsResponseModel: BookingDetailsResponseModel.fromJson(response.data)));
      } else {
        emit(GetUnratedBookingErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(GetUnratedBookingErrorState(AppStrings.genericErrorMsg));
    }
  }
}
