part of 'submit_feedback_cubit.dart';

sealed class SubmitFeedbackState extends Equatable {}

class SubmitFeedbackInitState extends SubmitFeedbackState {
  @override
  List<Object> get props => [];
}

class SubmitFeedbackLoadingState extends SubmitFeedbackState {
  @override
  List<Object> get props => [];
}

class SubmitFeedbackSuccessState extends SubmitFeedbackState {
  @override
  List<Object> get props => [];
}


class SubmitFeedbackErrorState extends SubmitFeedbackState {
  final String errorMsg;
  SubmitFeedbackErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
