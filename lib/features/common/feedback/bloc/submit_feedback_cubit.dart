import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../data/submit_feedback_req_model.dart';
part 'submit_feedback_state.dart';

class SubmitFeedbackCubit extends Cubit<SubmitFeedbackState> {
  SubmitFeedbackCubit() : super(SubmitFeedbackInitState());

  void submitFeedback({required SubmitFeedbackReqModel submitFeedbackReqModel}) async {
    emit(SubmitFeedbackLoadingState());
    try {
      final response = await ApiService.instance.submitFeedback(submitFeedbackReqModel: submitFeedbackReqModel);
      if (response.success) {
        emit(SubmitFeedbackSuccessState());
      } else {
        emit(SubmitFeedbackErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(SubmitFeedbackErrorState(AppStrings.genericErrorMsg));
    }
  }
}
