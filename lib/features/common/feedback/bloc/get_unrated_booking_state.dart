part of 'get_unrated_booking_cubit.dart';

sealed class GetUnratedBookingState extends Equatable {}

class GetUnratedBookingInitState extends GetUnratedBookingState {
  @override
  List<Object> get props => [];
}

class GetUnratedBookingLoadingState extends GetUnratedBookingState {
  @override
  List<Object> get props => [];
}

class GetUnratedBookingSuccessState extends GetUnratedBookingState {
  final BookingDetailsResponseModel? bookingDetailsResponseModel;

  GetUnratedBookingSuccessState({this.bookingDetailsResponseModel});
  @override
  List<Object?> get props => [bookingDetailsResponseModel];
}

class GetUnratedBookingErrorState extends GetUnratedBookingState {
  final String errorMsg;
  GetUnratedBookingErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
