class SubmitFeedbackReqModel {
  int bookingId;
  int rating;
  String? listedComment;
  String? customComment;

  SubmitFeedbackReqModel({
    required this.bookingId,
    required this.rating,
    this.listedComment,
    this.customComment,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['booking'] = bookingId;
    data['rating'] = rating;
    if (listedComment != null) {
      data['listed_comment'] = listedComment;
    }
    if (customComment != null) {
      data['custom_comment'] = customComment;
    }
    return data;
  }
}
