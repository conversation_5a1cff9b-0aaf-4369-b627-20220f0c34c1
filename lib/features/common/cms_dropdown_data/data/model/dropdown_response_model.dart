import '../enums/cms_drop_down_sub_type.dart';

class DropdownResponseModel {
  List<DropDownItem>? items;

  DropdownResponseModel({this.items});

  factory DropdownResponseModel.fromJson(List<dynamic>? json) {
    return DropdownResponseModel(
      items: json?.map((e) => DropDownItem.fromJson(e)).toList(),
    );
  }
}

class DropDownItem {
  int? id;
  String? type;
  CMSDropDownSubType? subtype;
  List<String>? value;
  DateTime? createdAt;
  DateTime? updatedAt;
  bool? isActive;

  DropDownItem({
    this.id,
    this.type,
    this.subtype,
    this.value,
    this.createdAt,
    this.updatedAt,
    this.isActive,
  });

  factory DropDownItem.fromJson(Map<String, dynamic>? json) {
    if (json == null) return DropDownItem();
    return DropDownItem(
      id: json['id'],
      type: json['type'],
      subtype: CMSDropDownSubType.getCMSDropDownSubTypeFromString(json['subtype']),
      value:
          (json['value'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      isActive: json['is_active'],
    );
  }
}
