import '../../../../../utils/string_constants/app_strings.dart';

enum CMSDropDownSubType {
  workHours(AppStrings.workHours),
  languages(AppStrings.languages),
  nativeLanguages(AppStrings.nativeLanguages),
  experience(AppStrings.experience),
  instant(AppStrings.instantLowerCase),
  nonInstant(AppStrings.nonInstant);

  final String _name;

  const CMSDropDownSubType(this._name);

  @override
  String toString() => _name;

  static CMSDropDownSubType? getCMSDropDownSubTypeFromString(String? cmsDropDownSubType) {
    switch (cmsDropDownSubType) {
      case AppStrings.languages:
        return CMSDropDownSubType.languages;
      case AppStrings.nativeLanguages:
        return CMSDropDownSubType.nativeLanguages;
      case AppStrings.experience:
        return CMSDropDownSubType.experience;
      case AppStrings.workHours:
        return CMSDropDownSubType.workHours;
      case AppStrings.instantLowerCase:
        return CMSDropDownSubType.instant;
      case AppStrings.nonInstant:
        return CMSDropDownSubType.nonInstant;
      default:
        return null;
    }
  }
}
