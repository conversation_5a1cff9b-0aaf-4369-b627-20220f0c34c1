import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../data/model/dropdown_response_model.dart';

part 'cms_dropdown_data_state.dart';

class CMSDropdownDataCubit extends Cubit<CMSDropdownDataState> {
  CMSDropdownDataCubit() : super(CMSDropdownDataInitState());

  void getCMSDropDown() async {
    emit(CMSDropdownDataLoadingState());

    final response = await ApiService.instance.cmsDropDown();

    try {
      if (response.success) {
        emit(CMSDropdownDataSuccessState(dropDownResponseModel: DropdownResponseModel.fromJson(response.data)));
      } else {
        emit(CMSDropdownDataErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(CMSDropdownDataErrorState(AppStrings.genericErrorMsg));
    }
  }
}
