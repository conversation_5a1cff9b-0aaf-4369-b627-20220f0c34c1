part of 'cms_dropdown_data_cubit.dart';

sealed class CMSDropdownDataState extends Equatable {}

class CMSDropdownDataInitState extends CMSDropdownDataState {
  @override
  List<Object> get props => [];
}

class CMSDropdownDataLoadingState extends CMSDropdownDataState {
  @override
  List<Object> get props => [];
}

class CMSDropdownDataSuccessState extends CMSDropdownDataState {
  final DropdownResponseModel? dropDownResponseModel;

  CMSDropdownDataSuccessState({this.dropDownResponseModel});
  @override
  List<Object?> get props => [dropDownResponseModel];
}

class CMSDropdownDataErrorState extends CMSDropdownDataState {
  final String errorMsg;
  CMSDropdownDataErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
