import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';

part 'continue_booking_state.dart';

class ContinueBookingCubit extends Cubit<ContinueBookingState> {
  ContinueBookingCubit() : super(ContinueBookingInitState());

  void getLastBookingDetails() async {
    emit(ContinueBookingLoadingState());

    try {
      final response = await ApiService.instance.getBookings(status: 'PE');
      if (response.success) {
        final BookingDetailsResponseModel bookingDetailsResponseModel = BookingDetailsResponseModel.fromJson(response.data as Map<String, dynamic>);
        emit(ContinueBookingSuccessState(bookingDetailsResponseModel));
      } else {
        emit(ContinueBookingErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ContinueBookingErrorState(AppStrings.genericErrorMsg));
    }
  }

  void discardPendingBooking({required int bookingId}) async {
    emit(ContinueBookingInitState());
    try {
      await ApiService.instance.discardPendingBooking(bookingId: bookingId);
    } catch (e) {
      emit(ContinueBookingErrorState(AppStrings.genericErrorMsg));
    }
  }
}
