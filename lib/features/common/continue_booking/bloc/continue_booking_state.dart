part of 'continue_booking_cubit.dart';

sealed class ContinueBookingState extends Equatable {
  const ContinueBookingState();

  @override
  List<Object?> get props => [];
}

final class ContinueBookingInitState extends ContinueBookingState {
  @override
  List<Object> get props => [];
}

final class ContinueBookingLoadingState extends ContinueBookingState {
  @override
  List<Object> get props => [];
}

final class ContinueBookingSuccessState extends ContinueBookingState {
  final BookingDetailsResponseModel? bookingDetailsResponseModel;
  const ContinueBookingSuccessState(this.bookingDetailsResponseModel);

  @override
  List<Object?> get props => [bookingDetailsResponseModel];
}

final class ContinueBookingErrorState extends ContinueBookingState {
  final String errorMsg;

  const ContinueBookingErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
