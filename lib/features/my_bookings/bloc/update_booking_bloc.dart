import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homeservice_app/features/service_booking/data/model/booking_request_model.dart';
import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
part 'update_booking_state.dart';

class UpdateBookingBloc extends Cubit<UpdateBookingState> {
  UpdateBookingBloc() : super(UpdateBookingInitState());

  void updateBooking({required BookingRequestModel updateBookingReqModel, int? bookingId}) async {
    emit(UpdateBookingLoadingState());
    try {
      final response = await ApiService.instance.updateBookingById(bookingRequestModel: updateBookingReqModel, bookingId: bookingId ?? 0);
      if (response.success) {
        emit(UpdateBookingSuccessState());
      } else {
        emit(UpdateBookingErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(UpdateBookingErrorState(AppStrings.genericErrorMsg));
    }
  }
}
