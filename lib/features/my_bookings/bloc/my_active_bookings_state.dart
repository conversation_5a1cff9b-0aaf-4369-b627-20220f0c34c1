part of 'my_active_bookings_bloc.dart';

sealed class ActiveB<PERSON><PERSON>State extends Equatable {}

class ActiveBookingsInitState extends ActiveBookingsState {
  @override
  List<Object> get props => [];
}

class ActiveBookingsLoadingState extends ActiveBookingsState {
  @override
  List<Object> get props => [];
}

class ActiveBookingsSuccessState extends ActiveBookingsState {
  final MyBookingsModel activeBookings;

  ActiveBookingsSuccessState({required this.activeBookings});
  @override
  List<Object?> get props => [activeBookings];
}

class ActiveBookingsErrorState extends ActiveBookingsState {
  final String errorMsg;
  ActiveBookingsErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
