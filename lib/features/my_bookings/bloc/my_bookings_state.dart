part of 'my_bookings_bloc.dart';

sealed class MyB<PERSON><PERSON>State extends Equatable {}

class MyBookingsInitState extends MyBookingsState {
  @override
  List<Object> get props => [];
}

class MyBookingsLoadingState extends MyB<PERSON>ingsState {
  @override
  List<Object> get props => [];
}

class MyBookingsSuccessState extends MyBookingsState {
  final Map<DateTime?, List<BookingDetailsResponseModel>?> myBookings;

  MyBookingsSuccessState({required this.myBookings});
  @override
  List<Object?> get props => [myBookings];
}

class MyBookingsErrorState extends MyBookingsState {
  final String errorMessage;
  MyBookingsErrorState(this.errorMessage);
  @override
  List<Object> get props => [errorMessage];
}
