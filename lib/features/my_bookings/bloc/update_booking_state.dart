part of 'update_booking_bloc.dart';

sealed class UpdateBookingState extends Equatable {}

class UpdateBookingInitState extends UpdateBookingState {
  @override
  List<Object> get props => [];
}

class UpdateBookingLoadingState extends UpdateBookingState {
  @override
  List<Object> get props => [];
}

class UpdateBookingSuccessState extends UpdateBookingState {
  @override
  List<Object> get props => [];
}


class UpdateBookingErrorState extends UpdateBookingState {
  final String errorMsg;
  UpdateBookingErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
