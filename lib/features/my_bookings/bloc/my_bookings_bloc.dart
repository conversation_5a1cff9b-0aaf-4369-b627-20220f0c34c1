import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homeservice_app/features/my_bookings/data/my_bookings_model.dart';
import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../booking_details/data/model/booking_details_response_model.dart';
import '../data/get_bookings_filters_req_model.dart';
import 'package:equatable/equatable.dart';
part 'my_bookings_state.dart';

class MyBookingsBloc extends Cubit<MyBookingsState> {
  MyBookingsBloc() : super(MyBookingsInitState());

  void getBookings({GetBookingsFiltersReqModel? getBookingsFiltersReqModel}) async {
    emit(MyBookingsLoadingState());
    try {
      final response = await ApiService.instance.getMyBookings(
        getBookingsFiltersReqModel: getBookingsFiltersReqModel,
      );
      if (response.success) {
        final myBookings = MyBookingsModel.fromJson({"data": response.data, "_meta": null});
        final Map<DateTime?, List<BookingDetailsResponseModel>?> groupedBookings = groupBookingsByDate(myBookings.bookings ?? []);
        emit(MyBookingsSuccessState(
          myBookings: groupedBookings,
        ));
      } else {
        emit(MyBookingsErrorState(
          response.message ?? AppStrings.genericErrorMsg,
        ));
      }
    } catch (e) {
      emit(MyBookingsErrorState(AppStrings.genericErrorMsg));
    }
  }


    // --- Grouping Logic ---
  Map<DateTime?, List<BookingDetailsResponseModel>> groupBookingsByDate(List<BookingDetailsResponseModel> bookings) {
    final grouped = groupBy(bookings, (BookingDetailsResponseModel booking) => booking.startDate);
    return grouped;
  }
}
