import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:homeservice_app/features/my_bookings/data/my_bookings_model.dart';
import '../../../../core/api/api_service.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../data/get_bookings_filters_req_model.dart';
part 'my_active_bookings_state.dart';

class ActiveBookingsBloc extends Cubit<ActiveBookingsState> {
  ActiveBookingsBloc() : super(ActiveBookingsInitState());

  void getActiveBookings({GetBookingsFiltersReqModel? getBookingsFiltersReqModel}) async {
    emit(ActiveBookingsLoadingState());
    try {
      final response = await ApiService.instance.getMyBookings(
        getBookingsFiltersReqModel: getBookingsFiltersReqModel,
      );
      if (response.success) {
        emit(ActiveBookingsSuccessState(
          activeBookings: MyBookingsModel.fromJson({"data": response.data, "_meta": null}),
        ));
      } else {
        emit(ActiveBookingsErrorState(
          response.message ?? AppStrings.genericErrorMsg,
        ));
      }
    } catch (e) {
      emit(ActiveBookingsErrorState(AppStrings.genericErrorMsg));
    }
  }
}
