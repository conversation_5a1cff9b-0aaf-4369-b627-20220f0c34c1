class GetBookingsFiltersReqModel {
  int? serviceType;
  int? serviceSubtype;
  int? duration;
  String? language;
  String? arrivalType;
  String? status;
  String? startDateTime;

  GetBookingsFiltersReqModel(
      {this.serviceType,
      this.serviceSubtype,
      this.duration,
      this.language,
      this.arrivalType,
      this.status,
      this.startDateTime});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (status != null && status!.isNotEmpty) {
      data['status'] = status;
    }
    if (serviceType != null) {
      data['service_type'] = serviceType;
    }
    if (serviceSubtype != null) {
      data['service_subtype'] = serviceSubtype;
    }
    if (duration != null) {
      data['duration'] = duration;
    }
    if (language != null && language!.isNotEmpty) {
      data['language'] = language;
    }
    if (arrivalType != null && arrivalType!.isNotEmpty) {
      data['arrival_type'] = arrivalType;
    }
    if (startDateTime != null && startDateTime!.isNotEmpty) {
      data['start_date_time'] = startDateTime;
    }
    return data;
  }
}
