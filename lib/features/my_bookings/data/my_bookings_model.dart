import '../../booking_details/data/model/booking_details_response_model.dart';

class MyBookingsModel {
  List<BookingDetailsResponseModel>? bookings;
  Meta? meta;

  MyBookingsModel({this.bookings, this.meta});

  factory MyBookingsModel.fromJson(Map<String, dynamic> json) {
    return MyBookingsModel(
      bookings: json['data'] is List
          ? (json['data'] as List<dynamic>).map((x) => BookingDetailsResponseModel.fromJson(x as Map<String, dynamic>)).toList()
          : [],
      meta: json['_meta'] != null ? Meta.fromJson(json['_meta']) : null,
    );
  }
}

class Meta {
  int currentPage;
  int totalCount;
  int totalPages;

  Meta({required this.currentPage, required this.totalCount, required this.totalPages});

  factory Meta.fromJson(Map<String, dynamic> json) {
    return Meta(
      currentPage: json['current_page'],
      totalCount: json['total_count'],
      totalPages: json['total_pages'],
    );
  }
}
