import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:homeservice_app/features/my_bookings/presentation/widgets/booking_card.dart';
import 'package:homeservice_app/features/my_bookings/presentation/widgets/booking_shimmer.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/error_state_widget.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
import '../../bloc/my_bookings_bloc.dart';
import '../../data/get_bookings_filters_req_model.dart';
import '../widgets/empty_booking_widget.dart';

class PastBookingsTab extends StatefulWidget {
  const PastBookingsTab({super.key});

  @override
  State<PastBookingsTab> createState() => _PastBookingsTabState();
}

class _PastBookingsTabState extends State<PastBookingsTab> {
  List<String> pastBookingStatus = [
    BookingStatusType.completed.apiParamValue,
    BookingStatusType.cancelled.apiParamValue,
    BookingStatusType.failed.apiParamValue,
  ];

  @override
  void initState() {
    super.initState();
    context.read<MyBookingsBloc>().getBookings(
          getBookingsFiltersReqModel: GetBookingsFiltersReqModel(
            status: pastBookingStatus.join(','),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        12.ph,
        Divider(
          thickness: 8.h,
          color: colorScheme.surface,
        ),
        Expanded(
          child: BlocBuilder<MyBookingsBloc, MyBookingsState>(
            builder: (context, state) {
              if (state is MyBookingsLoadingState) {
                return BookingShimmer();
              }
              if (state is MyBookingsErrorState) {
                return ErrorStateWidget(
                  errorMessage: state.errorMessage,
                  showRetryButton: false,
                  margin: EdgeInsets.only(top: 104.h),
                  removeDecoration: true,
                );
              }
              if (state is MyBookingsSuccessState) {
                // Get total bookings count
                final totalBookingsCount = state.myBookings.values.fold(0, (sum, bookings) => sum + (bookings?.length ?? 0));
                final sortedDates = state.myBookings.keys.whereType<DateTime>().toList()..sort((a, b) => a.compareTo(b));
                if (sortedDates.isEmpty) {
                  return const EmptyBookingWidget(
                    title: AppStrings.noBookingHistory,
                    description: AppStrings.manageAllYourPastBookingsHere,
                  );
                }
                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<MyBookingsBloc>().getBookings(
                          getBookingsFiltersReqModel: GetBookingsFiltersReqModel(
                            status: pastBookingStatus.join(','),
                          ),
                        );
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 20.w, top: 20.h),
                        child: Text14Bold(
                            "$totalBookingsCount ${AppStrings.pastBookings}"),
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: sortedDates.length,
                          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
                          itemBuilder: (context, index) {
                            final date = sortedDates[index];
                            final bookingsOnDate = state.myBookings[date] ?? [];
                            final bookingCount = bookingsOnDate.length;
                            final formattedDate = DateFormat('d MMM yyyy').format(date).toUpperCase();
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // --- Date Header ---
                                Padding(
                                  padding: EdgeInsets.only(top: index == 0 ? 0.w : 16.0.w,bottom: 8.0.w),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text14SemiBold(
                                        formattedDate,
                                      ),
                                      Text14Medium(
                                        bookingCount.toString(),
                                      ),
                                    ],
                                  ),
                                ),

                                // --- List of Booking Cards for this Date ---
                                Column(
                                  children: bookingsOnDate.map((booking) {
                                    return BookingCard(bookingDetails: booking); 
                                  }).toList(),
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ),
      ],
    );
  }
}
