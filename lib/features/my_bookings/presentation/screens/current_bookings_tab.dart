import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/features/my_bookings/presentation/widgets/booking_card.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';
import 'package:homeservice_app/widgets/error_state_widget.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
import '../../bloc/my_active_bookings_bloc.dart';
import '../../bloc/my_bookings_bloc.dart';
import '../../data/get_bookings_filters_req_model.dart';
import '../widgets/active_booking_shimmer.dart';
import '../widgets/booking_shimmer.dart';
import '../widgets/empty_booking_widget.dart';

class CurrentBookingsTab extends StatefulWidget {
  const CurrentBookingsTab({super.key});

  @override
  State<CurrentBookingsTab> createState() => _CurrentBookingsTabState();
}

class _CurrentBookingsTabState extends State<CurrentBookingsTab> {
  List<String> currentBookingStatus = [
    BookingStatusType.confirmed.apiParamValue,
    BookingStatusType.booked.apiParamValue,
    BookingStatusType.pending.apiParamValue,
  ];

  @override
  void initState() {
    super.initState();
    context.read<ActiveBookingsBloc>().getActiveBookings(
          getBookingsFiltersReqModel: GetBookingsFiltersReqModel(
            status: BookingStatusType.started.apiParamValue,
          ),
        );
    context.read<MyBookingsBloc>().getBookings(
          getBookingsFiltersReqModel: GetBookingsFiltersReqModel(
            status: currentBookingStatus.join(','),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return BlocBuilder<ActiveBookingsBloc, ActiveBookingsState>(
      builder: (context, activeBookingsState) {
        return BlocBuilder<MyBookingsBloc, MyBookingsState>(
          builder: (context, myBookingsState) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<ActiveBookingsBloc>().getActiveBookings(
                      getBookingsFiltersReqModel: GetBookingsFiltersReqModel(
                        status: BookingStatusType.started.apiParamValue,
                      ),
                    );
                context.read<MyBookingsBloc>().getBookings(
                      getBookingsFiltersReqModel: GetBookingsFiltersReqModel(
                        status: currentBookingStatus.join(','),
                      ),
                    );
              },
              child: Column(
                children: [
                  12.ph,
                  Divider(
                    thickness: 8.h,
                    color: colorScheme.surface,
                  ),
                  Expanded(
                    child: ListView(
                      children: [
                        Builder(builder: (context) {
                          if(activeBookingsState is ActiveBookingsErrorState && myBookingsState is MyBookingsErrorState) {
                            return ErrorStateWidget(
                              errorMessage: activeBookingsState.errorMsg,
                              showRetryButton: false,
                              removeDecoration: true,
                              margin: EdgeInsets.only(top: 120.h),
                            );
                          }
                          if (activeBookingsState is ActiveBookingsSuccessState && myBookingsState is MyBookingsSuccessState) {
                            final myBookings = myBookingsState.myBookings.keys.toList();
                            if ((activeBookingsState.activeBookings.bookings?.isEmpty ?? true) && (myBookings.isEmpty)) {
                              return const EmptyBookingWidget(
                                title: AppStrings.noActiveBookings,
                                description:
                                    AppStrings.manageAllYourActiveBookingsHere,
                              );
                            }
                          }
                          return const SizedBox.shrink();
                        }),
                        Builder(builder: (context) {
                          if (activeBookingsState is ActiveBookingsLoadingState) {
                            return ActiveBookingShimmer();
                          }
                          if (activeBookingsState is ActiveBookingsSuccessState) {
                            if (activeBookingsState.activeBookings.bookings?.isEmpty ?? true) {
                              return const SizedBox.shrink();
                            }
                            return ListView.builder(
                                itemCount: activeBookingsState.activeBookings.bookings?.length ?? 0,
                                padding: EdgeInsets.symmetric(
                                  horizontal: 20.w,
                                ),
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  BookingDetailsResponseModel bookingDetails = activeBookingsState.activeBookings.bookings![index];
                                  return AppGestureDetector(
                                    onTap: () {
                                      Navigator.pushNamed(
                                        context,
                                        AppRoute.bookingDetails,
                                        arguments: bookingDetails.id ?? 0,
                                      );
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(bottom: index == activeBookingsState.activeBookings.bookings!.length - 1 ? 0 : 16.h, top: index == 0 ? 16.h : 0),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text14SemiBold(AppStrings.active.toUpperCase(),
                                                  color:
                                                      colorScheme.green329071),
                                              Text14Bold(
                                                  "${AppStrings.instant}, ${formatSchedule(bookingDetails.startDateTime, bookingDetails.endDateTime)}",
                                                  fontSize: 16.sp),
                                              Text12SemiBold(bookingDetails
                                                      .subServiceDetails
                                                      ?.name ??
                                                  ""),
                                              Text12SemiBold(bookingDetails
                                                      .subServiceDetails
                                                      ?.subTitle ??
                                                  ""),
                                              // Text14Medium(AppStrings.renewalOn,
                                              //     color: colorScheme
                                              //         .lightGrey8F8F8F),
                                            ],
                                          ),
                                          SvgPicture.asset(
                                            AppImages.arrowTopRightIcon,
                                            width: 24.w,
                                            height: 24.h,
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                });
                          }
                          return const SizedBox.shrink();
                        }),
                        Builder(builder: (context) {
                          if (myBookingsState is MyBookingsLoadingState) {
                            return BookingShimmer();
                          }
                          if (myBookingsState is MyBookingsSuccessState) {
                            final sortedDates = myBookingsState.myBookings.keys.whereType<DateTime>().toList()..sort((a, b) => a.compareTo(b));
                            if (sortedDates.isEmpty) {
                              return const SizedBox.shrink();
                            }
                            return Column(
                              children: [
                                if (activeBookingsState is ActiveBookingsSuccessState && (activeBookingsState.activeBookings.bookings?.isNotEmpty ?? false)) ...[
                                  12.ph,
                                  Divider(
                                    thickness: 8.h,
                                    color: colorScheme.surface,
                                  ),
                                ],
                                ListView.builder(
                                  itemCount: sortedDates.length,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 20.w,
                                  ),
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    final date = sortedDates[index];
                                    final bookingsOnDate = myBookingsState.myBookings[date] ?? [];
                                    final bookingCount = bookingsOnDate.length;
                                    final formattedDate = DateFormat('d MMM yyyy').format(date).toUpperCase();
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(top: 16.0.w, bottom: 8.0.w),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text14SemiBold(
                                                formattedDate,
                                              ),
                                              Text14Medium(
                                                bookingCount.toString(),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          children:
                                              bookingsOnDate.map((booking) {
                                            return BookingCard(bookingDetails: booking); // Pass booking to your card widget
                                          }).toList(),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        })
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String formatSchedule(DateTime? start, DateTime? end) {
    if (start == null || end == null) {
      return '';
    }
    final startTime = DateFormat("h:mm a").format(start);
    final endTime = DateFormat("h:mm a").format(end);

    return "$startTime - $endTime";
  }
}
