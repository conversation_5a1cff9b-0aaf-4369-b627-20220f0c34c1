import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/features/my_bookings/presentation/screens/current_bookings_tab.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../bloc/my_active_bookings_bloc.dart';
import '../../bloc/my_bookings_bloc.dart';
import 'past_bookings_tab.dart';

class BookingsScreen extends StatefulWidget {
  const BookingsScreen({super.key});

  @override
  State<BookingsScreen> createState() => _BookingsScreenState();
}

class _BookingsScreenState extends State<BookingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ActiveBookingsBloc(),
        ),
        BlocProvider(
          create: (context) => MyBookingsBloc(),
        ),
      ],
      child: Scaffold(
        appBar: CustomAppbar(
          title: AppStrings.bookings,
          showBackButton: false,
        ),
        body: SafeArea(
          child: Column(
            children: [
              16.ph,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: TabBar(
                  controller: _tabController,
                  dividerColor: Colors.transparent,
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelStyle: textTheme.bodyMedium!.copyWith(fontSize: 16.sp),
                  unselectedLabelStyle:
                      textTheme.displaySmall!.copyWith(fontSize: 16.sp),
                  indicator: BoxDecoration(
                    color: colorScheme.blueE7EFF9,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  splashFactory: NoSplash.splashFactory,
                  dividerHeight: 0,
                  tabs: [
                    Tab(
                      text: AppStrings.currentBookings,
                      height: 32.h,
                    ),
                    Tab(text: AppStrings.pastBookings, height: 32.h),
                  ],
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    // Current Bookings
                    CurrentBookingsTab(),
                    // Past Bookings
                    PastBookingsTab(),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
