import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/texts/app_text.dart';
import '../../../../utils/string_constants/app_images.dart';

class EmptyBookingWidget extends StatelessWidget {
  final String title;
  final String description;
  final double topGap;
  const EmptyBookingWidget(
      {super.key,
      required this.title,
      required this.description,
      this.topGap = 120});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          topGap.ph,
          SvgPicture.asset(AppImages.emptyBookingImg),
          4.ph,
          Text20Bold(title),
          4.ph,
          Text14Medium(description, color: colorScheme.lightGrey6B6B6B),
        ],
      ),
    );
  }
}
