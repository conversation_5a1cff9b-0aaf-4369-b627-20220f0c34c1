import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:shimmer/shimmer.dart';

class ActiveBookingShimmer extends StatelessWidget {
  const ActiveBookingShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      children: [
        ListView.builder(
          itemCount: 2,
          shrinkWrap: true,
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) => Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      16.ph,
                      Container(
                          height: 12.h,
                          width: 60.w,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                          )),
                      4.ph,
                      Container(
                          height: 16.h,
                          width: 200.w,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                          )),
                      4.ph,
                      Container(
                          height: 14.h,
                          width: 180.w,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                          )), // Age Group

                      4.ph,
                      Container(
                          height: 14.h,
                          width: 160.w,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                          )),
                      16.ph,
                    ],
                  ),
                ),
                12.pw,
                // Right arrow shimmer
                Container(
                  height: 24.h,
                  width: 24.h,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
