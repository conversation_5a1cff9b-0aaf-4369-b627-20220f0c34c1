import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:homeservice_app/core/routes/app_routes.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../booking_details/data/model/booking_details_response_model.dart';
import '../../../service_booking/data/enums/booking_status_type.dart';
class BookingCard extends StatelessWidget {
  final BookingDetailsResponseModel bookingDetails;
  const BookingCard({super.key, required this.bookingDetails});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return AppGestureDetector(
      onTap: () {
        Navigator.pushNamed(context, AppRoute.bookingDetails, arguments: bookingDetails.id ?? 0);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          border: Border.all(color: colorScheme.lightGreenB6E2D3),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    child: Text14SemiBold(AppStrings.instant.toUpperCase())),
                Builder(
                  builder: (context) {
                    final status = bookingDetails.status;
                    final boxColor = (status == BookingStatusType.pending ||
                            status == BookingStatusType.booked)
                        ? colorScheme.orangeFCEEE3
                        : (status == BookingStatusType.started ||
                                status == BookingStatusType.confirmed ||
                                status == BookingStatusType.completed)
                            ? colorScheme.greenE9F7F2
                            : colorScheme.redFFE0E0;
                    final textColor = (status == BookingStatusType.pending ||
                            status == BookingStatusType.booked)
                        ? colorScheme.orangeEB8F47
                        : (status == BookingStatusType.started ||
                                status == BookingStatusType.confirmed ||
                                status == BookingStatusType.completed)
                            ? colorScheme.success3FB68E
                            : colorScheme.error;
                    return Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                      decoration: BoxDecoration(
                        color: boxColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text14Bold(
                        bookingDetails.status?.apiValue ?? '',
                        color: textColor,
                      ),
                    );
                  },
                ),
              ],
            ),
            4.ph,
            Row(
              children: [
                Text14SemiBold(
                    formatSchedule(
                        bookingDetails.startDateTime, bookingDetails.endDateTime),
                    fontSize: 16.sp),
                4.pw,
                Text14SemiBold(
                    getTimeDifference(
                        bookingDetails.startDateTime, bookingDetails.endDateTime),
                    color: colorScheme.lightGrey8F8F8F),
              ],
            ),
            2.ph,
            Text12SemiBold(
              bookingDetails.subServiceDetails?.name ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            2.ph,
            Text12SemiBold(
              bookingDetails.subServiceDetails?.subTitle ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            2.ph,
            Text14Medium(
                bookingDetails.addressDetails?.address ?? '',
                color: colorScheme.lightGrey8F8F8F),
            if (bookingDetails.caregiver == null &&
                (bookingDetails.status != BookingStatusType.completed &&
                    bookingDetails.status != BookingStatusType.cancelled &&
                    bookingDetails.status != BookingStatusType.failed)) ...[
            4.ph,
            Divider(
              height: 1.h,
              color: colorScheme.lightGreyDEDEDE,
              ),
              4.ph,
              Row(
                children: [
                  SvgPicture.asset(AppImages.caregiverUserIcon),
                  12.pw,
                  Expanded(
                    child: Text14SemiBold(AppStrings
                        .caregiverDetailsWillBeSharedOnceTheBookingIsConfirmed),
                  )
                ],
              ),
            ],
            if (bookingDetails.caregiver != null) ...[
              4.ph,
              Divider(
                height: 1.h,
                color: colorScheme.lightGreyDEDEDE,
              ),
              4.ph,
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundImage: NetworkImage(
                        bookingDetails.caregiver?.profilePicture ?? ''),
                  ),
                  12.pw,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text14Bold(bookingDetails.caregiver?.fullName ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            fontSize: 16.sp),
                        Text14SemiBold(
                            bookingDetails.subServiceDetails?.name ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            color: colorScheme.lightGrey6B6B6B),
                      ],
                    ),
                  ),
                  // const Spacer(),
                  // if (true)
                  //   Container(
                  //     padding: EdgeInsets.all(4.w),
                  //     decoration: BoxDecoration(
                  //       color: colorScheme.lightGreyF5F5F5,
                  //       borderRadius: BorderRadius.circular(4.r),
                  //     ),
                  //     child: Text14Bold("${AppStrings.otp} 1234",
                  //         color: colorScheme.lightGrey8F8F8F),
                  //   )
                ],
              ),
            ]
          ],
        ),
      ),
    );
  }

  String getTimeDifference(DateTime? start, DateTime? end) {
    if (start == null || end == null) {
      return '';
    }
    final difference = end.difference(start);
    return "${difference.inHours}hrs";
  }

  String formatSchedule(DateTime? start, DateTime? end) {
    if (start == null || end == null) {
      return '';
    }
    final startTime = DateFormat("h:mm a").format(start);
    final endTime = DateFormat("h:mm a").format(end);

    return "$startTime - $endTime";
  }
}
