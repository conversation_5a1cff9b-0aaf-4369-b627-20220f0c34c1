import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';

class LegupPromiseCard extends StatelessWidget {
  const LegupPromiseCard({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h).copyWith(top: 28.h),
      child: Column(
        children: [
          Center(
            child: Text(
              AppStrings.legUpPromise,
              style: TextStyle(
                fontWeight: FontWeight.w800,
                fontSize: 16.sp,
                fontFamily: FontFamily.epilogueExtraBold,
                color: colorScheme.primary,
              ),
            ),
          ),
          4.ph,
          Center(
            child: Text14Medium(
              AppStrings.legUpPromiseDescription,
              color: colorScheme.blue140042,
              textAlign: TextAlign.center,
              maxLines: 3,
            ),
          ),
        ],
      ),
    );
  }
}
