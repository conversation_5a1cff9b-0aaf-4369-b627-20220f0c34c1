import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/custom_cache_image.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../widgets/buttons/custom_outlined_button.dart';

class ContactSupportCard extends StatelessWidget {
  const ContactSupportCard({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 32.h),
      color: colorScheme.white,
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text20Bold(
                AppStrings.gotQuestions,
                fontSize: 18.sp,
                color: colorScheme.blue140042,
              ),
              4.ph,
              Text14Medium(
                AppStrings.ourOnboardingExpertIsJustOneCallAway,
                color: colorScheme.blue140042,
              ),
              12.ph,
              CustomOutlinedButton(
                onPressed: () {
                  launchUrl(Uri.parse('tel:${AppConfig.getInstance().metadata.contactNumber}'));
                },
                buttonWidget: Row(
                  children: [
                    SvgPicture.asset(AppImages.callIc),
                    4.pw,
                    Text14Bold(
                      AppStrings.contactUs,
                      color: colorScheme.primary,
                    ),
                  ],
                ),
              ),
            ],
          ),
          Spacer(),
          ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(54.5.r),
              bottomRight: Radius.circular(54.5.r),
            ),
            child: CustomCacheImage(
              imageUrl: AppConfig.getInstance().metadata.contactSupportImageUrl,
              height: 127.h,
              width: 175.w,
            ),
          ),
        ],
      ),
    );
  }
}
