import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/custom_outlined_button.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/enums/coupon_discount_type.dart';

class CouponCard extends StatelessWidget {
  final String title;
  final String description;
  final String discountValue;
  final String offerMaxAmount;
  final CouponDiscountType? discountType;
  final bool isApplied;
  final VoidCallback onApply;

  const CouponCard({
    super.key,
    required this.title,
    required this.description,
    required this.discountValue,
    required this.offerMaxAmount,
    this.discountType,
    required this.isApplied,
    required this.onApply,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(
            width: 34.w,
            child: CustomPaint(
              painter: CouponEdgePainter(
                color: colorScheme.success3FB68E,
                cornerRadius: 4.r,
                scallopRadius: 4.r,
              ),
              child: RotatedBox(
                quarterTurns: -1,
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.only(top: 8.h, bottom: 4.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text20ExtraBold(
                          discountType == CouponDiscountType.percentage
                              ? '$discountValue%'
                              : '₹$discountValue',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontFamily: FontFamily.nunitoSansExtraBoldItalic,
                            fontWeight: FontWeight.w800,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                        4.pw,
                        Text(
                          AppStrings.off,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                            fontFamily: FontFamily.nunitoSansExtraBoldItalic,
                            fontWeight: FontWeight.w800,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    colorScheme.lightGreenF0FAF6,
                    colorScheme.lightBrownFBF4EF,
                  ],
                  stops: [0.0, 1.0],
                ),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(4.r),
                  bottomRight: Radius.circular(4.r),
                ),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h).copyWith(left: 20.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text14Bold(
                          title,
                          fontSize: 16.sp,
                        ),
                        4.ph,
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: discountType == CouponDiscountType.percentage
                                    ? AppStrings.flatDiscount(discountValue)
                                    : '₹$discountValue',
                                style: textTheme.bodyMedium!.copyWith(
                                  fontSize: 18.sp,
                                ),
                              ),
                              WidgetSpan(child: 2.pw),
                              TextSpan(
                                text: AppStrings.off.toLowerCase(),
                                style: textTheme.displayMedium,
                              ),
                              if (offerMaxAmount.isNotEmpty) ...[
                                WidgetSpan(child: 2.pw),
                                TextSpan(
                                  text: AppStrings.offUpTo(offerMaxAmount),
                                  style: textTheme.displayMedium,
                                ),
                              ]
                            ],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        4.ph,
                        Text12Medium(
                          description,
                          fontSize: 13.sp,
                        ),
                      ],
                    ),
                  ),
                  14.pw,
                  CustomOutlinedButton(
                    width: 90.w,
                    backgroundColor: colorScheme.white,
                    borderColor: colorScheme.lightGreenB6E2D3,
                    onPressed: onApply,
                    buttonText: isApplied ? AppStrings.remove : AppStrings.apply,
                    textColor: isApplied ? colorScheme.error : colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Custom Painter for the left tab of the coupon with specific semi-circle cuts.
class CouponEdgePainter extends CustomPainter {
  final Color color;
  final double cornerRadius;  
  final double scallopRadius; // Radius of the semi-circle cutouts
  final double scallopSpacing; // Vertical distance between the end of one scallop and the start of the next
  final int numberOfScallops; // Number of cuts required (e.g., 4)

  CouponEdgePainter({
    required this.color,
    this.cornerRadius = 4.0,
    this.scallopRadius = 4.0,
    this.scallopSpacing = 6.0,
    this.numberOfScallops = 4,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    final path = Path();

    // --- Calculate Heights ---
    final double scallopDiameter = scallopRadius * 2;
    // Total vertical space used by the scallops themselves
    final double totalScallopHeight = numberOfScallops * scallopDiameter;
    // Total vertical space used by the spacing between scallops
    final double totalSpacingHeight = (numberOfScallops > 0) ? (numberOfScallops - 1) * scallopSpacing : 0;
    // Total height of the middle cut section
    final double middleSectionHeight = totalScallopHeight + totalSpacingHeight;

    // Height of the straight sections at the top and bottom
    // Ensure it's not negative if scallops take up too much space
    final double remainingHeight = max(0, size.height - middleSectionHeight);
    final double topStraightHeight = remainingHeight / 2;

    // --- Draw Path (Clockwise) ---

    // 1. Start at top-right corner (sharp)
    path.moveTo(size.width, 0);

    // 2. Top Edge (line leftwards to where the left edge begins, accounting for corner radius)
    path.lineTo(cornerRadius, 0);

    // 3. Top-Left Corner Arc (outward, like Container)
    path.arcToPoint(
      Offset(0, cornerRadius),
      radius: Radius.circular(cornerRadius),
      clockwise: false, // Outward curve
    );

    // 4. Left Edge (line down to start of scallops)
    path.lineTo(0, topStraightHeight);

    // 5. Scallops and Spacing Loop
    double currentY = topStraightHeight;
    for (int i = 0; i < numberOfScallops; i++) {
      // Draw Scallop Arc (inwards to the right)
      path.arcToPoint(
        Offset(0, currentY + scallopDiameter), // Target point after arc
        radius: Radius.circular(scallopRadius),
        clockwise: true, // Arc curves inwards (to the right)
      );
      currentY += scallopDiameter;

      // Draw Straight Spacing Segment (if not the last scallop)
      if (i < numberOfScallops - 1) {
        path.lineTo(0, currentY + scallopSpacing);
        currentY += scallopSpacing;
      }
    }

    // 6. Bottom Straight Left Edge (line down to bottom-left corner)
    path.lineTo(0, size.height - cornerRadius);

    // 7. Bottom-Left Corner Arc (outward, like Container)
    path.arcToPoint(
      Offset(cornerRadius, size.height),
      radius: Radius.circular(cornerRadius),
      clockwise: false, // Outward curve
    );

    // 8. Bottom Edge (line rightwards to bottom-right corner)
    path.lineTo(size.width, size.height);

    // 9. Right Edge (line up to top-right corner, sharp)
    path.lineTo(size.width, 0);

    // 10. Close the path
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CouponEdgePainter oldDelegate) {
    return oldDelegate.color != color ||
           oldDelegate.cornerRadius != cornerRadius ||
           oldDelegate.scallopRadius != scallopRadius ||
           oldDelegate.scallopSpacing != scallopSpacing ||
           oldDelegate.numberOfScallops != numberOfScallops;
  }
}
