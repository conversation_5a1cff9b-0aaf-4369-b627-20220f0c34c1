import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/google_map_location_picker/src/map_location_picker.dart';
import '../../../widgets/texts/app_text.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../../my_addresses/bloc/make_default_address_bloc.dart';
import '../../my_addresses/bloc/make_default_address_state.dart';
import '../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../my_addresses/bloc/my_addresses_state.dart';
import '../../my_addresses/data/my_address_model.dart';
import '../../my_addresses/presentation/widgets/address_list_shimmer.dart';

Future<bool?> changeAddressBottomSheet({
  required BuildContext context,
  required CaregiverServiceModel serviceModel,
}) async {
  return await showModalBottomSheet(
    context: context,
    isDismissible: false,
    enableDrag: false,
    isScrollControlled: true,
    useSafeArea: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    backgroundColor: Colors.white,
    builder: (context) {
      return _ChangeAddressBottomSheet(serviceModel: serviceModel);
    },
  );
}

class _ChangeAddressBottomSheet extends StatefulWidget {
  final CaregiverServiceModel serviceModel;
  const _ChangeAddressBottomSheet({required this.serviceModel});

  @override
  State<_ChangeAddressBottomSheet> createState() => _ChangeAddressBottomSheetState();
}

class _ChangeAddressBottomSheetState extends State<_ChangeAddressBottomSheet> {
  bool isChangedToScheduleForLater = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
      },
      child: Padding(
        padding: EdgeInsets.only(top: 20.h, bottom: 34.h, left: 20.w, right: 20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text20Bold('Change address'),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(Icons.close_rounded, color: colorScheme.blue150045),
                ),
              ],
            ),
            12.ph,
            Divider(
              color: colorScheme.lightGreyD5D5D5,
              height: 1,
              thickness: 1,
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    8.ph,
                    BlocConsumer<MakeDefaultAddressBloc, MakeDefaultAddressState>(
                      listener: (context, makeDefaultAddressState) {
                        if (makeDefaultAddressState is MakeDefaultAddressSuccessState) {
                          Navigator.pop(context, true);
                        }
                      },
                      builder: (context, makeDefaultAddressState) {
                        return BlocBuilder<MyAddressesBloc, MyAddressesState>(
                          builder: (context, state) {
                            if (state is MyAddressesLoadingState) {
                              return AddressListShimmer(
                                itemCount: 4,
                              );
                            }
                            if (state is MyAddressesErrorState) {
                              return SizedBox.shrink();
                            }
                            if (state is MyAddressesSuccessState) {
                              if (state.addresses?.isEmpty ?? true) {
                                return SizedBox.shrink();
                              }
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text14Medium(
                                    AppStrings.savedAddress,
                                    color: colorScheme.lightGrey61738A,
                                  ),
                                  12.ph,
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: state.addresses!.length,
                                    padding: EdgeInsets.zero,
                                    itemBuilder: (context, index) {
                                      final address = state.addresses![index];
                                      return _SavedAddressTile(
                                        isMarkingDefaultAddress: (makeDefaultAddressState is MakeDefaultAddressLoadingState &&
                                                address.id.toString() == makeDefaultAddressState.addressId),
                                        isSelected: address.isDefault == true,
                                        address: address,
                                        onAddressSelected: (MyAddressModel selectedAddress) {
                                          if (selectedAddress.isDefault == true) {
                                            Navigator.pop(context);
                                            return;
                                          }

                                          context.read<MakeDefaultAddressBloc>().markDefaultAddress(addressId:address.id.toString());
                                        },
                                      );
                                    },
                                  ),
                                ],
                              );
                            }
                            return SizedBox.shrink();
                          },
                        );
                      },
                    ),
                    AppGestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MapLocationPicker(
                                apiKey: "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
                                cameFrom: AppRoute.paymentScreen,
                              ),
                            ));
                      },
                      child: Row(
                        children: [
                          Icon(
                            Icons.add_rounded,
                            color: colorScheme.primary,
                          ),
                          4.ph,
                          Text14Bold(
                            AppStrings.addAddressManually,
                            fontSize: 16.sp,
                            color: colorScheme.primary,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class _SavedAddressTile extends StatelessWidget {
  final MyAddressModel address;
  final bool isSelected;
  final Function(MyAddressModel) onAddressSelected;
  final bool isMarkingDefaultAddress;

  const _SavedAddressTile({
    required this.address,
    required this.isSelected,
    required this.onAddressSelected,
    required this.isMarkingDefaultAddress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final bool intractable = !isMarkingDefaultAddress;

    return AppGestureDetector(
      onTap: intractable ? () => onAddressSelected(address) : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (!intractable) ...[
                SizedBox(
                  height: 20.h,
                  width: 20.h,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ] else ...[
                Container(
                  width: 20.h,
                  height: 20.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.lightGreyD5D7DA,
                      width: isSelected ? 6 : 1,
                    ),
                  ),
                ),
              ],
              8.pw,
              Text14SemiBold(
                address.title ?? '',
                color: colorScheme.blue140042,
              ),
              Spacer(),
              if (isSelected)
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: colorScheme.greenE9F7F2,
                  ),
                  child: Text14Bold(
                    AppStrings.selected.toUpperCase(),
                    color: colorScheme.success3FB68E,
                    fontSize: 12.sp,
                  ),
                )
            ],
          ),
          10.ph,
          Padding(
            padding: EdgeInsets.only(right: 55.w),
            child: Text14Medium(
              address.address ?? '',
            ),
          ),
          12.ph,
          Divider(
            color: colorScheme.lightGreyD5D5D5,
          ),
          12.ph,
        ],
      ),
    );
  }
}
