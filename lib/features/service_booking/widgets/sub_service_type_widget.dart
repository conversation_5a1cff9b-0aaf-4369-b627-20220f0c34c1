import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/routes/app_routes.dart';
import '../../../utils/extensions/list_extn.dart';
import '../../../widgets/error_state_widget.dart';
import '../../common/continue_booking/bloc/continue_booking_cubit.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../bloc/booking_cubit.dart';
import '../bloc/payment_service_cubit.dart';
import '../bloc/sub_service_cubit.dart';
import '../data/model/booking_request_model.dart';
import '../data/model/sub_service_response_model.dart';
import 'expandable_care_item.dart';
import 'shimmer/care_items_shimmer.dart';

class SubServiceTypeWidget extends StatefulWidget {
  final CaregiverServiceModel serviceModel;
  final BookingRequestModel? bookingRequestModel;

  const SubServiceTypeWidget({
    super.key,
    required this.serviceModel,
    this.bookingRequestModel,
  });

  @override
  State<SubServiceTypeWidget> createState() => _SubServiceTypeWidgetState();
}

class _SubServiceTypeWidgetState extends State<SubServiceTypeWidget> {
  List<SubServiceResponseModel> subServiceList = [];

  int? _selectedCareTypeId;
  int? _selectedDuration;
  String? _selectedLanguage;
  Set<int> _selectedAddonIds = {};

  double _calculateDisplayPrice() {
    if (_selectedCareTypeId == null || _selectedDuration == null) {
      return 0;
    }

    final selectedCareType = subServiceList.firstWhere((c) => c.id == _selectedCareTypeId);

    double totalHourlyRate = selectedCareType.price ?? 0;
    double totalAddOnPrice = 0;

    // Add cost of selected addons
    for (int addonId in _selectedAddonIds) {
      final addon = selectedCareType.attributes?.firstWhereOrNull((a) => a.id == addonId);
      totalAddOnPrice += addon?.price ?? 0;
    }

    return (totalHourlyRate * _selectedDuration!) + totalAddOnPrice;
  }

  void _handleSelect(int careTypeId) {
    if (careTypeId == _selectedCareTypeId) {
      setState(() {
        _selectedCareTypeId = null;
      });
      return;
    }
    setState(() {
      _selectedCareTypeId = careTypeId;
      _selectedAddonIds = {};
      _selectedDuration = null;
      _selectedLanguage = null;
    });
  }

  void _onDurationChanged(int? newDuration) {
    if (newDuration != null) {
      setState(() {
        _selectedDuration = newDuration;
      });
    }
  }

  void _onLanguageSelected(String? language) {
    setState(() {
      if (language != null && _selectedLanguage != language) {
        _selectedLanguage = language;
      }
    });
  }

  void _onAddonToggled(int addonId, bool isSelected) {
    setState(() {
      if (isSelected) {
        _selectedAddonIds.add(addonId);
      } else {
        _selectedAddonIds.remove(addonId);
      }
    });
  }

  void _onConfirm() {
    final finalPrice = _calculateDisplayPrice();
    final gstAmountString = (finalPrice * 0.18).toStringAsFixed(2);
    final totalAmount = (finalPrice + double.parse(gstAmountString)).toStringAsFixed(2);

    final bookingCubit = context.read<BookingCubit>();
    final initialModel = widget.bookingRequestModel;

    bool hasChanged = false;

    if (initialModel == null) {
      hasChanged = true;
    } else {
      final initialAddonIds = initialModel.serviceAttributesIds?.toSet() ?? <int>{};
      final currentAddonIds = _selectedAddonIds;

      if (initialModel.serviceSubtype != _selectedCareTypeId ||
          initialModel.duration != _selectedDuration ||
          initialModel.language != _selectedLanguage ||
          !const SetEquality().equals(initialAddonIds, currentAddonIds) ||
          initialModel.totalAmount != totalAmount.toString()
         ) {
        hasChanged = true;
      }
    }

    if (hasChanged) {
      final subService = subServiceList.firstWhereOrNull((c) => c.id == _selectedCareTypeId);

      final updatedBookingRequestModel = BookingRequestModel(
        serviceType: widget.serviceModel.id,
        serviceSubtype: _selectedCareTypeId,
        duration: _selectedDuration,
        language: _selectedLanguage,
        serviceAttributesIds: _selectedAddonIds.toList(),
        totalAmount: totalAmount.toString(),
        itemAmount: finalPrice.toStringAsFixed(2),
        gstAmount: gstAmountString.toString(),
        subServiceName: subService?.name,
        subServiceImage: subService?.image,
      );

      bookingCubit.updateBookingRequestModel(updatedBookingRequestModel);
      context.read<PaymentServiceCubit>().createOrUpdateBookingStep(
            bookingRequestModel: updatedBookingRequestModel,
            onSuccess: () {
              context.read<ContinueBookingCubit>().getLastBookingDetails();
            },
          );
    }

    if (initialModel == null) {
      Navigator.pushNamed(context, AppRoute.onDemandBookingType, arguments: widget.serviceModel);
    } else {
      Navigator.pop(context, hasChanged);
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.bookingRequestModel != null) {
      _selectedCareTypeId = widget.bookingRequestModel?.serviceSubtype;
      _selectedDuration = widget.bookingRequestModel?.duration;
      _selectedLanguage = widget.bookingRequestModel?.language;
      _selectedAddonIds = widget.bookingRequestModel?.serviceAttributesIds?.toSet() ?? {};
    }

    final serviceId = widget.serviceModel.id;
    if (serviceId == null) return;

    final cubit = context.read<SubServiceCubit>();
    if (cubit.state is SubServiceLoadingState) return;

    if (cubit.state is SubServiceSuccessState) {
      final subServiceList = (cubit.state as SubServiceSuccessState).subServiceList;
      if (subServiceList.isNotEmpty && subServiceList.first.serviceType == serviceId) {
        this.subServiceList = subServiceList;
        return;
      }
    }
    
    cubit.getSubServiceById(serviceId: serviceId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SubServiceCubit, SubServiceState>(
      builder: (context, state) {
        if (state is SubServiceLoadingState) {
          return CareItemsShimmer();
        }
        if (state is SubServiceErrorState) {
          return ErrorStateWidget(
            errorMessage: state.errorMsg,
            onRetry: () {
              context.read<SubServiceCubit>().getSubServiceById(serviceId: widget.serviceModel.id ?? 0);
            },
          );
        }
        if (state is SubServiceSuccessState) {
          subServiceList = state.subServiceList;
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 20.w).copyWith(bottom: 4.h),
            itemCount: subServiceList.length,
            itemBuilder: (context, index) {
              final subService = subServiceList[index];
              final bool isSelected = _selectedCareTypeId == subService.id;
                    
              final double displayPrice = _calculateDisplayPrice();
                    
              return ExpandableCareItem(
                model: subService,
                isSelected: isSelected,
                selectedDuration: isSelected ? _selectedDuration : null,
                selectedLanguage: isSelected ? _selectedLanguage : null,
                selectedAddonIds: isSelected ? _selectedAddonIds : const {},
                displayPrice: displayPrice,
                onSelect: () => _handleSelect(subService.id ?? 0),
                onDurationChanged: _onDurationChanged,
                onLanguageSelected: _onLanguageSelected,
                onAddonToggled: _onAddonToggled,
                onConfirm: _onConfirm,
              );
            },
          );
        }
        return SizedBox.shrink();
      },
    );
  }
}