import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/buttons/custom_outlined_button.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/model/instructions_response_model.dart';

class ServiceInstructionsWidget extends StatelessWidget {
  final List<InstructionsResponseModel> instructions;

  const ServiceInstructionsWidget({
    super.key,
    required this.instructions,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        children: [
          20.ph,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text20Bold(AppStrings.simpleAndEasyBooking),
              AppGestureDetector(
                onTap: () => Navigator.pop(context),
                child: Icon(Icons.close_rounded, color: colorScheme.blue150045),
              ),
            ],
          ),
      
          20.ph,
          Divider(height: 1, thickness: 1, color: colorScheme.lightGreyDEDEDE),
          20.ph,
          ...instructions.asMap().entries.map((entry) => _StepItem(
                number: (entry.key + 1).toString(),
                title: entry.value.title ?? '',
                description: entry.value.description ?? '',
                isLast: instructions.last.id == entry.value.id,
              )),
          20.ph,

          CustomOutlinedButton(
            width: 80.w,
            buttonWidget: Text14Bold(AppStrings.gotIt, color: colorScheme.primary),
            onPressed: () => Navigator.pop(context),
          ),
          32.ph,
        ],
      ),
    );
  }
}

class _StepItem extends StatelessWidget {
  final String number;
  final String title;
  final String description;
  final bool isLast;

  const _StepItem({
    required this.number,
    required this.title,
    required this.description,
    required this.isLast,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 10.r,
                  backgroundColor: colorScheme.success3FB68E,
                  child: Text14Bold(
                    number,
                    color: colorScheme.white,
                    fontSize: 12.sp,
                  ),
                ),
                if (!isLast)
                  Expanded(
                    child: Container(
                      width: 2,
                      color: colorScheme.success3FB68E,
                      margin: EdgeInsets.symmetric(vertical: 8.h),
                    ),
                  ),
              ],
            ),
            8.pw,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text14SemiBold(title, fontSize: 16.sp),
                  4.ph,
                  Text14SemiBold(description),
                  if (!isLast) 20.ph,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
