import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/custom_outlined_button.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/model/booking_request_model.dart';

void showPaymentSuccessSheet(
  BuildContext context, {
  required BookingRequestModel? bookingRequestModel,
}) {
  showModalBottomSheet(
    context: context,
    isDismissible: false,
    enableDrag: false,
    isScrollControlled: true,
    useSafeArea: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    builder: (BuildContext ctx) {
      final colorScheme = Theme.of(ctx).colorScheme;
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 32.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: colorScheme.greenE8F7F2,
                    child: Center(
                      child: SvgPicture.asset(
                        AppImages.successTickIcon,
                        width: 20.w,
                        height: 20.h,
                      ),
                    ),
                  ),
                ],
              ),
              20.ph,
              Text20Bold(
                bookingRequestModel?.subServiceName ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              2.ph,
              Text20Bold(
                AppStrings.bookingSuccessful,
              ),
              8.ph,
              Text14Medium(
                '${formatCustomDateTime(bookingRequestModel?.startDateTime ?? DateTime.now())}  •  ${bookingRequestModel?.duration} Hours  •  ${bookingRequestModel?.language}',
                color: colorScheme.blue140042,
              ),
              20.ph,
              CustomOutlinedButton(
                width: 120.w,
                onPressed: () => Navigator.pop(ctx),
                buttonWidget: Text14Bold(
                  AppStrings.gotIt,
                  fontSize: 16.sp,
                  color: colorScheme.primary,
                ),
              ),
              20.ph,
              SvgPicture.asset(
                AppImages.supportImg4,
                width: 24.w,
                height: 24.h,
              ),
            ],
          ),
        ),
      );
    },
  );
}

String formatCustomDateTime(DateTime dateTime) {
  final day = DateFormat('dd').format(dateTime);
  final month = DateFormat('MMM').format(dateTime).toUpperCase();
  final time = DateFormat('h:mma').format(dateTime).toLowerCase();

  return '$day $month, $time';
}
