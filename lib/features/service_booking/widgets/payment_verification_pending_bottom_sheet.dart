import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/buttons/custom_outlined_button.dart';
import '../../../widgets/texts/app_text.dart';

void showPaymentVerificationPendingSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isDismissible: false,
    enableDrag: false,
    isScrollControlled: true,
    useSafeArea: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    builder: (BuildContext ctx) {
      final colorScheme = Theme.of(ctx).colorScheme;
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 32.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: colorScheme.orangeFAE3D1.withOpacity(0.6),
                    child: Center(
                      child: Text(
                        '?',
                        style: TextStyle(
                          fontSize: 26.sp,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.orangeF0AB75,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              20.ph,
              Text20Bold(
                AppStrings.paymentVerificationPending,
                textAlign: TextAlign.center,
              ),
              8.ph,
              Text14Medium(
                AppStrings.paymentVerificationPendingDescription,
                color: colorScheme.blue140042,
              ),
              20.ph,
              CustomOutlinedButton(
                width: 120.w,
                onPressed: () => Navigator.pop(ctx),
                buttonWidget: Text14Bold(
                  AppStrings.okay,
                  fontSize: 16.sp,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

String formatCustomDateTime(DateTime dateTime) {
  final day = DateFormat('dd').format(dateTime);
  final month = DateFormat('MMM').format(dateTime).toUpperCase();
  final time = DateFormat('h:mma').format(dateTime).toLowerCase();

  return '$day $month, $time';
}
