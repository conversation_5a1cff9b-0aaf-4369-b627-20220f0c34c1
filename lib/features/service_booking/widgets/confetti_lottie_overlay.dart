// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../utils/string_constants/app_animations.dart';

void showConfettiLottieOverlay(BuildContext context) {
  final overlay = Overlay.of(context);
  late OverlayEntry overlayEntry;

  overlayEntry = OverlayEntry(
    builder: (context) => IgnorePointer(
      child: Center(
        child: Lottie.asset(
          AppAnimations.confettiLottie,
          repeat: false,
          onLoaded: (composition) {
            Future.delayed(composition.duration, () {
              overlayEntry.remove();
            });
          },
        ),
      ),
    ),
  );

  overlay.insert(overlayEntry);
}
