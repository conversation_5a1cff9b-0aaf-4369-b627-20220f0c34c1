import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/texts/app_text.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../data/enums/arrival_time_type.dart';
import '../data/model/booking_request_model.dart';
import 'change_booking_time_bottom_sheet.dart';

class SelectedServiceTimeWidget extends StatelessWidget {
  final BookingRequestModel bookingRequestModel;
  final CaregiverServiceModel serviceModel;

  const SelectedServiceTimeWidget({
    super.key,
    required this.bookingRequestModel,
    required this.serviceModel,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        SvgPicture.asset(AppImages.calendarFilledIcon),
        8.pw,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text14Medium(
                AppStrings.starts,
              ),
              2.ph,
              Text14Bold(
                bookingRequestModel.arrivalType == ArrivalTimeType.instant
                    ? AppStrings.instant15mins
                    : DateFormat('E, dd MMM, hh:mm a').format(
                        bookingRequestModel.startDateTime?.toLocal() ?? DateTime.now().toLocal(),
                      ),
              ),
            ],
          ),
        ),
        AppGestureDetector(
          onTap: () {
            changeBookingTimeBottomSheet(
              context: context,
              bookingRequestModel: bookingRequestModel,
              serviceModel: serviceModel,
            );
          },
          child: Text14Bold(
            AppStrings.change,
            color: colorScheme.primary,
          ),
        )
      ],
    );
  }
}
