import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/extensions/string_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/app_toast.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/texts/app_text.dart';
import '../../booking_type/presentation/widgets/caregiver_arrival_time_widget.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../bloc/booking_cubit.dart';
import '../data/enums/arrival_time_type.dart';
import '../data/model/booking_request_model.dart';

Future<bool?> changeBookingTimeBottomSheet({
  required BuildContext context,
  required BookingRequestModel bookingRequestModel,
  required CaregiverServiceModel serviceModel,
  bool forceUserToScheduleLater = false,
}) async {
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;
  return await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    useSafeArea: true,
    isDismissible: !forceUserToScheduleLater,
    enableDrag: !forceUserToScheduleLater,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    backgroundColor: colorScheme.white,
    builder: (context) {
      return PopScope(
        canPop: !forceUserToScheduleLater,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
        },
        child: _ChangeBookingTimeDialogContent(
          initialBookingRequestModel: bookingRequestModel,
          serviceModel: serviceModel,
          forceUserToScheduleLater: forceUserToScheduleLater,
        ),
      );
    },
  );
}

class _ChangeBookingTimeDialogContent extends StatefulWidget {
  final BookingRequestModel initialBookingRequestModel;
  final CaregiverServiceModel serviceModel;
  final bool forceUserToScheduleLater;

  const _ChangeBookingTimeDialogContent({
    required this.initialBookingRequestModel,
    required this.serviceModel,
    required this.forceUserToScheduleLater,
  });

  @override
  State<_ChangeBookingTimeDialogContent> createState() => _ChangeBookingTimeDialogContentState();
}

class _ChangeBookingTimeDialogContentState extends State<_ChangeBookingTimeDialogContent> {
  late DateTime _selectedDate;
  ArrivalTimeType? _selectedArrivalOption;

  DateTime? _initialDate;
  ArrivalTimeType? _initialArrivalOption;

  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    if (widget.forceUserToScheduleLater) {
      _selectedDate = DateTime.now();
      _selectedArrivalOption = null;
      _initialDate = null;
      _initialArrivalOption = null;
      _hasChanges = false;
    } else {
      _initialDate = widget.initialBookingRequestModel.startDateTime;
      _initialArrivalOption = widget.initialBookingRequestModel.arrivalType;
      _selectedDate = _initialDate ?? DateTime.now();
      _selectedArrivalOption = _initialArrivalOption;

      _hasChanges = false;
    }
  }

  void checkForChanges() {
    if (widget.forceUserToScheduleLater) return;

    final bool changed = _selectedArrivalOption != _initialArrivalOption || _selectedDate != _initialDate;

    if (changed != _hasChanges) {
      setState(() {
        _hasChanges = changed;
      });
    }
  }

  bool isValidTimeSelected(DateTime time) {
    final selectedTime = TimeOfDay.fromDateTime(time);
    const serviceableStartTime = TimeOfDay(hour: 10, minute: 0); // 10:00 AM
    const serviceableEndTime = TimeOfDay(hour: 20, minute: 0); // 8:00 PM

    final selectedMinutes = selectedTime.hour * 60 + selectedTime.minute;
    final startMinutes = serviceableStartTime.hour * 60 + serviceableStartTime.minute;
    final endMinutes = serviceableEndTime.hour * 60 + serviceableEndTime.minute;

    return selectedMinutes >= startMinutes && selectedMinutes <= endMinutes;
  }

  void _submitChanges() {
    if (!isValidTimeSelected(_selectedDate)) {
      CustomToast.showToast(message: AppStrings.invalidTimeSelected);
      return;
    }

    final cubit = context.read<BookingCubit>();
    final state = cubit.state;
    cubit.updateBookingRequestModel(
      state.bookingRequestModel.copyWith(
        arrivalType: _selectedArrivalOption,
        startDateTime: _selectedDate,
      ),
    );
    Navigator.pop(context, true);
  }

  bool get _isSubmitEnabled {
    if (widget.forceUserToScheduleLater) {
      return _selectedArrivalOption != null;
    } else {
      return _hasChanges;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SingleChildScrollView(
      padding: EdgeInsets.only(
        top: 20.h,
        bottom: 34.h + MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text20Bold(
                    AppStrings.caregiverArrivalTime.toUpperCaseEachFirst,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (!widget.forceUserToScheduleLater)
                  AppGestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Icon(
                      Icons.close_rounded,
                      color: colorScheme.blue150045,
                    ),
                  ),
              ],
            ),
          ),
          20.ph,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Divider(
              height: 1,
              thickness: 1,
              color: colorScheme.lightGreyDEDEDE,
            ),
          ),
          20.ph,
          CaregiverArrivalTimeWidget(
            key: ValueKey(_selectedArrivalOption),
            serviceModel: widget.serviceModel,
            selectedArrivalOption: _selectedArrivalOption,
            scheduledDateTime: _selectedDate,
            onDateTimeChanged: (newDateTime) {
              setState(() {
                _selectedDate = newDateTime;
              });
              checkForChanges();
            },
            onBookingTypeChange: (selectedArrivalOption) {
              if (selectedArrivalOption == _selectedArrivalOption) return;
              setState(() {
                _selectedArrivalOption = selectedArrivalOption;
                final now = DateTime.now();
                _selectedDate = DateTime(now.year, now.month, now.day, 10, 0);
              });
              checkForChanges();
            },
          ),
          30.ph,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: PrimaryButton(
              buttonText: AppStrings.submitChanges,
              onPressed: _isSubmitEnabled ? _submitChanges : null,
            ),
          ),
        ],
      ),
    );
  }
}