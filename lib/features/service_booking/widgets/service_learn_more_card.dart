import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/error_state_widget.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/sub_service_instructions_cubit.dart';
import 'service_instructions_widget.dart';
import 'shimmer/service_learn_more_shimmer.dart';

class ServiceLearnMoreCard extends StatefulWidget {
  final int subServiceId;

  const ServiceLearnMoreCard({
    super.key,
    required this.subServiceId,
  });

  @override
  State<ServiceLearnMoreCard> createState() => _ServiceLearnMoreCardState();
}

class _ServiceLearnMoreCardState extends State<ServiceLearnMoreCard> {
  @override
  void initState() {
    super.initState();
    context.read<SubServiceInstructionsCubit>().getInstructionsByServiceId(serviceId: widget.subServiceId);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            colorScheme.white,
            colorScheme.lightBrownFBF4EF,
          ],
          stops: [0.0, 0.75],
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.w).copyWith(top: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...[
            AppStrings.instantOrPrebook,
            AppStrings.flexibleTimeSlots,
            AppStrings.preferredLanguageSelection,
            AppStrings.expertsInAllAgeGroup,
          ].map(
            (text) => Column(
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      AppImages.infoStarGreenIc,
                      height: 18.h,
                      width: 18.h,
                    ),
                    12.pw,
                    Expanded(
                      child: Text14SemiBold(
                        text,
                        fontSize: 16.sp,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                8.ph,
              ],
            ),
          ),
          16.ph,
          BlocBuilder<SubServiceInstructionsCubit, SubServiceInstructionsState>(
            builder: (context, state) {
              if (state is SubServiceInstructionsLoadingState) {
                return const ServiceLearnMoreShimmer();
              }
              if (state is SubServiceInstructionsErrorState) {
                return ErrorStateWidget(
                  errorMessage: state.errorMsg,
                  onRetry: () {
                    context.read<SubServiceInstructionsCubit>().getInstructionsByServiceId(serviceId: widget.subServiceId);
                  },
                  showImage: false,
                  margin: EdgeInsets.zero,
                );
              }
              if (state is SubServiceInstructionsSuccessState) {
                final instructions = state.instructionsList;
                if (instructions.isEmpty) return const SizedBox.shrink();
                return Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: AppStrings.howToBookAnInstantCaregiver,
                        style: textTheme.displaySmall?.copyWith(
                          fontSize: 14.sp,
                        ),
                      ),
                      TextSpan(
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            showModalBottomSheet(
                              context: context,
                              isScrollControlled: true,
                              useSafeArea: true,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(24.r),
                                ),
                              ),
                              backgroundColor: colorScheme.white,
                              builder: (_) {
                                return ServiceInstructionsWidget(
                                  instructions: instructions,
                                );
                              },
                            );
                          },
                        text: AppStrings.learnMore,
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          20.ph,
        ],
      ),
    );
  }
}
