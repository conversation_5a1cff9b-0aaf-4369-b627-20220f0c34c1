import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:shimmer/shimmer.dart';

class CareItemsShimmer extends StatelessWidget {
  const CareItemsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return ListView.builder(
      itemCount: 3,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: index == 2 ? 0 : 16.h),
          child: Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: Container(
              height: 78.5.h,
              width: double.infinity,
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              decoration: BoxDecoration(
                color: colorScheme.shimmerColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        );
      },
    );
  }
}
