import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/theme/app_theme.dart';

class ServiceLearnMoreShimmer extends StatelessWidget {
  const ServiceLearnMoreShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Shimmer.fromColors(
      baseColor: colorScheme.shimmerBaseColor,
      highlightColor: colorScheme.shimmerHighlightColor,
      child: Container(
        height: 20.5.h,
        width: double.infinity,
        decoration: BoxDecoration(
          color: colorScheme.white,
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }
}
