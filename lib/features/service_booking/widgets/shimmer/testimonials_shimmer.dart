import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class TestimonialsShimmer extends StatelessWidget {
  const TestimonialsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final aspectRatio = MediaQuery.of(context).size.aspectRatio;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 32.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Text14Bold(
              AppStrings.legUpCustomerStories,
              color: colorScheme.blue140042,
              fontSize: 18.sp,
            ),
          ),
          16.ph,
          Shimmer.fromColors(
            baseColor: colorScheme.shimmerBaseColor,
            highlightColor: colorScheme.shimmerHighlightColor,
            child: CarouselSlider.builder(
              itemCount: 5,
              itemBuilder: (context, index, realIndex) {
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: 20.w),
                  decoration: BoxDecoration(
                    color: colorScheme.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                );
              },
              options: CarouselOptions(
                padEnds: false,
                viewportFraction: 0.625,
                height: 218.h,
                autoPlay: true,
                aspectRatio: aspectRatio,
                autoPlayInterval: Duration(seconds: 3),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
