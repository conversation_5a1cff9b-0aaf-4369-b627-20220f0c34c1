import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';

class NoCouponAppliedWidget extends StatelessWidget {
  const NoCouponAppliedWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        SvgPicture.asset(
          AppImages.couponsAndOffersIc,
        ),
        8.pw,
        Expanded(
          child: Text20Bold(
            AppStrings.couponsAndOffers,
            fontSize: 18.sp,
          ),
        ),
        Icon(
          Icons.chevron_right,
          color: colorScheme.blue140042,
        ),
      ],
    );
  }
}
