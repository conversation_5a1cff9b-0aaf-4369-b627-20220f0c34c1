import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/testimonials_cubit.dart';
import 'shimmer/testimonials_shimmer.dart';
import 'testimonial_card.dart';

class TestimonialsWidget extends StatefulWidget {
  const TestimonialsWidget({super.key});

  @override
  State<TestimonialsWidget> createState() => _TestimonialsWidgetState();
}

class _TestimonialsWidgetState extends State<TestimonialsWidget> {
  final CarouselSliderController _controller = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    final aspectRatio = MediaQuery.of(context).size.aspectRatio;
    final colorScheme = Theme.of(context).colorScheme;
    return BlocBuilder<TestimonialsCubit, TestimonialsState>(
      builder: (context, state) {
        if (state is TestimonialsLoadingState) {
          return TestimonialsShimmer();
        } else if (state is TestimonialsErrorState) {
          return SizedBox.shrink();
        } else if (state is TestimonialsSuccessState) {
          final testimonialsList = state.testimonialsList;
          if (testimonialsList.isEmpty) {
            return SizedBox.shrink();
          }
          final bool isSingleItem = testimonialsList.length == 1;
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 32.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text14Bold(
                    AppStrings.legUpCustomerStories,
                    color: colorScheme.blue140042,
                    fontSize: 18.sp,
                  ),
                ),
                16.ph,
                CarouselSlider(
                  items: testimonialsList.map((e) {
                    String name =
                        '${e.firstName != null && e.firstName!.isNotEmpty ? e.firstName : ""} ${e.lastName != null && e.lastName!.isNotEmpty ? e.lastName : ""}';
                    if (name.trim().isEmpty) {
                      name = 'Verified User';
                    }
                    return TestimonialCard(
                      imageUrl: e.profileImage ?? '',
                      quote: e.content ?? '',
                      name: name,
                      location: e.city ?? '',
                    );
                  }).toList(),
                  carouselController: _controller,
                  options: CarouselOptions(
                    padEnds: false,
                    viewportFraction: 0.61,
                    height: 220.h,
                    autoPlay: !isSingleItem,
                    enableInfiniteScroll: !isSingleItem,
                    aspectRatio: aspectRatio,
                    autoPlayInterval: Duration(seconds: 3),
                  ),
                ),
              ],
            ),
          );
        }
        return SizedBox.shrink();
      },
    );
  }
}