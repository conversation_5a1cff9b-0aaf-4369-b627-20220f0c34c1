import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/custom_drop_down_dialog.dart';
import '../../../widgets/texts/app_text.dart';
import '../data/model/sub_service_response_model.dart';

class ExpandableCareItem extends StatelessWidget {
  final SubServiceResponseModel model;
  final bool isSelected;
  final int? selectedDuration;
  final String? selectedLanguage;
  final Set<int> selectedAddonIds;
  final double displayPrice;
  final VoidCallback onSelect;
  final ValueChanged<int?> onDurationChanged;
  final ValueChanged<String?> onLanguageSelected;
  final Function(int addonId, bool isSelected) onAddonToggled;
  final VoidCallback onConfirm;

  const ExpandableCareItem({
    super.key,
    required this.model,
    required this.isSelected,
    required this.selectedDuration,
    required this.selectedLanguage,
    required this.selectedAddonIds,
    required this.displayPrice,
    required this.onSelect,
    required this.onDurationChanged,
    required this.onLanguageSelected,
    required this.onAddonToggled,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: colorScheme.whiteF7F7F7,
        borderRadius: BorderRadius.circular(8.r),
        border: isSelected
            ? Border.all(color: colorScheme.primary)
            : Border.all(color: Colors.transparent),
      ),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
      clipBehavior: Clip.antiAlias,
      child: AnimatedSize(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: Alignment.topCenter,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppGestureDetector(
              onTap: onSelect,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 20.r,
                    backgroundImage: NetworkImage(model.image ?? ''),
                    backgroundColor: Colors.grey,
                  ),
                  12.pw,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text14Bold(model.name ?? '', fontSize: 16.sp),
                        4.ph,
                        Text14SemiBold(model.subTitle ?? ''),
                      ],
                    ),
                  ),
                  4.pw,
                  PrimaryButton(
                    width: 90.w,
                    onPressed: onSelect,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      side: BorderSide(
                        color: colorScheme.lightGreenB6E2D3,
                      ),
                    ),
                    backgroundColor: colorScheme.white,
                    textColor: colorScheme.primary,
                    buttonText: isSelected ? AppStrings.selected : AppStrings.select,
                  ),
                ],
              ),
            ),
        
            if (isSelected) ...[
              16.ph,
              Divider(color: colorScheme.lightGreyDEDEDE),
              16.ph,
              Text14SemiBold(
                AppStrings.durationOfService,
                color: colorScheme.blue140042,
                fontSize: 16.sp,
              ),
              8.ph,
              CustomDialogSelectorWidget<int>(
                displayText: AppStrings.durationOfService,
                value: selectedDuration,
                selectedValue: selectedDuration != null
                    ? selectedDuration == 1
                        ? '$selectedDuration ${AppStrings.hour}'
                        : '$selectedDuration ${AppStrings.hours}'
                    : AppStrings.selectDuration,
                items: model.serviceDuration?.map((duration) {
                      return DropdownMenuItem<int>(
                        value: duration,
                        child: Text14SemiBold(
                          '$duration ${AppStrings.hours}',
                          fontSize: 16.sp,
                        ),
                      );
                    }).toList() ?? [],
                onChanged: onDurationChanged,
              ),
              16.ph,

              Text14SemiBold(
                AppStrings.caregiverLanguagePreference,
                color: colorScheme.blue140042,
                fontSize: 16.sp,
              ),
              8.ph,
              CustomDialogSelectorWidget<String>(
                displayText: AppStrings.caregiverLanguagePreference,
                value: selectedLanguage,
                selectedValue: selectedLanguage ?? AppStrings.selectLanguage,
                items: model.preferredLanguage?.map((language) {
                      return DropdownMenuItem<String>(
                        value: language,
                        child: Text14SemiBold(
                          language,
                          fontSize: 16.sp,
                        ),
                      );
                    }).toList() ?? [],
                onChanged: onLanguageSelected,
              ),
              16.ph,

              if (model.attributes?.isNotEmpty == true) ...[
                Text14SemiBold(
                  AppStrings.addOns,
                  fontSize: 16.sp,
                ),
                8.ph,
                Column(
                  children: model.attributes?.map((addon) {
                    final bool isAddonSelected = selectedAddonIds.contains(addon.id ?? 0);
                    return AppGestureDetector(
                      onTap: () => onAddonToggled(addon.id ?? 0, !isAddonSelected),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 4.h),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 24.w,
                              height: 24.h,
                              child: Checkbox(
                                value: isAddonSelected,
                                onChanged: (bool? value) {
                                  onAddonToggled(addon.id ?? 0, value == true);
                                },
                                visualDensity: VisualDensity.compact,
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                activeColor: colorScheme.primary,
                              ),
                            ),
                            8.pw,
                            Expanded(
                              child: Text16Medium(
                                addon.name ?? '',
                                color: colorScheme.blue140042,
                              ),
                            ),
                            4.pw,
                            Text14Bold(
                              addon.price != null && addon.price! > 0
                                  ? '₹${addon.price?.toStringAsFixed(2)}'
                                  : AppStrings.free,
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList() ?? [],
                ),
                16.ph,
              ],

              Divider(color: colorScheme.lightGreyDEDEDE),
              16.ph,
      
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text14Bold(
                        '₹${displayPrice.toStringAsFixed(2)}',
                        fontSize: 16.sp,
                      ),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: '₹${(model.price ?? 0).toStringAsFixed(2)}/',
                              style: textTheme.displayMedium,
                            ),
                            TextSpan(
                              text: AppStrings.hour,
                              style: textTheme.displayMedium?.copyWith(fontSize: 12.sp),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  PrimaryButton(
                    width: 100.w,
                    disabledBackgroundColor: colorScheme.lightGreyD5D5D5,
                    onPressed: (selectedDuration != null && selectedLanguage != null)
                        ? onConfirm
                        : null,
                    child: Text14Bold(
                      AppStrings.confirm,
                      color: (selectedDuration != null && selectedLanguage != null)
                          ? colorScheme.white
                          : null,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
