import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../widgets/custom_cache_image.dart';
import '../../../widgets/texts/app_text.dart';

class TestimonialCard extends StatelessWidget {
  final String imageUrl;
  final String quote;
  final String name;
  final String location;

  const TestimonialCard({
    super.key,
    required this.imageUrl,
    required this.quote,
    required this.name,
    required this.location,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      width: 211.w,
      height: 218.h,
      margin: EdgeInsets.only(left: 20.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: colorScheme.blueEFF4FB,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          bottomRight: Radius.circular(12.r),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 20.r,
            backgroundColor: colorScheme.lightGreyD5D5D5,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: CustomCacheImage(
                height: 40.w,
                width: 40.w,
                fit: BoxFit.cover,
                imageUrl: imageUrl,
                errorWidget: Center(
                  child: Text(
                    _getInitials(name),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ),
          8.pw,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text14Medium(
                  "“$quote”",
                  color: colorScheme.blue140042,
                  lineHeight: 1.4,
                  maxLines: 7,
                  overflow: TextOverflow.ellipsis,
                ),
                2.ph,
                Text(
                  '~ $name ${location.isNotEmpty ? ", " : ""}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: FontFamily.nunitoSansMediumItalic,
                    fontWeight: FontWeight.w500,
                    fontStyle: FontStyle.italic,
                    color: colorScheme.blue140042,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                ),
                Text(
                  location,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: FontFamily.nunitoSansMediumItalic,
                    fontWeight: FontWeight.w500,
                    fontStyle: FontStyle.italic,
                    color: colorScheme.blue140042,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) {
      return '';
    }
    List<String> nameParts = name.trim().split(' ');
    if (nameParts.length == 1) {
      return nameParts[0].isNotEmpty ? nameParts[0][0].toUpperCase() : '';
    } else if (nameParts.length > 1) {
      String firstInitial = nameParts.first.isNotEmpty ? nameParts.first[0].toUpperCase() : '';
      String lastInitial = nameParts.last.isNotEmpty ? nameParts.last[0].toUpperCase() : '';
      if (firstInitial.isEmpty && lastInitial.isNotEmpty) return lastInitial;
      if (lastInitial.isEmpty && firstInitial.isNotEmpty) return firstInitial;
      return '$firstInitial$lastInitial';
    }
    return '';
  }
}
