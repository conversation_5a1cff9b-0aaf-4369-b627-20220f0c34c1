import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../widgets/texts/app_text.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../data/model/booking_request_model.dart';
import 'sub_service_type_widget.dart';

Future<bool?> changeSubServiceBottomSheet({
  required BuildContext context,
  required CaregiverServiceModel serviceModel,
  required BookingRequestModel bookingRequestModel,
}) async {
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;
  return await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    useSafeArea: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(24.r),
      ),
    ),
    backgroundColor: colorScheme.white,
    builder: (context) {
      return Padding(
        padding: EdgeInsets.only(top: 20.h, bottom: 34.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text20Bold(
                      'Change ${serviceModel.name} type',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Icon(Icons.close_rounded, color: colorScheme.blue150045),
                  ),
                ],
              ),
            ),
            20.ph,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Divider(
                height: 1,
                thickness: 1,
                color: colorScheme.lightGreyDEDEDE,
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    20.ph,
                    SubServiceTypeWidget(
                      serviceModel: serviceModel,
                      bookingRequestModel: bookingRequestModel,
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      );
    },
  );
}
