// ignore_for_file: use_build_context_synchronously
import 'dart:typed_data';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/extensions/string_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/app_toast.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/texts/app_text.dart';
import '../../booking_type/bloc/instant_service_available_cubit.dart';
import '../../booking_type/data/instant_service_available_request_model.dart';
import '../../booking_type/presentation/widgets/selected_service_widget.dart';
import '../../common/continue_booking/bloc/continue_booking_cubit.dart';
import '../../dashboard/bloc/is_area_serviceable_cubit.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../../my_addresses/bloc/make_default_address_bloc.dart';
import '../../my_addresses/bloc/make_default_address_state.dart';
import '../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../my_addresses/bloc/my_addresses_state.dart';
import '../bloc/booking_cubit.dart';
import '../bloc/coupon_offers_cubit.dart';
import '../bloc/payment_service_cubit.dart';
import '../data/enums/arrival_time_type.dart';
import '../data/enums/coupon_discount_type.dart';
import '../data/enums/payment_type.dart';
import '../data/enums/transaction_status_type.dart';
import '../data/model/booking_request_model.dart';
import '../widgets/address_instant_unavailable_dialog.dart';
import '../widgets/address_not_serviceable_dialog.dart';
import '../widgets/change_address_bottom_sheet.dart';
import '../widgets/change_booking_time_bottom_sheet.dart';
import '../widgets/confetti_lottie_overlay.dart';
import '../widgets/no_coupon_applied_widget.dart';
import '../widgets/payment_failed_bottom_sheet.dart';
import '../widgets/payment_success_bottom_sheet.dart';
import '../widgets/payment_verification_pending_bottom_sheet.dart';
import '../widgets/selected_service_time_widget.dart';

class ServicePaymentsScreen extends StatefulWidget {
  final CaregiverServiceModel serviceModel;

  const ServicePaymentsScreen({
    super.key,
    required this.serviceModel,
  });

  @override
  State<ServicePaymentsScreen> createState() => _ServicePaymentsScreenState();
}

class _ServicePaymentsScreenState extends State<ServicePaymentsScreen> {
  String? selectedPaymentMethodId;
  PaymentType? selectedPaymentType;

  int? oldAddressId;
  bool isBottomSheetOpen = false;

  void _handlePayment({
    required BuildContext context,
    required PaymentServiceState state,
    required BookingRequestModel bookingRequestModel,
    bool isAddressChanged = false,
  }) {
    final cubit = context.read<PaymentServiceCubit>();

    if (state is PaymentServiceCancelledState && state.paymentIDResponseModel != null) {
      cubit.retryPaymentFlow(
        bookingRequestModel: bookingRequestModel,
        selectedPaymentType: selectedPaymentType!,
        selectedPaymentMethodId: selectedPaymentMethodId ?? '',
        paymentIDResponseModel: state.paymentIDResponseModel!,
      );
    } else {
      cubit.initiatePaymentFlow(
      bookingRequestModel: bookingRequestModel,
      selectedPaymentType: selectedPaymentType!,
        selectedPaymentMethodId: selectedPaymentMethodId ?? '',
      );
    }
  }

  @override
  void initState() {
    super.initState();
    final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;
    context.read<CouponOffersCubit>().getCouponOffersByItemTotal(itemTotal: bookingRequestModel.itemAmount?.toString() ?? '0');
  }

  void _handleCouponMaxUsageReached() {
    final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;
    context.read<CouponOffersCubit>().getCouponOffersByItemTotal(itemTotal: bookingRequestModel.itemAmount?.toString() ?? '0');
    double itemAmount = double.tryParse(bookingRequestModel.itemAmount ?? '0.0') ?? 0.0;

    // Remove coupon
    bookingRequestModel.offerId = null;
    bookingRequestModel.offerDeductedAmount = null;

    // GST calculation on original itemAmount
    final gstAmount = itemAmount * 0.18;
    bookingRequestModel.gstAmount = gstAmount.toStringAsFixed(2);
    bookingRequestModel.totalAmount = (itemAmount + gstAmount).toStringAsFixed(2);

    context.read<BookingCubit>().updateBookingRequestModel(bookingRequestModel);

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BlocBuilder<BookingCubit, BookingState>(
      builder: (context, state) {
        final bookingRequestModel = state.bookingRequestModel;
        return BlocListener<PaymentServiceCubit, PaymentServiceState>(
          listener: (context, state) {
            if (state is PaymentServiceLoadingState) {
              if (state.isShowingLoadingDialog) return;
              Dialogs.showLoadingDialog(context, loadingText: state.loadingMessage);
            }

            if (state is PaymentServiceErrorState) {
              Dialogs.hideDialog(context);
              if (state.errorCode == 'COUPON_MAX_USAGE_REACHED') {
                _handleCouponMaxUsageReached();
              }
              if (state.paymentVerificationResponseModel != null) {
                showPaymentFailedSheet(context, onRetry: () {
                  _handlePayment(context: context, state: state, bookingRequestModel: bookingRequestModel);
                });
                return;
              }

              CustomToast.showToast(message: state.errorMsg);
              context.read<ContinueBookingCubit>().getLastBookingDetails();
            }

            if (state is PaymentServiceSuccessState) {
              if (!state.isPaymentScreen) return;
              Dialogs.hideDialog(context);
              context.read<ContinueBookingCubit>().getLastBookingDetails();
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoute.bookingDetails,
                (route) => route.settings.name == AppRoute.appBottomNavBar,
                arguments: state.bookingResponseModel?.id ?? 0,
              );
              if (state.paymentVerificationResponseModel?.transactionStatus == TransactionStatusType.success) {
                showPaymentSuccessSheet(context, bookingRequestModel: bookingRequestModel);
              } else {
                showPaymentVerificationPendingSheet(context);
              }
            }

            if (state is PaymentServiceCancelledState) {
              Dialogs.hideDialog(context);
              context.read<ContinueBookingCubit>().getLastBookingDetails();
            }
          },
          child: Scaffold(
            appBar: CustomAppbar(title: AppStrings.completePayment),
            body: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    20.ph,
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.w,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SelectedServiceWidget(
                            bookingRequestModel: state.bookingRequestModel,
                            serviceModel: widget.serviceModel,
                            allowChange: false,
                          ),
                          16.ph,
                          Divider(
                            color: colorScheme.lightGreyDEDEDE,
                          ),
                          16.ph,
                          SelectedServiceTimeWidget(bookingRequestModel: state.bookingRequestModel, serviceModel: widget.serviceModel,),
                          16.ph,
                        ],
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      color: colorScheme.surface,
                      height: 8.h,
                    ),
                    AppGestureDetector(
                      onTap: () {
                        Navigator.pushNamed(context, AppRoute.couponsOffers, arguments: bookingRequestModel.itemAmount?.toString()).then((value) {
                          if (value == true) {
                            showConfettiLottieOverlay(context);
                          }
                          setState(() {});
                        });
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                        child: BlocBuilder<CouponOffersCubit, CouponOffersState>(
                          builder: (context, state) {
                            if (state is CouponOffersSuccessState) {
                              final couponOffers = state.couponOffersList;
                              if (bookingRequestModel.offerId == null || couponOffers.isEmpty) {
                                return NoCouponAppliedWidget();
                              }
                              final appliedCoupon = couponOffers.firstWhereOrNull((element) => element.id == bookingRequestModel.offerId);
                              if (appliedCoupon == null) {
                                return NoCouponAppliedWidget();
                              }
                              return Row(
                                children: [
                                  SvgPicture.asset(
                                    AppImages.couponsAndOffersIc,
                                  ),
                                  8.pw,
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Text14Bold(
                                              appliedCoupon.discountType == CouponDiscountType.percentage
                                                  ? AppStrings.flatDiscount(appliedCoupon.discountValue?.toStringAsFixed(0) ?? '')
                                                  : '₹${appliedCoupon.discountValue?.toStringAsFixed(0)}',
                                              fontSize: 16.sp,
                                              color: colorScheme.blue140042,
                                            ),
                                            4.pw,
                                            Text14Bold(
                                              AppStrings.applied.toUpperCase(),
                                              fontSize: 16.sp,
                                              color: colorScheme.success3FB68E,
                                            ),
                                          ],
                                        ),
                                        4.ph,
                                        Text14SemiBold(
                                          AppStrings.codeWithCode(appliedCoupon.code ?? ''),
                                          fontSize: 16.sp,
                                          color: colorScheme.lightGrey6B6B6B,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.chevron_right,
                                    color: colorScheme.blue140042,
                                  ),
                                ],
                              );
                            }
                            return NoCouponAppliedWidget();
                          },
                        ),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      color: colorScheme.surface,
                      height: 8.h,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.h),
                            child: Text20Bold(
                              AppStrings.selectPaymentMethod,
                              fontSize: 18.sp,
                            ),
                          ),
                          Builder(
                            builder: (context) {
                              final cubit = context.read<PaymentServiceCubit>();
                              final cashfreeService = cubit.cashfreeService;
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (cashfreeService.upiAppsList.isNotEmpty) ...[
                                    Padding(
                                      padding: EdgeInsets.symmetric(vertical: 16.h),
                                      child: Row(
                                        children: [
                                          Image.asset(
                                            AppImages.upiIcon,
                                            width: 36.h,
                                            height: 24.h,
                                          ),
                                          8.pw,
                                          Text14SemiBold(
                                            AppStrings.payByAnyUpiApp,
                                            color: colorScheme.blue140042,
                                            fontSize: 16.sp,
                                          ),
                                        ],
                                      ),
                                    ),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: const NeverScrollableScrollPhysics(),
                                      itemCount: cashfreeService.upiAppsList.length,
                                      itemBuilder: (context, index) {
                                        final app = cashfreeService.upiAppsList[index];
                                        final isSelected = selectedPaymentMethodId == app.id;
                                        return Padding(
                                          padding: EdgeInsets.symmetric(vertical: 8.h),
                                          child: Column(
                                            children: [
                                              AppGestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    selectedPaymentType = PaymentType.upi;
                                                    selectedPaymentMethodId = app.id;
                                                  });
                                                },
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.center,
                                                  children: [
                                                    CircleAvatar(
                                                      radius: 18.h,
                                                      backgroundColor: colorScheme.white,
                                                      child: Image.memory(
                                                        app.decodedIconBytes ?? Uint8List(0),
                                                        fit: BoxFit.contain,
                                                        width: 24.h,
                                                        height: 24.h,
                                                        errorBuilder: (context, error, stackTrace) {
                                                          return Icon(
                                                            Icons.payment,
                                                            color: colorScheme.lightGreyDEDEDE,
                                                            size: 24.h,
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    8.pw,
                                                    Expanded(
                                                      child: Text14Bold(
                                                        app.displayName?.toUpperCaseEachFirst ?? '',
                                                        fontSize: 16.sp,
                                                        maxLines: 1,
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                    ),
                                                    Container(
                                                      width: 20.h,
                                                      height: 20.h,
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        border: Border.all(
                                                          color: isSelected
                                                              ? colorScheme.primary
                                                              : colorScheme.lightGreyD5D7DA,
                                                          width: isSelected ? 6 : 1,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              16.ph,
                                              Padding(
                                                padding: EdgeInsets.only(
                                                  left: index == cashfreeService.upiAppsList.length - 1
                                                      ? 0
                                                      : 42.w,
                                                ),
                                                child: Divider(
                                                  color: colorScheme.lightGreyDEDEDE,
                                                  height: 1.h,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                    16.ph,
                                  ],
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w).copyWith(bottom: 16.h),
                      child: AppGestureDetector(
                        onTap: () {
                          setState(() {
                            selectedPaymentType = PaymentType.other;
                            selectedPaymentMethodId = null;
                          });
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding: EdgeInsets.all(6.h),
                              child: SvgPicture.asset(AppImages.creditCardIcon),
                            ),
                            8.pw,
                            Expanded(
                              child: Text14Bold(
                                AppStrings.payByCard,
                                fontSize: 16.sp,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              width: 20.h,
                              height: 20.h,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: selectedPaymentType == PaymentType.other
                                      ? colorScheme.primary
                                      : colorScheme.lightGreyD5D7DA,
                                  width: selectedPaymentType == PaymentType.other ? 6 : 1,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      color: colorScheme.surface,
                      height: 8.h,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.w, horizontal: 20.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          8.ph,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text14Bold(
                                AppStrings.totalToPay,
                                fontSize: 16.sp,
                              ),
                              Text14Bold(
                                "₹${bookingRequestModel.totalAmount}",
                                fontSize: 16.sp,
                              ),
                            ],
                          ),
                          4.ph,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text14SemiBold(
                                AppStrings.itemTotal,
                                color: colorScheme.lightGrey6B6B6B,
                              ),
                              Text14SemiBold(
                                "₹${bookingRequestModel.itemAmount}",
                                color: colorScheme.lightGrey6B6B6B,
                              ),
                            ],
                          ),
                          4.ph,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text14SemiBold(
                                AppStrings.gst18,
                                color: colorScheme.lightGrey6B6B6B,
                              ),
                              Text14SemiBold(
                                "₹${bookingRequestModel.gstAmount}",
                                color: colorScheme.lightGrey6B6B6B,
                              ),
                            ],
                          ),
                          if (bookingRequestModel.offerDeductedAmount != null) ...[
                            4.ph,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text14SemiBold(
                                  AppStrings.couponDiscount,
                                  color: colorScheme.success3FB68E,
                                ),
                                Text14SemiBold(
                                  "-₹${bookingRequestModel.offerDeductedAmount}",
                                  color: colorScheme.success3FB68E,
                                ),
                              ],
                            ),
                          ],
                          8.ph,
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 12.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  AppImages.securePaymentIcon,
                                  height: 32.h,
                                  width: 32.w,
                                ),
                                4.pw,
                                Text14Bold(
                                  AppStrings.safeAndSecurePayment,
                                  color: colorScheme.success3FB68E,
                                  fontSize: 16.sp,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: SafeArea(
              child: Container(
                decoration: BoxDecoration(
                  color: colorScheme.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 6,
                      offset: Offset(0, -3),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 16.h),
                      child: MultiBlocListener(
                        listeners: [
                          BlocListener<MakeDefaultAddressBloc, MakeDefaultAddressState>(
                            listenWhen: (previous, current) => isBottomSheetOpen != true,
                            listener: (context, state) {
                              if (state is MakeDefaultAddressLoadingState) {
                                Dialogs.showLoadingDialog(context, loadingText: 'Changing address');
                              }

                              if (state is MakeDefaultAddressErrorState) {
                                Dialogs.hideDialog(context);
                                CustomToast.showToast(message: state.errorMsg);
                              }

                              if (state is MakeDefaultAddressSuccessState) {
                                Dialogs.hideDialog(context);
                                context.read<MyAddressesBloc>().getAddresses();
                              }
                            },
                          ),
                          BlocListener<MyAddressesBloc, MyAddressesState>(
                            listener: (context, state) {
                              if (state is MyAddressesSuccessState) {
                                context.read<IsAreaServiceableCubit>().getIsAreaServiceableService(
                                      lat: state.selectedAddress?.location?.first ?? 0,
                                      lng: state.selectedAddress?.location?.last ?? 0,
                                    );
                              }
                            },
                          ),
                          BlocListener<InstantServiceAvailableCubit, InstantServiceAvailableState>(
                            listener: (context, state) {
                              if (state is InstantServiceAvailableLoadingState) {
                                Dialogs.showLoadingDialog(context, loadingText: AppStrings.checkingAddressServiceability);
                              }
                              if (state is InstantServiceAvailableErrorState) {
                                Dialogs.hideDialog(context);
                                CustomToast.showToast(message: state.errorMsg);
                              }
                              if (state is InstantServiceAvailableSuccessState) {
                                Dialogs.hideDialog(context);
                                final bookingCubit = context.read<BookingCubit>();
                                final arrivalType = bookingCubit.state.bookingRequestModel.arrivalType;
                                if (arrivalType == ArrivalTimeType.instant && state.instantServiceAvailableModel.available != true) {
                                  showAddressInstantUnavailableDialog(
                                    context,
                                    onScheduleForLaterPressed: () {
                                      Navigator.pop(context);
                                      changeBookingTimeBottomSheet(
                                        context: context,
                                        bookingRequestModel: bookingRequestModel,
                                        serviceModel: widget.serviceModel,
                                        forceUserToScheduleLater: true,
                                      );
                                    },
                                    onCancelPressed: () {
                                      Navigator.pop(context);
                                      context.read<MakeDefaultAddressBloc>().markDefaultAddress(addressId: oldAddressId.toString());
                                      setState(() {
                                        oldAddressId = null;
                                      });
                                    },
                                  );
                                } else {
                                  changeBookingTimeBottomSheet(
                                    context: context,
                                    bookingRequestModel: bookingRequestModel,
                                    serviceModel: widget.serviceModel,
                                    forceUserToScheduleLater: true,
                                  );
                                }
                              }
                            },
                          ),
                          BlocListener<IsAreaServiceableCubit, IsAreaServiceableState>(
                            listener: (context, state) {
                              if (state is IsAreaServiceableSuccessState) {
                                if (!state.isAreaServiceable) {
                                  showAddressNotServiceableDialog(
                                    context,
                                    onPressedDiscardBooking: () {
                                      Navigator.pushNamedAndRemoveUntil(context, AppRoute.appBottomNavBar, (route) => false);
                                    },
                                    onCancelPressed: () {
                                      Navigator.pop(context);
                                      context.read<MakeDefaultAddressBloc>().markDefaultAddress(addressId: oldAddressId.toString());
                                      setState(() {
                                        oldAddressId = null;
                                      });
                                    },
                                  );
                                } else {
                                  final addressState = context.read<MyAddressesBloc>().state;
                                  if (addressState is MyAddressesSuccessState) {
                                    context.read<InstantServiceAvailableCubit>().getIsInstantServiceAvailable(instantServiceAvailableReqModel: InstantServiceAvailableReqModel(
                                            workType: widget.serviceModel.caregiverServiceType?.toString(),
                                            language: bookingRequestModel.language,
                                            serviceAttributes: bookingRequestModel.serviceAttributesIds?.join(","),
                                            serviceType: bookingRequestModel.serviceType?.toString(),
                                            subService: bookingRequestModel.serviceSubtype?.toString(),
                                            duration: bookingRequestModel.duration?.toString(),
                                            lat: addressState.selectedAddress?.location?.first.toString(),
                                            lng: addressState.selectedAddress?.location?.last.toString(),
                                          ),
                                        );
                                  }
                                }
                              }
                            },
                          ),
                        ],
                        child: BlocBuilder<MyAddressesBloc, MyAddressesState>(
                          builder: (context, addressState) {
                            if (addressState is MyAddressesLoadingState) {
                              return Shimmer.fromColors(
                                baseColor: colorScheme.shimmerBaseColor,
                                highlightColor: colorScheme.shimmerHighlightColor,
                                child: Container(
                                  height: 19.5.h,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: colorScheme.white,
                                    borderRadius: BorderRadius.circular(12.r),
                                  ),
                                ),
                              );
                            }
                            if (addressState is MyAddressesErrorState) {
                              return SizedBox.shrink();
                            }
                            if (addressState is MyAddressesSuccessState) {
                              return Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    AppImages.homeIcon,
                                    height: 24.h,
                                    width: 24.w,
                                  ),
                                  8.pw,
                                  Flexible(
                                    flex: 3,
                                    child: Text14Bold(
                                      addressState.selectedAddress?.title ?? '',
                                      fontSize: 16.sp,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  4.pw,
                                  Expanded(
                                    flex: 10,
                                    child: Text14SemiBold(
                                      addressState.selectedAddress?.address ?? '',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      color: colorScheme.lightGrey6B6B6B,
                                    ),
                                  ),
                                  AppGestureDetector(
                                    onTap: () async {
                                      final address = addressState.selectedAddress;
                                      setState(() {
                                        oldAddressId = address?.id;
                                        isBottomSheetOpen = true;
                                      });

                                      final result = await changeAddressBottomSheet(context: context, serviceModel: widget.serviceModel);

                                      setState(() {
                                        isBottomSheetOpen = false;
                                      });

                                      if (result == true) {
                                        CustomToast.showToast(message: AppStrings.addressUpdated, isInfo: true);
                                        context.read<MyAddressesBloc>().getAddresses();
                                        return;
                                      }
                        
                                      final arguments = ModalRoute.of(context)?.settings.arguments as Map;
                                      final addressJson = arguments['result'] as Map<String, dynamic>?;
                        
                                      if (addressJson != null) {
                                        context.read<MyAddressesBloc>().getAddresses();
                                      }
                                    },
                                    child: Text14Bold(
                                      AppStrings.change,
                                      color: colorScheme.primary,
                                    ),
                                  ),
                                ],
                              );
                            }
                            return SizedBox.shrink();
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                      child: BlocBuilder<PaymentServiceCubit, PaymentServiceState>(
                        builder: (context, state) {
                          return PrimaryButton(
                            padding: EdgeInsets.zero,
                            disabledBackgroundColor: colorScheme.lightGreyD5D5D5,
                            onPressed: selectedPaymentType != null && state is! PaymentServiceLoadingState
                                ? () {
                                    final addressCubit = context.read<MyAddressesBloc>();
                                    final addressState = addressCubit.state;
                                    if (addressState is MyAddressesSuccessState) {
                                      final address = addressState.selectedAddress;
                                      if (address != null) {
                                        final isAddressChanged = address.id != bookingRequestModel.addressDetails?.id;

                                        final bookingCubit = context.read<BookingCubit>();
                                        final bookingModel = bookingRequestModel.copyWith(
                                          addressDetails: AddressDetails(
                                            id: address.id,
                                            title: address.title,
                                            address: address.address,
                                            houseNo: address.houseNo,
                                            city: address.city,
                                            state: address.state,
                                            country: address.country,
                                            pincode: address.pincode,
                                            location: address.location,
                                          ),
                                        );
                                        bookingCubit.updateBookingRequestModel(bookingModel);

                                        _handlePayment(
                                          context: context,
                                          state: state,
                                          bookingRequestModel: bookingModel,
                                          isAddressChanged: isAddressChanged,
                                        );
                                      }
                                    }
                                  }
                                : null,
                            child: Builder(
                              builder: (context) {
                                if (state is PaymentServiceErrorState) {
                                  return Text14Bold(
                                    'Retry Payment for ₹${bookingRequestModel.totalAmount}',
                                    fontSize: 16.sp,
                                    color: colorScheme.white,
                                  );
                                }
                                return Text14Bold(
                                  selectedPaymentType != null
                                      ? "Pay ₹${bookingRequestModel.totalAmount}"
                                      : "Select payment to Pay ₹${bookingRequestModel.totalAmount}",
                                  fontSize: 16.sp,
                                  color: colorScheme.white,
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
