import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/custom_cache_image.dart';
import '../../../widgets/texts/app_text.dart';
import '../../dashboard/data/model/caregiver_service_model.dart';
import '../bloc/payment_service_cubit.dart';
import '../bloc/sub_service_cubit.dart';
import '../bloc/testimonials_cubit.dart';
import '../widgets/contact_support_card.dart';
import '../widgets/legup_promise_card.dart';
import '../widgets/service_learn_more_card.dart';
import '../widgets/sub_service_type_widget.dart';
import '../widgets/testimonials_widget.dart';

class ServiceBookingScreen extends StatefulWidget {
  final CaregiverServiceModel serviceModel;

  const ServiceBookingScreen({
    super.key,
    required this.serviceModel,
  });

  @override
  State<ServiceBookingScreen> createState() => _ServiceBookingScreenState();
}

class _ServiceBookingScreenState extends State<ServiceBookingScreen> {
  @override
  void initState() {
    super.initState();
    context.read<TestimonialsCubit>().getUserTestimonials();
    context.read<SubServiceCubit>().getSubServiceById(serviceId: widget.serviceModel.id ?? 0);
    context.read<PaymentServiceCubit>().reset();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    return Scaffold(
      appBar: CustomAppbar(title: AppStrings.instantCare(widget.serviceModel.name ?? ''),),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(
                  left: 20.w,
                  right: 20.w,
                  top: 20.h,
                  bottom: 16.h,
                ),
                padding: EdgeInsets.only(left: 16.h),
                decoration: BoxDecoration(
                  color: colorScheme.greenE8F7F2,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.r),
                    bottomRight: Radius.circular(8.r),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text14Bold(
                            '${AppStrings.expert} ${widget.serviceModel.name}',
                            fontSize: 16.sp,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: 'in ',
                                  style: textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                                ),
                                TextSpan(
                                  text: '15mins',
                                  style: textTheme.bodyMedium?.copyWith(
                                    fontSize: 16.sp,
                                    color: colorScheme.success3FB68E,
                                  ),
                                ),
                              ],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          8.ph,
                          Text14Medium(AppStrings.startsAt),
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '₹${(double.tryParse(widget.serviceModel.basePrice ?? '0') ?? 0).toStringAsFixed(0)}',
                                  style: textTheme.bodyMedium?.copyWith(fontSize: 18.sp),
                                ),
                                TextSpan(
                                  text: AppStrings.perHour,
                                  style: textTheme.displayMedium,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    8.pw,
                    ClipRRect(
                      borderRadius: BorderRadius.only(
                        bottomRight: Radius.circular(8.r),
                      ),
                      child: CustomCacheImage(
                        imageUrl: widget.serviceModel.image ?? '',
                        height: 124.h,
                        width: 124.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ],
                ),
              ),
              8.ph,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Text14Bold(
                  AppStrings.selectServiceType(widget.serviceModel.name ?? ''),
                  fontSize: 16.sp,
                  color: colorScheme.blue140042,
                ),
              ),
              8.ph,
              SubServiceTypeWidget(serviceModel: widget.serviceModel),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
                child: Divider(color: colorScheme.lightGreyDEDEDE),
              ),
              ServiceLearnMoreCard(subServiceId: widget.serviceModel.id ?? 0),
              Container(
                width: double.infinity,
                color: colorScheme.success3FB68E,
                padding: EdgeInsets.symmetric(vertical: 8.h),
                child: Center(
                  child: Text14Bold(
                    AppStrings.verifiedProfessionals,
                    color: colorScheme.white,
                  ),
                ),
              ),
              TestimonialsWidget(),
              LegupPromiseCard(),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                child: SvgPicture.asset(
                  AppImages.supportImg4,
                ),
              ),
              ContactSupportCard(),
            ],
          ),
        ),
      ),
    );
  }
}