import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_toast.dart';
import '../../../widgets/buttons/custom_outlined_button.dart';
import '../../../widgets/custom_appbar.dart';
import '../../../widgets/error_state_widget.dart';
import '../../../widgets/textfields/app_textfield.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/booking_cubit.dart';
import '../bloc/coupon_offers_cubit.dart';
import '../data/enums/coupon_discount_type.dart';
import '../data/model/coupon_offers_model.dart';
import '../widgets/coupon_card.dart';

class CouponOffersScreen extends StatefulWidget {
  final String itemTotal;

  const CouponOffersScreen({
    super.key,
    required this.itemTotal,
  });

  @override
  State<CouponOffersScreen> createState() => _CouponOffersScreenState();
}

class _CouponOffersScreenState extends State<CouponOffersScreen> {
  final _formKey = GlobalKey<FormState>();
  final couponController = TextEditingController();

  void _applyCoupon(CouponOffersModel couponOffer) {
    final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;

    double itemAmount = double.tryParse(bookingRequestModel.itemAmount ?? '0.0') ?? 0.0;

    // Coupon already applied -> remove it
    if (bookingRequestModel.offerId == couponOffer.id) {
      bookingRequestModel.offerId = null;
      bookingRequestModel.offerDeductedAmount = null;

      // GST calculation on original itemAmount
      final gstAmount = itemAmount * 0.18;
      bookingRequestModel.gstAmount = gstAmount.toStringAsFixed(2);

      // Total = itemAmount + gstAmount
      bookingRequestModel.totalAmount = (itemAmount + gstAmount).toStringAsFixed(2);

      context.read<BookingCubit>().updateBookingRequestModel(bookingRequestModel);
    } else {
      double calculatedDiscount = 0.0;

      if (couponOffer.discountType == CouponDiscountType.percentage) {
        calculatedDiscount = (itemAmount * (couponOffer.discountValue ?? 0)) / 100;
      } else if (couponOffer.discountType == CouponDiscountType.fixed) {
        calculatedDiscount = couponOffer.discountValue ?? 0.0;
      }

      // Clamp discount if necessary
      if (couponOffer.maxAmount != null && couponOffer.maxAmount! > 0 && calculatedDiscount > couponOffer.maxAmount!) {
        calculatedDiscount = couponOffer.maxAmount!;
      }

      if (couponOffer.minAmount != null && itemAmount < couponOffer.minAmount!) {
        calculatedDiscount = 0.0;
      }

      if (calculatedDiscount > 0) {
        double discountedItemAmount = itemAmount - calculatedDiscount;

        // GST calculated on discounted subtotal
        final gstAmount = discountedItemAmount * 0.18;

        bookingRequestModel.offerId = couponOffer.id;
        bookingRequestModel.offerDeductedAmount = calculatedDiscount.toStringAsFixed(2);
        bookingRequestModel.couponCode = couponOffer.code;
        bookingRequestModel.gstAmount = gstAmount.toStringAsFixed(2);

        // Final Total = discounted item amount + gst
        bookingRequestModel.totalAmount = (discountedItemAmount + gstAmount).toStringAsFixed(2);

        context.read<BookingCubit>().updateBookingRequestModel(bookingRequestModel);
        Navigator.pop(context, true);
      } else {
        Navigator.pop(context, false);
      }
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: CustomAppbar(title: AppStrings.couponsAndOffers),
      body: BlocBuilder<CouponOffersCubit, CouponOffersState>(
        builder: (context, state) {
          if (state is CouponOffersLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is CouponOffersErrorState) {
            return ErrorStateWidget(
              errorMessage: state.errorMsg,
              onRetry: () {
                context.read<CouponOffersCubit>().getCouponOffersByItemTotal(itemTotal: widget.itemTotal);
              },
            );
          }
          if (state is CouponOffersSuccessState) {
            if (state.couponOffersList.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.only(bottom: 100.h),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 300.h,
                        width: 300.w,
                        child: SvgPicture.asset(
                          AppImages.emptyCouponsImage,
                        ),
                      ),
                      Transform.translate(
                        offset: Offset(0, -35.h),
                        child: Text20Bold(
                          AppStrings.noCouponsAvailable,
                          fontSize: 18.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
            final couponOffersList = state.couponOffersList;
            final bookingRequestModel = context.read<BookingCubit>().state.bookingRequestModel;
            final appliedCoupon = couponOffersList.firstWhereOrNull((element) => element.id == bookingRequestModel.offerId);
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text14Medium(
                          AppStrings.applyCoupon,
                          fontSize: 16.sp,
                        ),
                        8.ph,
                        Form(
                          key: _formKey,
                          child: AppTextFormField(
                            controller: couponController,
                            hintText: AppStrings.enterCouponCode,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return ValidationMsg.plsEnterCouponCode;
                              }
                              return null;
                            },
                            suffixIcon: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: CustomOutlinedButton(
                                onPressed: () {
                                  if (_formKey.currentState?.validate() == true) {
                                    final coupon = couponOffersList.firstWhereOrNull(
                                      (element) => element.code?.toLowerCase() == couponController.text.toLowerCase(),
                                    );
                                    if (coupon != null) {
                                      if (bookingRequestModel.offerId != coupon.id) {
                                        _applyCoupon(coupon);
                                      } else {
                                        CustomToast.showToast(message: AppStrings.couponAlreadyApplied);
                                      }
                                    } else {
                                      CustomToast.showToast(message: AppStrings.couponNotFound);
                                    }
                                  }
                                },
                                borderColor: colorScheme.lightGreenB6E2D3,
                                buttonText: AppStrings.apply,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    color: colorScheme.surface,
                    height: 8.h,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text20Bold(
                          AppStrings.availableOffers,
                          fontSize: 18.sp,
                        ),
                        16.ph,
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: state.couponOffersList.length,
                          itemBuilder: (context, index) {
                            final couponOffer = couponOffersList[index];
                            return Padding(
                              padding: EdgeInsets.only(top: 16.h),
                              child: CouponCard(
                                isApplied: appliedCoupon?.id == couponOffer.id,
                                title: couponOffer.title ?? '',
                                description: couponOffer.description ?? '',
                                discountValue: couponOffer.discountValue?.toStringAsFixed(0) ?? '',
                                offerMaxAmount: couponOffer.maxAmount?.toStringAsFixed(0) ?? '',
                                discountType: couponOffer.discountType,
                                onApply: () {
                                  _applyCoupon(couponOffer);
                                },
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  20.ph,
                ],
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }

  @override
  void dispose() {
    couponController.dispose();
    super.dispose();
  }
}
