import '../../../../utils/string_constants/app_strings.dart';

enum BookingStatusType {
  pending(AppStrings.bookingStatusPending, 'PE'),
  booked(AppStrings.bookingStatusBooked, 'BO'),
  confirmed(AppStrings.bookingStatusConfirmed, 'CF'),
  started(AppStrings.bookingStatusStarted, 'ST'),
  completed(AppStrings.bookingStatusCompleted, 'CO'),
  cancelled(AppStrings.bookingStatusCancelled, 'CA'),
  failed(AppStrings.bookingStatusFailed, 'FA');

  final String apiValue;
  final String apiParamValue;

  const BookingStatusType(this.apiValue, this.apiParamValue);

  static BookingStatusType? getBookingStatusTypeFromString(String? apiValue) {
    switch (apiValue) {
      case AppStrings.bookingStatusPending:
        return BookingStatusType.pending;
      case AppStrings.bookingStatusBooked:
        return BookingStatusType.booked;
      case AppStrings.bookingStatusConfirmed:
        return BookingStatusType.confirmed;
      case AppStrings.bookingStatusStarted:
        return BookingStatusType.started;
      case AppStrings.bookingStatusCompleted:
        return BookingStatusType.completed;
      case AppStrings.bookingStatusCancelled:
        return BookingStatusType.cancelled;
      case AppStrings.bookingStatusFailed:
        return BookingStatusType.failed;
      default:
        return null;
    }
  }
}
