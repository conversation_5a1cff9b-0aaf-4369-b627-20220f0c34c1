import '../../../../utils/string_constants/app_strings.dart';

enum TransactionStatusType {
  pending(AppStrings.transactionStatusPending),
  success(AppStrings.transactionStatusSuccess),
  failed(AppStrings.transactionStatusFailed),
  cancelled(AppStrings.transactionStatusCancelled),
  refundInitiated(AppStrings.transactionStatusRefundInitiated),
  refundCompleted(AppStrings.transactionStatusRefundCompleted),
  refundFailed(AppStrings.transactionStatusRefundFailed),
  expired(AppStrings.transactionStatusExpired),
  stale(AppStrings.transactionStatusStale);

  final String apiValue;

  const TransactionStatusType(this.apiValue);

  static TransactionStatusType? getTransactionStatusTypeFromString(String? apiValue) {
    switch (apiValue) {
      case AppStrings.transactionStatusPending:
        return TransactionStatusType.pending;
      case AppStrings.transactionStatusSuccess:
        return TransactionStatusType.success;
      case AppStrings.transactionStatusFailed:
        return TransactionStatusType.failed;
      case AppStrings.transactionStatusCancelled:
        return TransactionStatusType.cancelled;
      case AppStrings.transactionStatusRefundInitiated:
        return TransactionStatusType.refundInitiated;
      case AppStrings.transactionStatusRefundCompleted:
        return TransactionStatusType.refundCompleted;
      case AppStrings.transactionStatusRefundFailed:
        return TransactionStatusType.refundFailed;
      case AppStrings.transactionStatusExpired:
        return TransactionStatusType.expired;
      case AppStrings.transactionStatusStale:
        return TransactionStatusType.stale;
      default:
        return null;
    }
  }
}
