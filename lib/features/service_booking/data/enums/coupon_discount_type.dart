import '../../../../utils/string_constants/enum_strings.dart';

enum CouponDiscountType {
  fixed(EnumStrings.fixed),
  percentage(EnumStrings.percentage);

  final String apiValue;

  const CouponDiscountType(this.apiValue);

  static CouponDiscountType? getCouponDiscountTypeFromString(String? apiValue) {
    switch (apiValue) {
      case EnumStrings.fixed:
        return CouponDiscountType.fixed;
      case EnumStrings.percentage:
        return CouponDiscountType.percentage;
      default:
        return null;
    }
  }
}
