import '../../../../utils/string_constants/app_strings.dart';

enum ArrivalTimeType {
  instant(AppStrings.instantIn),
  schedule(AppStrings.instantSL);

  final String _name;

  const ArrivalTimeType(this._name);

  @override
  String toString() => _name;

  static ArrivalTimeType? getArrivalTimeTypeFromString(String? arrivalTimeType) {
    switch (arrivalTimeType) {
      case AppStrings.instantIn:
        return ArrivalTimeType.instant;
      case AppStrings.instantSL:
        return ArrivalTimeType.schedule;
      default:
        return null;
    }
  }
}
