class PaymentIDResponseModel {
  int? bookingId;
  int? paymentId;
  String? cfOrderId;
  String? paymentSessionId;
  int? attemptNumber;

  PaymentIDResponseModel({
    this.bookingId,
    this.paymentId,
    this.cfOrderId,
    this.paymentSessionId,
    this.attemptNumber,
  });

  factory PaymentIDResponseModel.fromJson(Map<String, dynamic> json) {
    return PaymentIDResponseModel(
      bookingId: json['booking_id'] as int?,
      paymentId: json['payment_id'] as int?,
      cfOrderId: json['cf_order_id'] as String?,
      paymentSessionId: json['payment_session_id'] as String?,
      attemptNumber: json['attempt_number'] as int?,
    );
  }
}
