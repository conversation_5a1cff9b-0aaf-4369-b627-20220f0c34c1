import '../enums/coupon_discount_type.dart';

class CouponOffersModel {
  int? id;
  String? code;
  String? title;
  String? description;
  CouponDiscountType? discountType;
  double? discountValue;
  double? minAmount;
  double? maxAmount;

  CouponOffersModel({
    this.id,
    this.code,
    this.title,
    this.description,
    this.discountType,
    this.discountValue,
    this.minAmount,
    this.maxAmount,
  });

  factory CouponOffersModel.fromJson(Map<String, dynamic> json) {
    return CouponOffersModel(
      id: json['id'] as int?,
      code: json['code'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      discountType: CouponDiscountType.getCouponDiscountTypeFromString(json['discount_type'] as String?),
      discountValue: (json['discount_value'] as num?)?.toDouble(),
      minAmount: (json['min_amount'] as num?)?.toDouble(),
      maxAmount: (json['max_amount'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'title': title,
      'description': description,
      'discount_type': discountType,
      'discount_value': discountValue,
      'min_amount': minAmount,
      'max_amount': maxAmount,
    };
  }
}
