class SubServiceResponseModel {
  int? id;
  String? name;
  String? image;
  int? serviceType;
  String? subTitle;
  List<String>? preferredLanguage;
  double? price;
  List<int>? serviceDuration;
  List<Attribute>? attributes;

  SubServiceResponseModel({
    this.id,
    this.name,
    this.image,
    this.serviceType,
    this.subTitle,
    this.preferredLanguage,
    this.price,
    this.serviceDuration,
    this.attributes,
  });

  factory SubServiceResponseModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return SubServiceResponseModel();
    return SubServiceResponseModel(
      id: json['id'] as int?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      serviceType: json['service_type'] as int?,
      subTitle: json['sub_title'] as String?,
      preferredLanguage: (json['preferred_language'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      price: json['price'] != null
          ? double.tryParse(json['price'].toString())
          : null,
      serviceDuration: (json['service_duration'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      attributes: (json['attributes'] as List<dynamic>?)
          ?.map((e) => Attribute.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class Attribute {
  int? id;
  String? name;
  String? attributeCode;
  String? description;
  double? price;
  int? priority;

  Attribute({
    this.id,
    this.name,
    this.attributeCode,
    this.description,
    this.price,
    this.priority,
  });

  factory Attribute.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Attribute();
    return Attribute(
      id: json['id'] as int?,
      name: json['name'] as String?,
      attributeCode: json['attribute_code'] as String?,
      description: json['description'] as String?,
      price: json['price'] != null
          ? double.tryParse(json['price'].toString())
          : null,
      priority: json['priority'] as int?,
    );
  }
}
