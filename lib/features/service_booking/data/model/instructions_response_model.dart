class InstructionsResponseModel {
  int? id;
  int? serviceType;
  String? title;
  String? description;
  int? order;

  InstructionsResponseModel({
    this.id,
    this.serviceType,
    this.title,
    this.description,
    this.order,
  });

  factory InstructionsResponseModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return InstructionsResponseModel();
    return InstructionsResponseModel(
      id: json['id'] as int?,
      serviceType: json['service_type'] as int?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      order: json['order'] as int?,
    );
  }
}
