import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';

class UpiAppData {
  final String id;
  final String? displayName;
  final String? iconBytes;
  Uint8List? decodedIconBytes;

  UpiAppData({required this.id, this.displayName, this.iconBytes}) {
    if (iconBytes != null && iconBytes!.isNotEmpty) {
      try {
        decodedIconBytes = base64Decode(iconBytes!);
      } catch (e) {
        log("Error decoding base64 for app $id: $e");
        decodedIconBytes = null;
      }
    }
  }
}
