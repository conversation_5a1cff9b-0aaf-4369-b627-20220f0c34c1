import '../enums/arrival_time_type.dart';

class BookingRequestModel {
  int? serviceType;
  int? serviceSubtype;
  int? duration;
  String? language;
  ArrivalTimeType? arrivalType;
  DateTime? startDateTime;
  List<int>? serviceAttributesIds;
  AddressDetails? addressDetails;
  String? totalAmount;
  String? itemAmount;
  String? gstAmount;
  // extra fields
  String? subServiceName;
  String? subServiceImage;
  bool? popup;
  int? offerId;
  String? offerDeductedAmount;
  String? couponCode;

  BookingRequestModel({
    this.serviceType,
    this.serviceSubtype,
    this.duration,
    this.language,
    this.arrivalType,
    this.startDateTime,
    this.serviceAttributesIds,
    this.addressDetails,
    this.totalAmount,
    this.itemAmount,
    this.gstAmount,
    this.subServiceName,
    this.subServiceImage,
    this.popup,
    this.offerId,
    this.offerDeductedAmount,
    this.couponCode,
  });

  Map<String, dynamic> toJson({required bool isBooking}) {
    if (isBooking == true) {
      return {
        if (serviceType != null) 'service_type': serviceType,
        if (serviceSubtype != null) 'service_subtype': serviceSubtype,
        if (duration != null) 'duration': duration,
        if (language != null) 'language': language,
        if (arrivalType != null) 'arrival_type': arrivalType?.toString(),
        if (startDateTime != null) 'start_date_time': startDateTime?.toUtc().toIso8601String(),
        if (serviceAttributesIds != null && serviceAttributesIds!.isNotEmpty) 'service_attributes_ids': serviceAttributesIds,
        if (addressDetails != null) 'address_details': addressDetails?.toJson(),
        if (totalAmount != null) 'total_amount': totalAmount,
        if (itemAmount != null) 'item_amount': itemAmount,
        if (gstAmount != null) 'gst_amount': gstAmount,
        if (popup != null) 'popup': popup,
        if (offerId != null) 'offer': offerId,
        if (offerDeductedAmount != null) 'offer_deducted_amount': offerDeductedAmount,
      };
    }

    return {
      'total_amount': totalAmount,
      'item_amount': itemAmount,
      'gst_amount': gstAmount,
    };
  }

  BookingRequestModel copyWith({
    int? serviceType,
    int? serviceSubtype,
    int? duration,
    String? language,
    ArrivalTimeType? arrivalType,
    DateTime? startDateTime,
    List<int>? serviceAttributesIds,
    AddressDetails? addressDetails,
    String? totalAmount,
    String? itemAmount,
    String? gstAmount,
    String? subServiceName,
    String? subServiceImage,
    bool? popup,
    int? offerId,
    String? offerDeductedAmount,
    String? couponCode,
  }) {
    return BookingRequestModel(
      serviceType: serviceType ?? this.serviceType,
      serviceSubtype: serviceSubtype ?? this.serviceSubtype,
      duration: duration ?? this.duration,
      language: language ?? this.language,
      arrivalType: arrivalType ?? this.arrivalType,
      startDateTime: startDateTime ?? this.startDateTime,
      serviceAttributesIds: serviceAttributesIds ?? this.serviceAttributesIds,
      addressDetails: addressDetails ?? this.addressDetails,
      totalAmount: totalAmount ?? this.totalAmount,
      itemAmount: itemAmount ?? this.itemAmount,
      gstAmount: gstAmount ?? this.gstAmount,
      subServiceName: subServiceName ?? this.subServiceName,
      subServiceImage: subServiceImage ?? this.subServiceImage,
      popup: popup ?? this.popup,
      offerId: offerId ?? this.offerId,
      offerDeductedAmount: offerDeductedAmount ?? this.offerDeductedAmount,
      couponCode: couponCode ?? this.couponCode,
    );
  }
}

class AddressDetails {
  int? id;
  String? title;
  String? address;
  String? houseNo;
  String? city;
  String? state;
  String? country;
  String? pincode;
  List<double>? location;

  AddressDetails({
    this.id,
    this.title,
    this.address,
    this.houseNo,
    this.city,
    this.state,
    this.country,
    this.pincode,
    this.location,
  });

  factory AddressDetails.fromJson(Map<String, dynamic> json) {
    return AddressDetails(
      id: json['id'],
      title: json['title'],
      address: json['address'],
      houseNo: json['house_no'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      pincode: json['pincode'],
      location: json['location'] != null
          ? List<double>.from(json["location"].map((x) => x.toDouble()))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // 'id': id,
      'title': title,
      'address': address,
      'house_no': houseNo,
      'city': city,
      'state': state,
      'country': country,
      'pincode': pincode,
      'location': location,
    };
  }
}
