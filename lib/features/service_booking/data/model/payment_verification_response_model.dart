import '../enums/booking_status_type.dart';
import '../enums/transaction_status_type.dart';

class PaymentVerificationResponseModel {
  final String? bookingId;
  final DateTime? bookingStartDateTime;
  final int? bookingDuration;
  final String? bookingLanguage;
  final String? bookingServiceType;
  final String? bookingServiceSubtype;
  final BookingStatusType? bookingStatus;
  final String? orderId;
  final String? cfOrderId;
  final String? totalAmount;
  final TransactionStatusType? transactionStatus;
  final String? reason;

  PaymentVerificationResponseModel({
    this.bookingId,
    this.bookingStartDateTime,
    this.bookingDuration,
    this.bookingLanguage,
    this.bookingServiceType,
    this.bookingServiceSubtype,
    this.bookingStatus,
    this.orderId,
    this.cfOrderId,
    this.totalAmount,
    this.transactionStatus,
    this.reason,
  });

  factory PaymentVerificationResponseModel.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationResponseModel(
      bookingId: json['booking_id'] as String?,
      bookingStartDateTime: json['booking_start_date_time'] != null
          ? DateTime.tryParse(json['booking_start_date_time'])
          : null,
      bookingDuration: json['booking_duration'] as int?,
      bookingLanguage: json['booking_language'] as String?,
      bookingServiceType: json['booking_service_type'] as String?,
      bookingServiceSubtype: json['booking_service_subtype'] as String?,
      bookingStatus: BookingStatusType.getBookingStatusTypeFromString(json['booking_status'] as String?),
      orderId: json['order_id'] as String?,
      cfOrderId: json['cf_order_id'] as String?,
      totalAmount: json['total_amount'] as String?,
      transactionStatus: TransactionStatusType.getTransactionStatusTypeFromString(json['transaction_status'] as String?),
      reason: json['reason'] as String?,
    );
  }
}
