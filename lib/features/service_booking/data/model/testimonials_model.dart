class TestimonialsModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isActive;
  final String? profileImage;
  final String? firstName;
  final String? lastName;
  final String? city;
  final String? content;
  final int? createdBy;
  final int? updatedBy;
  final int? user;

  TestimonialsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.isActive,
    this.profileImage,
    this.firstName,
    this.lastName,
    this.city,
    this.content,
    this.createdBy,
    this.updatedBy,
    this.user,
  });

  factory TestimonialsModel.fromJson(Map<String, dynamic> json) {
    return TestimonialsModel(
      id: json['id'] as int?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      isActive: json['is_active'] as bool?,
      profileImage: json['profile_image'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      city: json['city'] as String?,
      content: json['content'] as String?,
      createdBy: json['created_by'] as int?,
      updatedBy: json['updated_by'] as int?,
      user: json['user'] as int?,
    );
  }
}
