part of 'sub_service_instructions_cubit.dart';

sealed class SubServiceInstructionsState extends Equatable {
  const SubServiceInstructionsState();

  @override
  List<Object> get props => [];
}

final class SubServiceInstructionsInitState extends SubServiceInstructionsState {
  @override
  List<Object> get props => [];
}

final class SubServiceInstructionsLoadingState extends SubServiceInstructionsState {
  @override
  List<Object> get props => [];
}

final class SubServiceInstructionsSuccessState extends SubServiceInstructionsState {
  final List<InstructionsResponseModel> instructionsList;

  const SubServiceInstructionsSuccessState(this.instructionsList);

  @override
  List<Object> get props => [instructionsList];
}

final class SubServiceInstructionsErrorState extends SubServiceInstructionsState {
  final String errorMsg;

  const SubServiceInstructionsErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
