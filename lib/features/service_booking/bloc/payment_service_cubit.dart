import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';

import '../../../core/api/api_service.dart';
import '../../../core/services/cashfree_service.dart';
import '../../../utils/common_models/api_response_model.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../booking_details/data/model/booking_details_response_model.dart';
import '../data/enums/payment_type.dart';
import '../data/enums/transaction_status_type.dart';
import '../data/model/booking_request_model.dart';
import '../data/model/payment_id_response_model.dart';
import '../data/model/payment_verification_response_model.dart';

part 'payment_service_state.dart';

class PaymentServiceCubit extends Cubit<PaymentServiceState> {
  late CashfreeService cashfreeService;

  PaymentServiceCubit() : super(const PaymentServiceInitState()) {
    cashfreeService = CashfreeService(
      onVerifyPayment: _handleCashfreeVerification,
      onError: _handleCashfreeError,
    );
  }

  static const int maxVerificationAttempts = 12;
  static const Duration verificationInterval = Duration(seconds: 5);

  void reset() {
    emit(const PaymentServiceInitState());
  }

  void updateBookingResponseModel(BookingDetailsResponseModel bookingResponseModel) {
    emit(PaymentServiceInitState(
      bookingResponseModel: bookingResponseModel,
    ));
  }

  void createOrUpdateBookingStep({
    required BookingRequestModel bookingRequestModel,
    VoidCallback? onSuccess,
  }) async {
    emit(PaymentServiceLoadingState(
      loadingMessage: AppStrings.processing,
      bookingRequestModel: bookingRequestModel,
      bookingResponseModel: state.bookingResponseModel,
      paymentIDResponseModel: state.paymentIDResponseModel,
      paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      isShowingLoadingDialog: (state is PaymentServiceLoadingState),
    ));

    try {
      ApiResponse response;

      if (state.bookingResponseModel?.id != null) {
        response = await ApiService.instance.updateBookingById(bookingRequestModel: bookingRequestModel, bookingId: state.bookingResponseModel!.id!);
      } else {
        response = await ApiService.instance.createNewBooking(bookingRequestModel: bookingRequestModel);
      }

      if (response.success) {
        final bookingResponseModel = BookingDetailsResponseModel.fromJson(response.data as Map<String, dynamic>);
        emit(PaymentServiceSuccessState(
          bookingRequestModel: bookingRequestModel,
          bookingResponseModel: bookingResponseModel,
          paymentIDResponseModel: state.paymentIDResponseModel,
          paymentVerificationResponseModel: state.paymentVerificationResponseModel,
          isPaymentScreen: false,
        ));
        onSuccess?.call();
      } else {
        emit(PaymentServiceErrorState(
          errorMsg: response.message ?? AppStrings.genericErrorMsg,
          bookingRequestModel: bookingRequestModel,
          bookingResponseModel: state.bookingResponseModel,
          paymentIDResponseModel: state.paymentIDResponseModel,
          paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        ));
      }
    } catch (e) {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel ?? bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }

  void initiatePaymentFlow({
    required BookingRequestModel bookingRequestModel,
    required PaymentType selectedPaymentType,
    String? selectedPaymentMethodId,
  }) async {
    try {
      emit(PaymentServiceLoadingState(
        loadingMessage: AppStrings.processing,
        bookingRequestModel: bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        isShowingLoadingDialog: (state is PaymentServiceLoadingState),
      ));

      ApiResponse bookingApiResponse;

      if (state.bookingResponseModel?.id != null) {
        bookingApiResponse = await ApiService.instance.updateBookingById(bookingRequestModel: bookingRequestModel, bookingId: state.bookingResponseModel!.id!);
      } else {
        bookingApiResponse = await ApiService.instance.createNewBooking(bookingRequestModel: bookingRequestModel);
      }

      if (bookingApiResponse.success) {
        final bookingResponseModel = BookingDetailsResponseModel.fromJson(bookingApiResponse.data as Map<String, dynamic>);

        getPaymentIdAndInitiatePaymentFlow(
          bookingRequestModel: bookingRequestModel,
          selectedPaymentType: selectedPaymentType,
          selectedPaymentMethodId: selectedPaymentMethodId,
          bookingResponseModel: bookingResponseModel,
        );
      } else {
        emit(PaymentServiceErrorState(
          errorMsg: bookingApiResponse.message ?? AppStrings.genericErrorMsg,
          bookingRequestModel: state.bookingRequestModel,
          bookingResponseModel: state.bookingResponseModel,
          paymentIDResponseModel: state.paymentIDResponseModel,
          paymentVerificationResponseModel: state.paymentVerificationResponseModel,
          errorCode: bookingApiResponse.errors['error_code'],
        ));
      }
    } catch (e) {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel ?? bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }

  void retryPaymentFlow({
    required BookingRequestModel bookingRequestModel,
    required PaymentType selectedPaymentType,
    String? selectedPaymentMethodId,
    required PaymentIDResponseModel paymentIDResponseModel,
  }) async {
    try {
      emit(PaymentServiceLoadingState(
        loadingMessage: AppStrings.processing,
        bookingRequestModel: bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        isShowingLoadingDialog: (state is PaymentServiceLoadingState),
      ));

      ApiResponse bookingApiResponse;

      if (state.bookingResponseModel?.id != null) {
        bookingApiResponse = await ApiService.instance.updateBookingById(bookingRequestModel: bookingRequestModel, bookingId: state.bookingResponseModel!.id!);
      } else {
        bookingApiResponse = await ApiService.instance.createNewBooking(bookingRequestModel: bookingRequestModel);
      }

      if (bookingApiResponse.success) {
        final bookingResponseModel = BookingDetailsResponseModel.fromJson(bookingApiResponse.data as Map<String, dynamic>);

        openCashFreeWithPaymentId(
          bookingRequestModel: bookingRequestModel,
          selectedPaymentType: selectedPaymentType,
          selectedPaymentMethodId: selectedPaymentMethodId,
          bookingResponseModel: bookingResponseModel,
          paymentIDResponseModel: paymentIDResponseModel,
        );
      } else {
        emit(PaymentServiceErrorState(
          errorMsg: bookingApiResponse.message ?? AppStrings.genericErrorMsg,
          bookingRequestModel: state.bookingRequestModel,
          bookingResponseModel: state.bookingResponseModel,
          paymentIDResponseModel: state.paymentIDResponseModel,
          paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        ));
      }
    } catch (e) {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel ?? bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }

  void getPaymentIdAndInitiatePaymentFlow({
    required BookingRequestModel bookingRequestModel,
    required PaymentType selectedPaymentType,
    String? selectedPaymentMethodId,
    required BookingDetailsResponseModel bookingResponseModel,
  }) async {
    try {
      emit(PaymentServiceLoadingState(
        loadingMessage: AppStrings.processing,
        bookingRequestModel: bookingRequestModel,
        bookingResponseModel: bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        isShowingLoadingDialog: (state is PaymentServiceLoadingState),
      ));

      final cashfreeOrderResponse = await ApiService.instance.generateCashfreeOrder(
        bookingRequestModel: bookingRequestModel,
        bookingId: state.bookingResponseModel?.id ?? 0,
      );

      if (cashfreeOrderResponse.success) {
        final paymentIDResponseModel = PaymentIDResponseModel.fromJson(cashfreeOrderResponse.data as Map<String, dynamic>);

        openCashFreeWithPaymentId(
          bookingRequestModel: bookingRequestModel,
          selectedPaymentType: selectedPaymentType,
          selectedPaymentMethodId: selectedPaymentMethodId,
          bookingResponseModel: bookingResponseModel,
          paymentIDResponseModel: paymentIDResponseModel,
        );
      } else {
        emit(PaymentServiceErrorState(
          errorMsg: cashfreeOrderResponse.message ?? AppStrings.genericErrorMsg,
          bookingRequestModel: state.bookingRequestModel,
          bookingResponseModel: state.bookingResponseModel,
          paymentIDResponseModel: state.paymentIDResponseModel,
          paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        ));
      }
    } catch (e) {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel ?? bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }

  void openCashFreeWithPaymentId({
    required BookingRequestModel bookingRequestModel,
    required PaymentType selectedPaymentType,
    String? selectedPaymentMethodId,
    required BookingDetailsResponseModel bookingResponseModel,
    required PaymentIDResponseModel paymentIDResponseModel,
  }) async {
    try {
      emit(PaymentServiceLoadingState(
        loadingMessage: AppStrings.processing,
        bookingRequestModel: bookingRequestModel,
        bookingResponseModel: bookingResponseModel,
        paymentIDResponseModel: paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        isShowingLoadingDialog: (state is PaymentServiceLoadingState),
      ));

      if (selectedPaymentType == PaymentType.upi && selectedPaymentMethodId != null && selectedPaymentMethodId.isNotEmpty) {
        await cashfreeService.upiIntentPayTapped(
          appUpiId: selectedPaymentMethodId,
          orderId: paymentIDResponseModel.cfOrderId!,
          paymentSessionId: paymentIDResponseModel.paymentSessionId!,
        );
      } else {
        await cashfreeService.webCheckout(
          orderId: paymentIDResponseModel.cfOrderId!,
          paymentSessionId: paymentIDResponseModel.paymentSessionId!,
        );
      }
    } catch (e) {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel ?? bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }

  /// Callback handler for Cashfree's successful transaction signal (onVerify)
  void _handleCashfreeVerification(String orderId) {
    if (orderId == state.paymentIDResponseModel?.cfOrderId) {
      // Trigger backend verification
      _verifyPaymentOnBackend(bookingId: state.bookingResponseModel?.id ?? 0);
    } else {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }

  /// Callback handler for Cashfree's error signal (onError)
  void _handleCashfreeError(CFErrorResponse errorResponse, String? orderId) {
    log("error: ${errorResponse.getMessage()}");
    final errorCode = errorResponse.getCode();
    if (errorCode == 'action_cancelled') {
      emit(PaymentServiceCancelledState(
        bookingRequestModel: state.bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
      return;
    }
    if (state.bookingResponseModel != null && state.bookingRequestModel != null) {
      if (orderId != null) {
        _verifyPaymentOnBackend(bookingId: state.bookingResponseModel!.id ?? 0);
      } else {
        emit(PaymentServiceErrorState(
          errorMsg: errorResponse.getMessage() ?? AppStrings.genericErrorMsg,
          bookingRequestModel: state.bookingRequestModel,
          bookingResponseModel: state.bookingResponseModel,
          paymentIDResponseModel: state.paymentIDResponseModel,
          paymentVerificationResponseModel: state.paymentVerificationResponseModel,
        ));
      }
    }
  }

  /// Calls the backend API to verify the payment status
  Future<void> _verifyPaymentOnBackend({required int bookingId}) async {
    // emit(PaymentServiceVerificationLoadingState(
    //   loadingMessage: AppStrings.verifyingPayment,
    //   bookingRequestModel: state.bookingRequestModel,
    //   bookingResponseModel: state.bookingResponseModel,
    //   paymentIDResponseModel: state.paymentIDResponseModel,
    //   paymentVerificationResponseModel: state.paymentVerificationResponseModel,
    // ));

    try {
      for (int i = 1; i <= maxVerificationAttempts; i++) {
        final response = await ApiService.instance.verifyCashfreePayment(bookingId: bookingId);
        if (response.success) {
          final paymentVerificationResponse = PaymentVerificationResponseModel.fromJson(response.data as Map<String, dynamic>);
          
          if (paymentVerificationResponse.transactionStatus == TransactionStatusType.success) {
            emit(PaymentServiceSuccessState(
              bookingRequestModel: state.bookingRequestModel,
              bookingResponseModel: state.bookingResponseModel,
              paymentIDResponseModel: state.paymentIDResponseModel,
              paymentVerificationResponseModel: paymentVerificationResponse,
            ));
            break;
          }

          if (paymentVerificationResponse.transactionStatus == TransactionStatusType.failed ||
              paymentVerificationResponse.transactionStatus == TransactionStatusType.expired) {
            emit(PaymentServiceErrorState(
              errorMsg: paymentVerificationResponse.reason ?? AppStrings.genericErrorMsg,
              bookingRequestModel: state.bookingRequestModel,
              bookingResponseModel: state.bookingResponseModel,
              paymentIDResponseModel: state.paymentIDResponseModel,
              paymentVerificationResponseModel: paymentVerificationResponse,
            ));
            break;
          }

          if (i == maxVerificationAttempts && paymentVerificationResponse.transactionStatus == TransactionStatusType.pending) {
            emit(PaymentServiceSuccessState(
              bookingRequestModel: state.bookingRequestModel,
              bookingResponseModel: state.bookingResponseModel,
              paymentIDResponseModel: state.paymentIDResponseModel,
              paymentVerificationResponseModel: state.paymentVerificationResponseModel,
            ));
            break;
          }

          await Future.delayed(verificationInterval);
        } else {
          emit(PaymentServiceErrorState(
            errorMsg: response.message ?? AppStrings.genericErrorMsg,
            bookingRequestModel: state.bookingRequestModel,
            bookingResponseModel: state.bookingResponseModel,
            paymentIDResponseModel: state.paymentIDResponseModel,
            paymentVerificationResponseModel: state.paymentVerificationResponseModel,
          ));
          break;
        }
      }
    } catch (e) {
      emit(PaymentServiceErrorState(
        errorMsg: AppStrings.genericErrorMsg,
        bookingRequestModel: state.bookingRequestModel,
        bookingResponseModel: state.bookingResponseModel,
        paymentIDResponseModel: state.paymentIDResponseModel,
        paymentVerificationResponseModel: state.paymentVerificationResponseModel,
      ));
    }
  }
}