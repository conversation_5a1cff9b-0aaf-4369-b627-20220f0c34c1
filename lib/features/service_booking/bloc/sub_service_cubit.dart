import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/sub_service_response_model.dart';

part 'sub_service_state.dart';

class SubServiceCubit extends Cubit<SubServiceState> {
  SubServiceCubit() : super(SubServiceInitState());

  void getSubServiceById({required int serviceId}) async {
    emit(SubServiceLoadingState());

    try {
      final response = await ApiService.instance.getSubServiceById(serviceId: serviceId);
      if (response.success) {
        final List<SubServiceResponseModel> subServiceList = response.data is List
            ? (response.data as List<dynamic>).map((x) => SubServiceResponseModel.fromJson(x as Map<String, dynamic>)).toList()
            : [];
        if (subServiceList.isEmpty) {
          emit(SubServiceErrorState(AppStrings.noSubServicesFound));
        } else {
          emit(SubServiceSuccessState(subServiceList));
        }
      } else {
        emit(SubServiceErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(SubServiceErrorState(AppStrings.genericErrorMsg));
    }
  }
}
