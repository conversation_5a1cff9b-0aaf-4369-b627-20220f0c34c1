part of 'coupon_offers_cubit.dart';

sealed class CouponOffersState extends Equatable {
  const CouponOffersState();

  @override
  List<Object> get props => [];
}

final class CouponOffersInitState extends CouponOffersState {
  @override
  List<Object> get props => [];
}

final class CouponOffersLoadingState extends CouponOffersState {
  @override
  List<Object> get props => [];
}

final class CouponOffersSuccessState extends CouponOffersState {
  final List<CouponOffersModel> couponOffersList;

  const CouponOffersSuccessState(this.couponOffersList);

  @override
  List<Object> get props => [couponOffersList];
}

final class CouponOffersErrorState extends CouponOffersState {
  final String errorMsg;

  const CouponOffersErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
