part of 'sub_service_cubit.dart';

sealed class SubServiceState extends Equatable {
  const SubServiceState();

  @override
  List<Object> get props => [];
}

final class SubServiceInitState extends SubServiceState {
  @override
  List<Object> get props => [];
}

final class SubServiceLoadingState extends SubServiceState {
  @override
  List<Object> get props => [];
}

final class SubServiceSuccessState extends SubServiceState {
  final List<SubServiceResponseModel> subServiceList;

  const SubServiceSuccessState(this.subServiceList);

  @override
  List<Object> get props => [subServiceList];
}

final class SubServiceErrorState extends SubServiceState {
  final String errorMsg;

  const SubServiceErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
