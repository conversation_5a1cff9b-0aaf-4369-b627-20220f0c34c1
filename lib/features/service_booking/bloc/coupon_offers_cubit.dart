import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/coupon_offers_model.dart';

part 'coupon_offers_state.dart';

class CouponOffersCubit extends Cubit<CouponOffersState> {
  CouponOffersCubit() : super(CouponOffersInitState());

  void getCouponOffersByItemTotal({required String itemTotal}) async {
    emit(CouponOffersLoadingState());

    try {
      final response = await ApiService.instance.getCouponOffersByItemTotal(itemTotal: itemTotal);
      if (response.success) {
        final List<CouponOffersModel> couponOffersList = response.data is List
            ? (response.data as List<dynamic>).map((x) => CouponOffersModel.fromJson(x as Map<String, dynamic>)).toList()
            : [];
        emit(CouponOffersSuccessState(couponOffersList));
      } else {
        emit(CouponOffersErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(CouponOffersErrorState(AppStrings.genericErrorMsg));
    }
  }
}
