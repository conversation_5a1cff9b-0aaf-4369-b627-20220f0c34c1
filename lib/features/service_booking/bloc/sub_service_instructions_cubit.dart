import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/instructions_response_model.dart';

part 'sub_service_instructions_state.dart';

class SubServiceInstructionsCubit extends Cubit<SubServiceInstructionsState> {
  SubServiceInstructionsCubit() : super(SubServiceInstructionsInitState());

  void getInstructionsByServiceId({required int serviceId}) async {
    emit(SubServiceInstructionsLoadingState());

    try {
      final response = await ApiService.instance.getInstructionsByServiceId(serviceId: serviceId);
      if (response.success) {
        final List<InstructionsResponseModel> instructionsList = response.data is List
            ? (response.data as List<dynamic>).map((x) => InstructionsResponseModel.fromJson(x as Map<String, dynamic>)).toList()
            : [];
        emit(SubServiceInstructionsSuccessState(instructionsList));
      } else {
        emit(SubServiceInstructionsErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(SubServiceInstructionsErrorState(AppStrings.genericErrorMsg));
    }
  }
}
