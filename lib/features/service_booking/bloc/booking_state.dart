part of 'booking_cubit.dart';

class BookingState extends Equatable {
  final BookingRequestModel bookingRequestModel;

  const BookingState({
    required this.bookingRequestModel,
  });

  BookingState copyWith({
    BookingRequestModel? bookingRequestModel,
  }) {
    return BookingState(
      bookingRequestModel: bookingRequestModel ?? this.bookingRequestModel,
    );
  }

  @override
  List<Object?> get props => [bookingRequestModel];
}
