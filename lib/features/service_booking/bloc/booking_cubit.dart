import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../data/model/booking_request_model.dart';

part 'booking_state.dart';

class BookingCubit extends Cubit<BookingState> {
  BookingCubit() : super(BookingState(bookingRequestModel: BookingRequestModel()));

  void updateBookingRequestModel(BookingRequestModel bookingRequestModel) {
    emit(state.copyWith(bookingRequestModel: bookingRequestModel));
  }

  void resetBookingRequestModel() {
    emit(BookingState(bookingRequestModel: BookingRequestModel()));
  }
}
