part of 'payment_service_cubit.dart';

sealed class PaymentServiceState extends Equatable {
  final BookingRequestModel? bookingRequestModel;
  final BookingDetailsResponseModel? bookingResponseModel;
  final PaymentIDResponseModel? paymentIDResponseModel;
  final PaymentVerificationResponseModel? paymentVerificationResponseModel;

  const PaymentServiceState({
    this.bookingResponseModel,
    this.paymentIDResponseModel,
    this.bookingRequestModel,
    this.paymentVerificationResponseModel,
  });

  @override
  List<Object?> get props => [
        bookingResponseModel,
        paymentIDResponseModel,
        bookingRequestModel,
        paymentVerificationResponseModel,
      ];
}

final class PaymentServiceInitState extends PaymentServiceState {
  const PaymentServiceInitState({
    super.bookingRequestModel,
    super.bookingResponseModel,
    super.paymentIDResponseModel,
    super.paymentVerificationResponseModel,
  });
}

final class PaymentServiceLoadingState extends PaymentServiceState {
  final String loadingMessage;
  final bool isShowingLoadingDialog;

  const PaymentServiceLoadingState({
    required super.bookingRequestModel,
    required super.bookingResponseModel,
    required super.paymentIDResponseModel,
    required super.paymentVerificationResponseModel,
    required this.loadingMessage,
    required this.isShowingLoadingDialog,
  });

  @override
  List<Object?> get props => [
        bookingRequestModel,
        bookingResponseModel,
        paymentIDResponseModel,
        paymentVerificationResponseModel,
        loadingMessage,
        isShowingLoadingDialog,
      ];
}

// final class PaymentServiceVerificationLoadingState extends PaymentServiceState {
//   final String loadingMessage;

//   const PaymentServiceVerificationLoadingState({
//     required super.bookingRequestModel,
//     required super.bookingResponseModel,
//     required super.paymentIDResponseModel,
//     required super.paymentVerificationResponseModel,
//     required this.loadingMessage,
//   });

//   @override
//   List<Object?> get props => [
//         bookingRequestModel,
//         bookingResponseModel,
//         paymentIDResponseModel,
//         paymentVerificationResponseModel,
//         loadingMessage,
//       ];
// }

final class PaymentServiceSuccessState extends PaymentServiceState {
  final bool isPaymentScreen;

  const PaymentServiceSuccessState({
    required super.bookingRequestModel,
    required super.bookingResponseModel,
    required super.paymentIDResponseModel,
    required super.paymentVerificationResponseModel,
    this.isPaymentScreen = true,
  });

  @override
  List<Object?> get props => [
        bookingRequestModel,
        bookingResponseModel,
        paymentIDResponseModel,
        paymentVerificationResponseModel,
        isPaymentScreen,
      ];
}

final class PaymentServiceCancelledState extends PaymentServiceState {
  const PaymentServiceCancelledState({
    required super.bookingRequestModel,
    required super.bookingResponseModel,
    required super.paymentIDResponseModel,
    required super.paymentVerificationResponseModel,
  });
}


final class PaymentServiceErrorState extends PaymentServiceState {
  final String errorMsg;
  final String? errorCode;

  const PaymentServiceErrorState({
    required super.bookingRequestModel,
    required super.bookingResponseModel,
    required super.paymentIDResponseModel,
    required super.paymentVerificationResponseModel,
    required this.errorMsg,
    this.errorCode,
  });

  @override
  List<Object?> get props => [
        bookingRequestModel,
        bookingResponseModel,
        paymentIDResponseModel,
        paymentVerificationResponseModel,
        errorMsg,
      ];
}
