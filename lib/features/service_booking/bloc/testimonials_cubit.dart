import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/testimonials_model.dart';

part 'testimonials_state.dart';

class TestimonialsCubit extends Cubit<TestimonialsState> {
  TestimonialsCubit() : super(TestimonialsInitState());

  void getUserTestimonials() async {
    emit(TestimonialsLoadingState());

    try {
      final response = await ApiService.instance.getUserTestimonials();
      if (response.success) {
        final List<TestimonialsModel> testimonialsList = (response.data as List<dynamic>)
                .map((item) => TestimonialsModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(TestimonialsSuccessState(testimonialsList));
      } else {
        emit(TestimonialsErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(TestimonialsErrorState(AppStrings.genericErrorMsg));
    }
  }
}
