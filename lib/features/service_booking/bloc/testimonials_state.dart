part of 'testimonials_cubit.dart';

sealed class TestimonialsState extends Equatable {
  const TestimonialsState();

  @override
  List<Object> get props => [];
}

final class TestimonialsInitState extends TestimonialsState {
  @override
  List<Object> get props => [];
}

final class TestimonialsLoadingState extends TestimonialsState {
  @override
  List<Object> get props => [];
}

final class TestimonialsSuccessState extends TestimonialsState {
  final List<TestimonialsModel> testimonialsList;

  const TestimonialsSuccessState(this.testimonialsList);

  @override
  List<Object> get props => [testimonialsList];
}

final class TestimonialsErrorState extends TestimonialsState {
  final String errorMsg;

  const TestimonialsErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
