import 'package:hive_flutter/hive_flutter.dart';

part 'auth_response_model.g.dart';

class AuthResponseModel {
  Token? token;
  User? user;

  AuthResponseModel({this.token, this.user});

  AuthResponseModel.fromJson(Map<String, dynamic> json) {
    token = json['token'] != null ? Token.fromJson(json['token']) : null;
    user = json['user'] != null ? User.fromJson(json['user']) : null;
  }
}

class Token {
  String? refresh;
  String? access;

  Token({this.refresh, this.access});

  Token.fromJson(Map<String, dynamic> json) {
    refresh = json['refresh'];
    access = json['access'];
  }
}

@HiveType(typeId: 1)
class User {
  @HiveField(0)
  String? id;

  @HiveField(1)
  String? mobileNumber;

  @HiveField(2)
  String? countryName;

  @HiveField(3)
  String? countryAbbr;

  @HiveField(4)
  String? countryCode;

  @HiveField(5)
  bool? isNewUser;

  @HiveField(7)
  String? name;

  User({
    this.id,
    this.mobileNumber,
    this.countryName,
    this.countryAbbr,
    this.countryCode,
    this.isNewUser,
    this.name,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      mobileNumber: json['mobile_number'],
      countryName: json['country_name'],
      countryAbbr: json['country_abbr'],
      countryCode: json['country_code'],
      isNewUser: json['is_new_user'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mobile_number': mobileNumber,
      'country_name': countryName,
      'country_abbr': countryAbbr,
      'country_code': countryCode,
      'is_new_user': isNewUser,
      'name': name,
    };
  }

  User copyWith({
    String? id,
    String? mobileNumber,
    String? countryName,
    String? countryAbbr,
    String? countryCode,
    bool? isNewUser,
    String? name,
  }) {
    return User(
      id: id ?? this.id,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      countryName: countryName ?? this.countryName,
      countryAbbr: countryAbbr ?? this.countryAbbr,
      countryCode: countryCode ?? this.countryCode,
      isNewUser: isNewUser ?? this.isNewUser,
      name: name ?? this.name,
    );
  }
}
