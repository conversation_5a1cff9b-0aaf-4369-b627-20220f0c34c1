import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import '../../../core/api/api_service.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../authentication/data/authentication_req_model.dart';
import '../../profile/data/model/user_data_model.dart';
import '../data/auth_response_model.dart';
import 'verify_otp_state.dart';

class VerifyOtpBloc extends Cubit<VerifyOtpState> {
  @override
  VerifyOtpBloc() : super(VerifyOtpInitState());

  void verifyOtp({
    required AuthenticationReqModel authenticationReqModel,
  }) async {
    emit(VerifyOtpLoadingState());
    try {
      final response = await ApiService.instance.verifyOtp(authenticationReqModel: authenticationReqModel);
      if (response.success) {
        AuthResponseModel authResponseModel = AuthResponseModel.fromJson(response.data);
        await ApiService.instance.setPartnerGatewayToken(authResponseModel.token?.access ?? "", authResponseModel.token?.refresh ?? "");
        await HiveStorageHelper.saveData<String>(HiveBoxName.user, HiveKeys.userToken, authResponseModel.token?.access ?? "");

        final res = await ApiService.instance.getProfile();
        if (res.success) {
          // We are saving user instead of userDataModel because of existing users
          UserDataModel userDataModel = UserDataModel.fromJson(res.data);

          if (authResponseModel.user != null) {
            final user = authResponseModel.user!.copyWith(name: userDataModel.fullname);
            await HiveStorageHelper.saveData<User>(HiveBoxName.user, HiveKeys.userData, user);
          }
        }

        final uuid = Uuid();
        await HiveStorageHelper.saveData<String>(HiveBoxName.user, HiveKeys.sessionId, uuid.v4());

        if (authResponseModel.user != null && authResponseModel.user!.id != null) {
          await FirebaseCrashlytics.instance.setUserIdentifier(authResponseModel.user!.id!);
        }

        emit(VerifyOtpSuccessState(successMsg: response.message ?? ""));
      } else {
        emit(VerifyOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(VerifyOtpErrorState(AppStrings.genericErrorMsg));
    }
  }
}
