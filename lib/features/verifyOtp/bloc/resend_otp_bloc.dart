import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import 'resend_otp_state.dart';

class ResendOtpBloc extends Cubit<ResendOtpState> {
  ResendOtpBloc() : super(ResendOtpInitState());

  Future<void> resendOtp({
    String? mobileNumber,
  }) async {
    emit(ResendOtpLoadingState());
    final response = await ApiService.instance.resendOtp(mobileNumber: mobileNumber,);
    try {
      if (response.success) {
        emit(ResendOtpSuccessState(message: response.message));
      } else {
        emit(ResendOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ResendOtpErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
