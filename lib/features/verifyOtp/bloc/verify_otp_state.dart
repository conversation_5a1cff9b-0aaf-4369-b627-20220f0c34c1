import 'package:equatable/equatable.dart';

abstract class VerifyOtpState extends Equatable {}

class VerifyOtpInitState extends VerifyOtpState {
  @override
  List<Object> get props => [];
}

class VerifyOtpLoadingState extends VerifyOtpState {
  @override
  List<Object> get props => [];
}

class VerifyOtpSuccessState extends VerifyOtpState {
  final String? successMsg;

  VerifyOtpSuccessState({this.successMsg});
  @override
  List<Object> get props => [];
}

class VerifyOtpErrorState extends VerifyOtpState {
  final String errorMsg;
  VerifyOtpErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
