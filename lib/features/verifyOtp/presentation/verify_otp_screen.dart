import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
// import 'package:sms_autofill/sms_autofill.dart';

import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/app_toast.dart';
import '../../../widgets/buttons/primary_button.dart';
import '../../../widgets/dialogs.dart';
import '../../../widgets/legup_logo.dart';
import '../../../widgets/texts/app_text.dart';
import '../../authentication/data/authentication_req_model.dart';
import '../bloc/resend_otp_bloc.dart';
import '../bloc/resend_otp_state.dart';
import '../bloc/verify_otp_bloc.dart';
import '../bloc/verify_otp_state.dart';

class VerifyOtpScreen extends StatefulWidget {
  final AuthenticationReqModel authenticationReqModel;
  const VerifyOtpScreen({super.key, required this.authenticationReqModel});

  @override
  State<VerifyOtpScreen> createState() => _VerifyOtpScreenState();
}

class _VerifyOtpScreenState extends State<VerifyOtpScreen> {

  // @override
  // void codeUpdated() {
  //   if (code != null) {
  //     setState(() {
  //       _otpController.text = code!;
  //     });
  //     verifyOtp();
  //   }
  // }

  late TextEditingController _otpController;
  Timer? _resendTimer;
  int _timeLeft = 30; // Start with 30 seconds countdown
  bool _canResend = false; // Initially disabled

  @override
  void initState() {
    super.initState();
    // listenForCode();

    _otpController = TextEditingController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      startResendTimer(); // Start the countdown when screen loads
    });
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    // cancel();
    super.dispose();
  }

  void startResendTimer() {
    setState(() {
      _timeLeft = 30;
      _canResend = false; // Disable button for first 30 seconds
    });

    if (_resendTimer?.isActive ?? false) {
      _resendTimer?.cancel();
    }

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_timeLeft > 0) {
          _timeLeft--;
        } else {
          _canResend = true; // Enable the button after 30 seconds
          timer.cancel();
        }
      });
    });
  }

  String get resendText {
    if (_canResend) {
      return AppStrings.resendOTP;
    }
    return AppStrings.resendIn(_timeLeft); // Countdown display
  }

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    TextTheme textTheme = themeData.textTheme;
    ColorScheme colorScheme = themeData.colorScheme;
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 24.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LegUpLogo(),
              40.ph,
              Row(
                children: [
                  InkWell(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(AppImages.leftArrowIc)),
                  8.pw,
                  const Text20ExtraBold(AppStrings.otpVerification, fontSize: 24),
                ],
              ),
              8.ph,
              Text12Regular(
                AppStrings.plsEnter6DigitCode,
                fontSize: 14.sp,
                color: colorScheme.subTextGrey6A6A6A,
              ),
              4.ph,
              Row(
                children: [
                  Text14Medium(
                    "${widget.authenticationReqModel.countryCode} ${widget.authenticationReqModel.mobileNumber}",
                    fontSize: 14.sp,
                  ),
                  4.pw,
                  AppGestureDetector(
                    onTap: () {
                      Navigator.pop(context, true);
                    },
                    child: SvgPicture.asset(
                      AppImages.editIcon,
                      colorFilter: ColorFilter.mode(
                        colorScheme.blue140042,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ],
              ),
              32.ph,
              PinCodeTextField(
                errorTextMargin: EdgeInsets.zero,
                appContext: context,
                length: 6,
                controller: _otpController,
                showCursor: true,
                cursorColor: Theme.of(context).colorScheme.primary,
                keyboardType: TextInputType.number,
                errorTextSpace: 20,
                hintCharacter: '-',
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                ],
                textStyle: Theme.of(context).textTheme.displayMedium,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  fieldHeight: 44.h,
                  fieldWidth: 48.h,
                  borderWidth: 1,
                  activeBorderWidth: 1,
                  selectedBorderWidth: 1,
                  inactiveBorderWidth: 1,
                  disabledBorderWidth: 1,
                  errorBorderWidth: 1,
                  borderRadius: BorderRadius.circular(12.r),
                  selectedColor: Theme.of(context).colorScheme.primary,
                  disabledColor: Theme.of(context).colorScheme.lightGreyDDDDDD,
                  inactiveColor: Theme.of(context).colorScheme.lightGreyDDDDDD,
                  errorBorderColor: Theme.of(context).colorScheme.error,
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: BlocListener<ResendOtpBloc, ResendOtpState>(
                  listener: (context, state) {
                    if (state is ResendOtpLoadingState) {
                      Dialogs.showLoadingDialog(context, loadingText: AppStrings.gettingOTP);
                    }
                    if (state is ResendOtpSuccessState) {
                      Dialogs.hideDialog(context);
                      // Restart the timer
                      startResendTimer();
                      // Show Success Message
                      CustomToast.showToast(
                          message: state.message ?? "", isSuccess: true);
                    }
                    if (state is ResendOtpErrorState) {
                      Dialogs.hideDialog(context);
                      // Show Error Message
                      CustomToast.showToast(
                        message: state.errorMsg,
                      );
                    }
                  },
                  child: RichText(
                    text: TextSpan(
                      text: AppStrings.didntReceiveCode,
                      style: textTheme.bodySmall!.copyWith(
                        fontSize: 14.sp,
                        color: _canResend ? colorScheme.subTextGrey6A6A6A : colorScheme.blue150045,
                      ),
                      children: [
                        TextSpan(
                          text: resendText,
                          style: textTheme.displaySmall!.copyWith(
                            fontSize: 14.sp,
                            color: _canResend ? colorScheme.primary : colorScheme.blue150045,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = _canResend
                                ? () {
                                    _otpController.clear();
                                    context.read<ResendOtpBloc>().resendOtp(
                                          mobileNumber: "${widget.authenticationReqModel.countryCode}${widget.authenticationReqModel.mobileNumber}"
                                        );
                                  }
                                : null,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              20.ph,
              BlocListener<VerifyOtpBloc, VerifyOtpState>(
                  listener: (context, state) {
                    if (state is VerifyOtpLoadingState) {
                      Dialogs.showLoadingDialog(context, loadingText: AppStrings.verifyingOTP);
                    }
                    if (state is VerifyOtpSuccessState) {
                      Dialogs.hideDialog(context);
                      // Show Success Message
                      CustomToast.showToast(
                          message: state.successMsg ?? "", isSuccess: true);
                      // Navigate to Address Screen
                      Navigator.pushNamedAndRemoveUntil(context, AppRoute.appBottomNavBar, (route) => false);
                    }
                    if (state is VerifyOtpErrorState) {
                      Dialogs.hideDialog(context);
                      // Show Error Message
                      CustomToast.showToast(
                        message: state.errorMsg,
                      );
                    }
                  },
                  child: ValueListenableBuilder<TextEditingValue>(
                    valueListenable: _otpController,
                    builder: (context, value, child) {
                      return PrimaryButton(
                        onPressed: value.text.trim().length >= 6
                            ? () {
                                verifyOtp();
                              }
                            : null,
                        buttonText: AppStrings.verifyOTP,
                      );
                    },
                  )),
            ],
          ),
        ),
      ),
    );
  }

  void verifyOtp() {
    FocusManager.instance.primaryFocus?.unfocus();
    widget.authenticationReqModel.otpCode = _otpController.text.trim();
    context.read<VerifyOtpBloc>().verifyOtp(authenticationReqModel: widget.authenticationReqModel);
  }
}
