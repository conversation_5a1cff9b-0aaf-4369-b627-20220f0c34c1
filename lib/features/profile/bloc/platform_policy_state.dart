part of 'platform_policy_cubit.dart';

sealed class PlatformPolicyState extends Equatable {
  const PlatformPolicyState();

  @override
  List<Object> get props => [];
}

final class PlatformPolicyInitState extends PlatformPolicyState {
  @override
  List<Object> get props => [];
}

final class PlatformPolicyLoadingState extends PlatformPolicyState {
  @override
  List<Object> get props => [];
}

final class PlatformPolicySuccessState extends PlatformPolicyState {
  final List<PlatformPolicyModel> platformPolicyList;

  const PlatformPolicySuccessState(this.platformPolicyList);

  @override
  List<Object> get props => [platformPolicyList];
}

final class PlatformPolicyErrorState extends PlatformPolicyState {
  final String errorMsg;

  const PlatformPolicyErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
