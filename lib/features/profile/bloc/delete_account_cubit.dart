import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';

part 'delete_account_state.dart';

class DeleteAccountCubit extends Cubit<DeleteAccountState> {
  DeleteAccountCubit() : super(DeleteAccountInitState());

  Future<void> deleteAccount() async {
    emit(DeleteAccountLoadingState());

    try {
      final response = await ApiService.instance.deleteAccount();

      if (response.success) {
        emit(DeleteAccountSuccessState());
      } else {
        emit(DeleteAccountErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(DeleteAccountErrorState(AppStrings.genericErrorMsg));
    }
  }
}
