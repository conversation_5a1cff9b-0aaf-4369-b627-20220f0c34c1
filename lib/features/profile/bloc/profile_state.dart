part of 'profile_cubit.dart';

sealed class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object> get props => [];
}

final class ProfileInitState extends ProfileState {
  @override
  List<Object> get props => [];
}

final class ProfileLoadingState extends ProfileState {
  @override
  List<Object> get props => [];
}

final class ProfileSuccessState extends ProfileState {
  final UserDataModel userDataModel;
  final bool showToast;

  const ProfileSuccessState({
    required this.userDataModel,
    this.showToast = false,
  });

  @override
  List<Object> get props => [userDataModel];
}

final class ProfileErrorState extends ProfileState {
  final String errorMsg;

  const ProfileErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
