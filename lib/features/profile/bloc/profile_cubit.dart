import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import '../../../core/api/api_service.dart';
import '../../../core/services/hive/hive_keys.dart';
import '../../../core/services/hive/hive_storage_helper.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../verifyOtp/data/auth_response_model.dart';
import '../data/model/user_data_model.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(ProfileInitState());

  Future<void> getProfile() async {
    emit(ProfileLoadingState());

    try {
      final response = await ApiService.instance.getProfile();

      if (response.success) {
        UserDataModel userDataModel = UserDataModel.fromJson(response.data);

        final userData = HiveStorageHelper.getData<User>(HiveBoxName.user, HiveKeys.userData);

        if (userData == null) {
          final user = User(
            id: userDataModel.id?.toString(),
            mobileNumber: userDataModel.mobileNumber,
            countryName: userDataModel.countryName,
            countryAbbr: userDataModel.countryCode,
            countryCode: userDataModel.countryCode,
            name: userDataModel.fullname,
          );
          await HiveStorageHelper.saveData<User>(HiveBoxName.user, HiveKeys.userData, user);

          final uuid = Uuid();
          await HiveStorageHelper.saveData<String>(HiveBoxName.user, HiveKeys.sessionId, uuid.v4());

        }

        emit(ProfileSuccessState(userDataModel: userDataModel));
      } else {
        emit(ProfileErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ProfileErrorState(AppStrings.genericErrorMsg));
    }
  }

  Future<void> updateProfileDetails({required UserDataModel user}) async {
    emit(ProfileLoadingState());

    try {
      final res = await ApiService.instance.updateProfileDetails(userDataModel: user);

      if (res.success) {
        final response = await ApiService.instance.getProfile();

        if (response.success) {
          UserDataModel userDataModel = UserDataModel.fromJson(response.data);
          emit(ProfileSuccessState(userDataModel: userDataModel, showToast: true));
          final user = HiveStorageHelper.getData<User>(HiveBoxName.user, HiveKeys.userData);
          if (user != null) {
            final userData = user.copyWith(name: userDataModel.fullname);
            await HiveStorageHelper.saveData<User>(HiveBoxName.user, HiveKeys.userData, userData);
          }
        } else {
          emit(ProfileErrorState(response.message ?? AppStrings.genericErrorMsg));
        }
      } else {
        emit(ProfileErrorState(res.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(ProfileErrorState(AppStrings.genericErrorMsg));
    }
  }
}
