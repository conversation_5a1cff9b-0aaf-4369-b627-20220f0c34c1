part of 'delete_account_cubit.dart';

sealed class DeleteAccountState extends Equatable {
  const DeleteAccountState();

  @override
  List<Object> get props => [];
}

final class DeleteAccountInitState extends DeleteAccountState {
  @override
  List<Object> get props => [];
}

final class DeleteAccountLoadingState extends DeleteAccountState {
  @override
  List<Object> get props => [];
}

final class DeleteAccountSuccessState extends DeleteAccountState {
  const DeleteAccountSuccessState();

  @override
  List<Object> get props => [];
}

final class DeleteAccountErrorState extends DeleteAccountState {
  final String errorMsg;

  const DeleteAccountErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
