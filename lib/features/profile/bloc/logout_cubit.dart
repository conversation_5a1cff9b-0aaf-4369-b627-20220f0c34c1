import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';

part 'logout_state.dart';

class LogoutCubit extends Cubit<LogoutState> {
  LogoutCubit() : super(LogoutInitState());

  Future<void> getLogout() async {
    emit(LogoutLoadingState());

    try {
      final response = await ApiService.instance.logout();

      if (response.success) {
        emit(LogoutSuccessState());
      } else {
        emit(LogoutErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(LogoutErrorState(AppStrings.genericErrorMsg));
    }
  }
}
