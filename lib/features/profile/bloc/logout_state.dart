part of 'logout_cubit.dart';

sealed class LogoutState extends Equatable {
  const LogoutState();

  @override
  List<Object> get props => [];
}

final class LogoutInitState extends LogoutState {
  @override
  List<Object> get props => [];
}

final class LogoutLoadingState extends LogoutState {
  @override
  List<Object> get props => [];
}

final class LogoutSuccessState extends LogoutState {
  const LogoutSuccessState();

  @override
  List<Object> get props => [];
}

final class LogoutErrorState extends LogoutState {
  final String errorMsg;

  const LogoutErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
