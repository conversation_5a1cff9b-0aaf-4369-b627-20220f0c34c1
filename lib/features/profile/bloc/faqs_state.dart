part of 'faqs_cubit.dart';

sealed class FAQsState extends Equatable {
  const FAQsState();

  @override
  List<Object> get props => [];
}

final class FAQsInitState extends FAQsState {
  @override
  List<Object> get props => [];
}

final class FAQsLoadingState extends FAQsState {
  @override
  List<Object> get props => [];
}

final class FAQsSuccessState extends FAQsState {
  final List<FAQsResponseModel> faqsList;

  const FAQsSuccessState(this.faqsList);

  @override
  List<Object> get props => [faqsList];
}

final class FAQsErrorState extends FAQsState {
  final String errorMsg;

  const FAQsErrorState(this.errorMsg);

  @override
  List<Object> get props => [errorMsg];
}
