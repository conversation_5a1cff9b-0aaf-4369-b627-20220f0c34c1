import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/model/faqs_response_model.dart';

part 'faqs_state.dart';

class FAQsCubit extends Cubit<FAQsState> {
  FAQsCubit() : super(FAQsInitState());

  Future<void> getFAQs() async {
    emit(FAQsLoadingState());
    final response = await ApiService.instance.getFAQs();

    try {
      if (response.success) {
        final List<FAQsResponseModel> faqsList = (response.data as List<dynamic>)
                .map((item) => FAQsResponseModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(FAQsSuccessState(faqsList));
      } else {
        emit(FAQsErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(FAQsErrorState(response.message ?? AppStrings.genericErrorMsg));
    }
  }
}
