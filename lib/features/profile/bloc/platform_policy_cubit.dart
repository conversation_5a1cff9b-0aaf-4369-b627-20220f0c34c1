import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/enums/policy_type.dart';
import '../data/model/platform_policy_model.dart';

part 'platform_policy_state.dart';

class PlatformPolicyCubit extends Cubit<PlatformPolicyState> {
  PlatformPolicyCubit() : super(PlatformPolicyInitState());

  Future<void> getPlatformPolicy({required PolicyType policyType}) async {
    emit(PlatformPolicyLoadingState());

    try {
      final response = await ApiService.instance.getPlatformPolicy(policyType: policyType);
      if (response.success) {
        final List<PlatformPolicyModel> platformPolicyList = (response.data as List<dynamic>)
                .map((item) => PlatformPolicyModel.fromJson(item as Map<String, dynamic>))
                .toList();
        emit(PlatformPolicySuccessState(platformPolicyList));
      } else {
        emit(PlatformPolicyErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(PlatformPolicyErrorState(AppStrings.genericErrorMsg));
    }
  }
}
