import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/custom_cache_image.dart';
import '../../../../widgets/error_state_widget.dart';
import '../../bloc/profile_cubit.dart';
import '../../data/enums/policy_type.dart';
import '../profile_user_details_widget.dart';
import '../widgets/profile_card_shimmer.dart';
import '../widgets/profile_tile_widget.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    context.read<ProfileCubit>().getProfile();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: CustomAppbar(
        title: AppStrings.profile,
        showBackButton: false,
      ),
      body: SafeArea(
        child: BlocBuilder<ProfileCubit, ProfileState>(
          builder: (context, state) {
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          colorScheme.white,
                          colorScheme.lightBrownFBF4EF,
                        ],
                        stops: [0.0, 1.0],
                      ),
                      border: Border(
                        bottom: BorderSide(
                          color: colorScheme.orangeF0AB75,
                        ),
                      ),
                    ),
                    child: BlocConsumer<ProfileCubit, ProfileState>(
                      listener: (context, state) {
                        if (state is ProfileSuccessState && state.showToast) {
                          CustomToast.showToast(message: AppStrings.nameUpdatedSuccessfully, isSuccess: true);
                        }
                      },
                      builder: (context, state) {
                        if (state is ProfileLoadingState) {
                          return const ProfileCardShimmer();
                        }
                        if (state is ProfileErrorState) {
                          return ErrorStateWidget(
                            margin: EdgeInsets.zero,
                            errorMessage: state.errorMsg,
                            onRetry: () {
                              context.read<ProfileCubit>().getProfile();
                            },
                            removeDecoration: true,
                          );
                        }
                        if (state is ProfileSuccessState) {
                          final userData = state.userDataModel;
                          return Row(
                            children: [
                              ClipOval(
                                child: CustomCacheImage(
                                  imageUrl: userData.profilePicture ?? '',
                                  width: 60.r,
                                  height: 60.r,
                                  errorWidget: SvgPicture.asset(
                                    AppImages.caregiverUserIcon,
                                    width: 60.r,
                                    height: 60.r,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              12.pw,
                              ProfileUserDetailsWidget(userData: userData),
                            ],
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  Container(
                    color: colorScheme.surface,
                    height: 8,
                  ),
                  ProfileTileWidget(
                    title: AppStrings.savedAddresses,
                    description: AppStrings.savedAddressDescription,
                    buttonText: AppStrings.view,
                    onButtonPressed: () {
                      Navigator.pushNamed(context, AppRoute.profileSavedAddresses);
                    },
                  ),
                  ProfileTileWidget(
                    title: AppStrings.helpAndSupportAndFaq,
                    description: AppStrings.helpAndSupportAndFaqDescription,
                    buttonText: AppStrings.view,
                    onButtonPressed: () {
                      Navigator.pushNamed(context, AppRoute.helpAndSupport);
                    },
                  ),
                  ProfileTileWidget(
                    title: AppStrings.privacyP,
                    description: AppStrings.privacyPolicyDescription,
                    buttonText: AppStrings.view,
                    onButtonPressed: () {
                      Navigator.pushNamed(context, AppRoute.platformPolicy, arguments: PolicyType.privacyPolicy);
                    },
                  ),
                  ProfileTileWidget(
                    title: AppStrings.tAndC,
                    description: AppStrings.tAndCDescription,
                    buttonText: AppStrings.view,
                    onButtonPressed: () {
                      Navigator.pushNamed(context, AppRoute.platformPolicy, arguments: PolicyType.termsAndConditions);
                    },
                  ),
                  ProfileTileWidget(
                    title: AppStrings.refundsAndCancellation,
                    description: AppStrings.refundsAndCancellationDescription,
                    buttonText: AppStrings.view,
                    onButtonPressed: () {
                      Navigator.pushNamed(context, AppRoute.platformPolicy, arguments: PolicyType.refundPolicy);
                    },
                  ),
                  ProfileTileWidget(
                    title: AppStrings.yourLegUpAccount,
                    description: AppStrings.yourLegUpAccountDescription,
                    buttonText: AppStrings.view,
                    onButtonPressed: () {
                      Navigator.pushNamed(context, AppRoute.profileAccountDetails);
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
