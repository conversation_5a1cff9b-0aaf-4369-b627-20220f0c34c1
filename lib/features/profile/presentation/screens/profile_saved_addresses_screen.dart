import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/routes/app_routes.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/error_state_widget.dart';
import '../../../../widgets/google_map_location_picker/map_location_picker.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../../my_addresses/bloc/delete_address_bloc.dart';
import '../../../my_addresses/bloc/delete_address_state.dart';
import '../../../my_addresses/bloc/make_default_address_bloc.dart';
import '../../../my_addresses/bloc/make_default_address_state.dart';
import '../../../my_addresses/bloc/my_addresses_cubit.dart';
import '../../../my_addresses/bloc/my_addresses_state.dart';
import '../../../my_addresses/data/my_address_model.dart';
import '../../../my_addresses/presentation/pages/pick_address_screen.dart';
import '../../../my_addresses/presentation/widgets/address_list_shimmer.dart';

class ProfileSavedAddressesScreen extends StatefulWidget {
  const ProfileSavedAddressesScreen({super.key});

  @override
  State<ProfileSavedAddressesScreen> createState() => _ProfileSavedAddressesScreenState();
}

class _ProfileSavedAddressesScreenState extends State<ProfileSavedAddressesScreen> {
  bool isLocationDenied = false;

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
  }

  /// Checks location permission and updates state accordingly.
  Future<void> _checkLocationPermission() async {
    final permission = await Geolocator.checkPermission();
    final denied = permission == LocationPermission.denied || permission == LocationPermission.deniedForever;

    if (mounted && isLocationDenied != denied) {
      setState(() {
        isLocationDenied = denied;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: CustomAppbar(
        title: AppStrings.savedAddresses,
      ),
      body: SafeArea(
        child: MultiBlocListener(
          listeners: [
            BlocListener<MakeDefaultAddressBloc, MakeDefaultAddressState>(
              listener: (context, state) {
                if (state is MakeDefaultAddressSuccessState) {
                  Dialogs.hideDialog(context);
                  CustomToast.showToast(message: AppStrings.defaultAddressUpdatedSuccessfully, isSuccess: true);
                  context.read<MyAddressesBloc>().getAddresses();
                }
                if (state is MakeDefaultAddressErrorState) {
                  Dialogs.hideDialog(context);
                  CustomToast.showToast(message: state.errorMsg);
                }
                if (state is MakeDefaultAddressLoadingState) {
                  Dialogs.showLoadingDialog(
                    context,
                    loadingText: AppStrings.updatingDefaultAddress,
                  );
                }
              },
            ),
            BlocListener<DeleteAddressBloc, DeleteAddressState>(
              listener: (context, state) {
                if (state is DeleteAddressSuccessState) {
                  Dialogs.hideDialog(context);
                  context.read<MyAddressesBloc>().getAddresses();
                }
                if (state is DeleteAddressErrorState) {
                  Dialogs.hideDialog(context);
                  CustomToast.showToast(message: state.errorMsg);
                }
                if (state is DeleteAddressLoadingState) {
                  Dialogs.showLoadingDialog(
                    context,
                    loadingText: AppStrings.deletingAddress,
                  );
                }
              },
            ),
          ],
          child: BlocBuilder<MyAddressesBloc, MyAddressesState>(
            builder: (context, state) {
              if (state is MyAddressesLoadingState) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: AddressListShimmer(),
                );
              }
              if (state is MyAddressesErrorState) {
                return ErrorStateWidget(
                  errorMessage: state.errorMsg,
                  onRetry: () {
                    context.read<MyAddressesBloc>().getAddresses();
                  },
                );
              }
              if (state is MyAddressesSuccessState) {
                if (state.addresses?.isEmpty ?? true) {
                  return SizedBox(
                    height: 1.sh,
                    child: Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            AppImages.noAddressImage,
                            height: 150.h,
                            width: 150.w,
                          ),
                          4.ph,
                          Text20Bold(AppStrings.noSavedAddresses),
                          4.ph,
                          Text14Medium(
                            AppStrings.manageAllYourSavedAddressesHere,
                            color: colorScheme.lightGrey6B6B6B,
                          ),
                        ],
                      ),
                    ),
                  );
                }
                return SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.addresses!.length,
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, index) {
                      final address = state.addresses![index];
                      return SavedAddressTile(
                        cameFrom: AppRoute.profileSavedAddresses,
                        address: address,
                        onAddressSelected: (MyAddressModel selectedAddress) {
                          if (selectedAddress.isDefault ?? false) return;
                          context.read<MakeDefaultAddressBloc>().markDefaultAddress(addressId: address.id.toString());
                        },
                      );
                    },
                  ),
                );
              }
              return 1.ph;
            },
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        child: PrimaryButton(
          buttonText: AppStrings.addNewAddress,
          onPressed: () {
            Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MapLocationPicker(
                    apiKey: "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
                    latitude: isLocationDenied ? 26.9124 : null,
                    longitude: isLocationDenied ? 75.78 : null,
                    cameFrom: AppRoute.profileSavedAddresses,
                  ),
                ));
          },
        ),
      ),
    );
  }
}
