import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/dependency_injection/service_locator.dart';
import '../../../../core/navigation/navigation_service.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/delete_account_cubit.dart';
import '../../bloc/logout_cubit.dart';
import '../widgets/delete_account_dialog.dart';
import '../widgets/logout_user_dialog.dart';

class ProfileAccountDetailsScreen extends StatelessWidget {
  const ProfileAccountDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: CustomAppbar(
        title: AppStrings.legUpAccount,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<LogoutCubit, LogoutState>(
            listener: (context, state) {
              if (state is LogoutLoadingState) {
                Dialogs.showLoadingDialog(context, loadingText: AppStrings.loggingOut)  ;
              }
              if (state is LogoutSuccessState) {
                Dialogs.hideDialog(context);
                serviceLocator<NavigationService>().doLogout();
              }
              if (state is LogoutErrorState) {
                Dialogs.hideDialog(context);
                CustomToast.showToast(message: state.errorMsg);
              }
            },
          ),
          BlocListener<DeleteAccountCubit, DeleteAccountState>(
            listener: (context, state) {
              if (state is DeleteAccountLoadingState) {
                Dialogs.showLoadingDialog(context, loadingText: AppStrings.deletingAccount)  ;
              }
              if (state is DeleteAccountSuccessState) {
                Dialogs.hideDialog(context);
                serviceLocator<NavigationService>().doLogout();
              }
              if (state is DeleteAccountErrorState) {
                Dialogs.hideDialog(context);
                CustomToast.showToast(message: state.errorMsg);
              }
            },
          ),
        ],
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text14SemiBold(AppStrings.manageAccount, fontSize: 16.sp),
                    16.ph,
                    Text16Medium(AppStrings.logOutOfLegUpApp, fontSize: 16.sp),
                    16.ph,
                    CustomOutlinedButton(
                      width: double.infinity,
                      buttonWidget: Text14Bold(
                        AppStrings.logOut,
                        color: colorScheme.primary,
                      ),
                      onPressed: () {
                        showLogoutUserDialog(context, onPressed: () {
                          Navigator.pop(context);
                          context.read<LogoutCubit>().getLogout();
                        });
                      },
                    ),
                    16.ph,
                    Divider(
                      height: 1.h,
                      color: colorScheme.lightGreyDEDEDE,
                    ),
                    16.ph,
                    Text16Medium(AppStrings.deleteYourLegUpAccount, fontSize: 16.sp),
                    16.ph,
                    CustomOutlinedButton(
                      width: double.infinity,
                      borderColor: colorScheme.error,
                      buttonWidget: Text14Bold(
                        AppStrings.deleteAccount,
                        color: colorScheme.error,
                      ),
                      onPressed: () {
                        showDeleteAccountDialog(context, onPressed: () {
                          Navigator.pop(context);
                          context.read<DeleteAccountCubit>().deleteAccount();
                        });
                      },
                    ),
                  ],
                ),
              ),
              Container(
                color: colorScheme.surface,
                height: 8,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
