import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:homeservice_app/features/profile/bloc/faqs_cubit.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/texts/app_text.dart';
import '../widgets/faq_shimmer.dart';

class HelpAndSupportScreen extends StatelessWidget {
  const HelpAndSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BlocProvider(
      create: (_) => FAQsCubit()..getFAQs(),
      child: Scaffold(
        appBar: CustomAppbar(
          title: AppStrings.helpAndSupportAndFaq,
        ),
        body: SafeArea(
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  color: colorScheme.surface,
                  height: 8,
                ),
              ),
          
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      8.ph,
                      BlocBuilder<FAQsCubit, FAQsState>(
                        builder: (context, state) {
                          if (state is FAQsLoadingState) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text14Bold(
                                  AppStrings.faqTitle,
                                  fontSize: 16.sp,
                                  color: colorScheme.blue140042,
                                ),
                                12.ph,
                                FaqShimmer(),
                              ],
                            );
                          }
                          if (state is FAQsSuccessState) {
                            final faqsList = state.faqsList;
                            if (faqsList.isEmpty) {
                              return const SizedBox.shrink();
                            }
          
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text14Bold(
                                  AppStrings.faqTitle,
                                  fontSize: 16.sp,
                                  color: colorScheme.blue140042,
                                ),
                                12.ph,
                                ExpansionTileGroup(
                                  toggleType: ToggleType.expandOnlyCurrent,
                                  spaceBetweenItem: 12.h,
                                  children: List.generate(
                                    faqsList.length,
                                    (index) {
                                      final item = faqsList[index];
                                      return ExpansionTileItem(
                                        borderRadius: BorderRadius.circular(8.r),
                                        isHasLeftBorder: true,
                                        isHasRightBorder: true,
                                        isHasTopBorder: true,
                                        isHasBottomBorder: true,
                                        backgroundColor: colorScheme.lightGreyD0D8DE.withOpacity(0.3),
                                        collapsedBackgroundColor: colorScheme.lightGreyD0D8DE.withOpacity(0.3),
                                        title: Text14SemiBold(
                                          item.question ?? '',
                                          color: colorScheme.blu1A3D65,
                                        ),
                                        childrenPadding: EdgeInsets.symmetric(horizontal: 12.w).copyWith(bottom: 12.h),
                                        children: [
                                          Container(
                                            width: double.infinity,
                                            height: 1.5.h,
                                            color: colorScheme.primary,
                                          ),
                                          10.ph,
                                          Text12Regular(
                                            item.answer ?? '',
                                            fontSize: 14.sp,
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
          
                      16.ph,
                      Text14Bold(
                        AppStrings.needAssistance,
                        fontSize: 16.sp,
                        color: colorScheme.blue140042,
                      ),
                      8.ph,
                      Text14Medium(
                        AppStrings.ourServiceExpertIsJustOneCallOrEmailAway,
                        color: colorScheme.blue140042,
                      ),
                      8.ph,
                      Row(
                        children: [
                          CustomOutlinedButton(
                            width: 132.w,
                            onPressed: () {
                              launchUrl(Uri.parse('tel:${AppConfig.getInstance().metadata.contactNumber}'));
                            },
                            buttonWidget: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(AppImages.callIc),
                                4.pw,
                                Text14Bold(
                                  AppStrings.contactUs,
                                  color: colorScheme.primary,
                                ),
                              ],
                            ),
                          ),
                          12.pw,
                          CustomOutlinedButton(
                            width: 132.w,
                            onPressed: () {
                              launchUrl(Uri.parse('mailto:${AppConfig.getInstance().metadata.contactEmail}'));
                            },
                            buttonWidget: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.email_rounded,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                                4.pw,
                                Text14Bold(
                                  AppStrings.emailUs,
                                  color: colorScheme.primary,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      8.ph,
                    ],
                  ),
                ),
              ),
          
              SliverFillRemaining(
                hasScrollBody: false,
                fillOverscroll: false,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 20.w,
                      right: 20.w,
                      bottom: 20.h,
                      top: 10.h,
                    ),
                    child: Stack(
                      alignment: Alignment.bottomLeft,
                      children: [
                        SvgPicture.asset(AppImages.supportImg3),
                        Padding(
                          padding: EdgeInsets.only(bottom: 12.h),
                          child: Text(
                            AppStrings.madeWithLoveAndEmpathy,
                            textAlign: TextAlign.left,
                            style: TextStyle(
                              fontFamily: FontFamily.playwriteIE,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w400,
                              color: colorScheme.success3FB68E,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
