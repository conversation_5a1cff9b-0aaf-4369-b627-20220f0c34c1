import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/error_state_widget.dart';
import '../../bloc/platform_policy_cubit.dart';
import '../../data/enums/policy_type.dart';

class PlatformPolicyScreen extends StatefulWidget {
  final PolicyType policyType;

  const PlatformPolicyScreen({
    super.key,
    required this.policyType,
  });

  @override
  State<PlatformPolicyScreen> createState() => _PlatformPolicyScreenState();
}

class _PlatformPolicyScreenState extends State<PlatformPolicyScreen> {
  @override
  void initState() {
    super.initState();
    context.read<PlatformPolicyCubit>().getPlatformPolicy(policyType: widget.policyType);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: widget.policyType == PolicyType.privacyPolicy
            ? AppStrings.privacyP
            : widget.policyType == PolicyType.termsAndConditions
                ? AppStrings.tAndC
                : AppStrings.refundsAndCancellationPolicy,
        showBackButton: true,
      ),
      body: BlocBuilder<PlatformPolicyCubit, PlatformPolicyState>(
        builder: (context, state) {
          if (state is PlatformPolicyLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is PlatformPolicyErrorState) {
            return ErrorStateWidget(
              errorMessage: state.errorMsg,
              onRetry: () {
                context.read<PlatformPolicyCubit>().getPlatformPolicy(policyType: widget.policyType);
              },
            );
          }
          if (state is PlatformPolicySuccessState) {
            if (state.platformPolicyList.isEmpty) {
              return SizedBox.shrink();
            }

            return ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              itemCount: state.platformPolicyList.length,
              itemBuilder: (context, index) {
                final item = state.platformPolicyList[index];
                return Html(
                  data: item.content,
                  style: {
                    "b": Style(
                      fontWeight: FontWeight.w700,
                      fontFamily: FontFamily.nunitoSansBold,
                    ),
                    "strong": Style(
                      fontWeight: FontWeight.w600,
                      fontFamily: FontFamily.nunitoSansSemiBold,
                    ),
                    "p": Style(
                      fontWeight: FontWeight.w400,
                      fontFamily: FontFamily.nunitoSansRegular,
                    ),
                  },
                  onLinkTap: (String? url, Map<String, String> attributes, _) async {
                    if (url != null) {
                      await launchUrl(Uri.parse(url));
                    }
                  },
                );
              },
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
