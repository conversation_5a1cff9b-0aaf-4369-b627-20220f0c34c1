import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';

class ProfileCardShimmer extends StatelessWidget {
  const ProfileCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Shimmer.fromColors(
          baseColor: colorScheme.shimmerBaseColor,
          highlightColor: colorScheme.shimmerHighlightColor,
          child: ClipOval(
            child: Container(
              width: 60.r,
              height: 60.r,
              color: colorScheme.shimmerColor,
            ),
          ),
        ),
        12.pw,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Shimmer.fromColors(
              baseColor: colorScheme.shimmerBaseColor,
              highlightColor: colorScheme.shimmerHighlightColor,
              child: Container(
                width: 150.r,
                height: 20.r,
                decoration: BoxDecoration(
                  color: colorScheme.shimmerColor,
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
            8.ph,
            Shimmer.fromColors(
              baseColor: colorScheme.shimmerBaseColor,
              highlightColor: colorScheme.shimmerHighlightColor,
              child: Container(
                width: 100.r,
                height: 16.r,
                decoration: BoxDecoration(
                  color: colorScheme.shimmerColor,
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
