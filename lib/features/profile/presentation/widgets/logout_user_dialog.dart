import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/widgets/app_gesture_detector.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/buttons/custom_outlined_button.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/texts/app_text.dart';

void showLogoutUserDialog(
  BuildContext context, {
  required VoidCallback onPressed,
}) {
  final theme = Theme.of(context);
  final colorScheme = theme.colorScheme;
  showDialog(
    context: context,
    builder: (ctx) => Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
      backgroundColor: colorScheme.white,
      insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 24.r,
                  backgroundColor: colorScheme.redFFE0E0,
                  child: Center(
                    child: Text(
                      '!',
                      style: TextStyle(
                        fontSize: 26.sp,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.redD60000,
                      ),
                    ),
                  ),
                ),
                AppGestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(
                    Icons.close_rounded,
                    color: colorScheme.blue150045,
                  ),
                )
              ],
            ),
            20.ph,

            Flexible(
              child: Text20Bold(
                AppStrings.logout,
              ),
            ),
            12.ph,
            Flexible(
              child: Text14Medium(
                AppStrings.logoutDescription,
                color: colorScheme.blue140042,
              ),
            ),
            20.ph,
            Divider(color: colorScheme.lightGreyD5D5D5),
            20.ph,
            CustomOutlinedButton(
              width: 160.w,
              onPressed: onPressed,
              buttonText: AppStrings.yesLogout,
            ),
            12.ph,
            PrimaryButton(
              width: 160.w,
              onPressed: () => Navigator.pop(context),
              buttonText: AppStrings.cancel,
            ),
            18.ph,
          ],
        ),
      ),
    ),
  );
}
