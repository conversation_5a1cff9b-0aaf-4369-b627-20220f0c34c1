import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../utils/extensions/empty_space_extn.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/texts/app_text.dart';

class ProfileTileWidget extends StatelessWidget {
  final String title;
  final String? description;
  final String buttonText;
  final VoidCallback onButtonPressed;

  const ProfileTileWidget({
    super.key,
    required this.title,
    this.description,
    required this.buttonText,
    required this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text14SemiBold(
                    title,
                    fontSize: 16.sp,
                  ),
                  4.ph,
                  AppGestureDetector(
                    onTap: onButtonPressed,
                    child: Text16Medium(
                      buttonText,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
              if (description != null) ...[
                8.ph,
                Text14SemiBold(
                  description!,
                  fontSize: 16.sp,
                  color: colorScheme.lightGrey6B6B6B,
                ),
              ]
            ],
          ),
        ),
        Container(
          color: colorScheme.surface,
          height: 8,
        ),
      ],
    );
  }
}
