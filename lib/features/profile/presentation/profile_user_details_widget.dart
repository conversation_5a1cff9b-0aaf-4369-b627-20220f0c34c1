import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/theme/app_theme.dart';
import '../../../utils/extensions/empty_space_extn.dart';
import '../../../utils/string_constants/app_images.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../../../widgets/app_gesture_detector.dart';
import '../../../widgets/app_toast.dart';
import '../../../widgets/texts/app_text.dart';
import '../bloc/profile_cubit.dart';
import '../data/model/user_data_model.dart';

class ProfileUserDetailsWidget extends StatefulWidget {
  final UserDataModel userData;

  const ProfileUserDetailsWidget({
    super.key,
    required this.userData,
  });

  @override
  State<ProfileUserDetailsWidget> createState() => _ProfileUserDetailsWidgetState();
}

class _ProfileUserDetailsWidgetState extends State<ProfileUserDetailsWidget> {
  late TextEditingController nameController;
  late String initialName;

  bool isEditing = false;

  late FocusNode focusNode;

  @override
  void initState() {
    super.initState();
    focusNode = FocusNode();

    initialName = widget.userData.fullname?.trim().isNotEmpty == true
        ? widget.userData.fullname!
        : AppStrings.legUpUser;

    nameController = TextEditingController(text: initialName);
  }

  void resetChanges() {
    nameController.text = initialName;
  }

  void saveChanges() {
    final newName = nameController.text.trim();
    if (newName.isEmpty) {
      nameController.text = AppStrings.legUpUser;
    } else {
      nameController.text = newName;
    }

    final user = UserDataModel(fullname: nameController.text);
    context.read<ProfileCubit>().updateProfileDetails(user: user);
  }

  void toggleEditing({bool reset = false}) {
    final bool wasEditing = isEditing;

    if (wasEditing && reset) {
      resetChanges();
    }

    if (wasEditing) {
      if (nameController.text.isEmpty) {
        CustomToast.showToast(message: AppStrings.nameCannotBeEmpty);
        return;
      }
      focusNode.unfocus();
      if (!reset) {
        saveChanges();
      }
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && focusNode.canRequestFocus) {
          focusNode.requestFocus();
          nameController.selection = TextSelection.fromPosition(
            TextPosition(offset: nameController.text.length),
          );
        }
      });
    }
    setState(() {
      isEditing = !isEditing;
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Builder(
                builder: (context) {
                  if (isEditing) {
                    return AutoResizingTextField(
                      controller: nameController,
                      focusNode: focusNode,
                      onCheckPressed: () => toggleEditing(),
                      onCancelEditing: () => toggleEditing(reset: true),
                    );
                  } else {
                    return Flexible(
                      child: Text20Bold(
                        nameController.text,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }
                },
              ),
              if (!isEditing) ...[
                12.pw,
                AppGestureDetector(
                  onTap: () {
                    if (!isEditing) {
                      toggleEditing();
                    }
                  },
                  child: SvgPicture.asset(
                    AppImages.editIcon,
                    width: 20.h,
                    height: 20.h,
                    colorFilter: ColorFilter.mode(
                      colorScheme.blue150045,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ],
          ),
          Row(
            children: [
              Text14SemiBold(
                widget.userData.mobileNumber ?? '',
                fontSize: 16.sp,
              ),
              4.pw,
              SvgPicture.asset(
                AppImages.verifiedIcon,
                width: 24.r,
                height: 24.r,
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    nameController.dispose();
    focusNode.dispose();
    super.dispose();
  }
}


class AutoResizingTextField extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final VoidCallback onCheckPressed;
  final VoidCallback onCancelEditing;

  const AutoResizingTextField({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onCheckPressed,
    required this.onCancelEditing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    return ConstrainedBox(
      constraints: BoxConstraints(
        minWidth: 50,
        maxWidth: MediaQuery.of(context).size.width - 130,
      ),
      child: IntrinsicWidth(
        child: TextFormField(
          controller: controller,
          focusNode: focusNode,
          decoration: InputDecoration(
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            isDense: true,
            contentPadding: EdgeInsets.zero,
            counterText: '',
            suffixIcon: Padding(
              padding: EdgeInsets.only(left: 10.w),
              child: AppGestureDetector(
                onTap: onCheckPressed,
                child: SvgPicture.asset(
                  AppImages.checkGreenIcon,
                  width: 20.h,
                  height: 20.h,
                  colorFilter: ColorFilter.mode(
                    colorScheme.blue150045,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            suffixIconConstraints: BoxConstraints(
              maxHeight: 20.h,
            ),
          ),
          style: textTheme.bodyMedium!.copyWith(
            fontSize: 20.sp,
          ),
          onTapOutside: (_) {
            focusNode.unfocus();
            onCancelEditing();
          },
          onFieldSubmitted: (_) {
            onCheckPressed();
          },
          maxLines: 1,
          maxLength: 100,
          textInputAction: TextInputAction.done,
        ),
      ),
    );
  }
}
