class PlatformPolicyModel {
  int? id;
  String? content;
  String? section;

  PlatformPolicyModel({
    this.id,
    this.content,
    this.section,
  });

  factory PlatformPolicyModel.fromJson(Map<String, dynamic> json) {
    return PlatformPolicyModel(
      id: json['id'] as int?,
      content: json['content'] as String?,
      section: json['section'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'section': section,
    };
  }
}
