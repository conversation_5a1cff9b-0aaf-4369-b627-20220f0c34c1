import '../../../my_addresses/data/my_address_model.dart';

class UserDataModel {
  int? id;
  String? fullname;
  MyAddressModel? savedAddress;
  String? mobileNumber;
  String? profilePicture;
  String? countryName;
  String? countryCode;

  UserDataModel({
    this.id,
    this.fullname,
    this.savedAddress,
    this.mobileNumber,
    this.profilePicture,
    this.countryName,
    this.countryCode,
  });

  factory UserDataModel.fromJson(Map<String, dynamic> json) {
    return UserDataModel(
      id: json['id'] as int?,
      fullname: json['fullname'] as String?,
      savedAddress: json['saved_address'] != null ? MyAddressModel.fromJson(json['saved_address']) : null,
      mobileNumber: json['mobile_number'] as String?,
      profilePicture: json['profile_picture'] as String?,
      countryName: json['country_name'] as String?,
      countryCode: json['country_code'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (fullname != null && fullname!.isNotEmpty) {
      data['full_name'] = fullname;
    }
    if (mobileNumber != null && mobileNumber!.isNotEmpty) {
      data['mobile_number'] = mobileNumber;
    }
    if (profilePicture != null && profilePicture!.isNotEmpty) {
      data['profile_picture'] = profilePicture;
    }
    if (countryName != null && countryName!.isNotEmpty) {
      data['country_name'] = countryName;
    }
    if (countryCode != null && countryCode!.isNotEmpty) {
      data['country_code'] = countryCode;
    }
    // if (savedAddress != null) {
    //   data['saved_address'] = savedAddress!.toJson();
    // }
    // if (id != null) {
    //   data['id'] = id;
    // }
    return data;
  }
}
