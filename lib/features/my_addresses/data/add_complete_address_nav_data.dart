import '../../../widgets/google_map_location_picker/map_location_picker.dart';
import 'my_address_model.dart';

class AddCompleteAddressNavData {
  final GeocodingResult? geocodingResult;
  final double? latitude;
  final double? longitude;
  final bool isEdit;
  final int? addressId;
  final MyAddressModel? address;
  final String? cameFrom;

  AddCompleteAddressNavData({
    this.geocodingResult,
    this.latitude,
    this.longitude,
    this.isEdit = false,
    this.addressId,
    this.address,
    this.cameFrom,
  });
}
