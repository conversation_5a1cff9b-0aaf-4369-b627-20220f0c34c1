class AddressReqModel {
  String? title;
  String? address;
  String? houseNo;
  String? city;
  String? state;
  String? country;
  String? pincode;
  bool? isDefault;
  List<double>? location;

  AddressReqModel({
    this.title,
    this.address,
    this.houseNo,
    this.city,
    this.state,
    this.country,
    this.pincode,
    this.isDefault,
    this.location,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (title != null) data['title'] = title;
    if (address != null) data['address'] = address;
    if (houseNo != null) data['house_no'] = houseNo;
    if (city != null) data['city'] = city;
    if (state != null) data['state'] = state;
    if (country != null) data['country'] = country;
    if (pincode != null) data['pincode'] = pincode;
    if (isDefault != null) data['is_default'] = isDefault;
    if (location != null) data['location'] = location;
    return data;
  }

  factory AddressReqModel.fromJson(Map<String, dynamic> json) {
    return AddressReqModel(
      title: json['title'] as String?,
      address: json['address'] as String?,
      houseNo: json['house_no'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      pincode: json['pincode'] as String?,
      isDefault: json['is_default'] as bool?,
      location: (json['location'] as List<dynamic>?)?.map((e) => (e as num).toDouble()).toList(),
    );
  }
}
