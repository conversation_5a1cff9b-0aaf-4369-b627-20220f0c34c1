class MyAddressModel {
  int? id;
  String? title;
  String? address;
  String? houseNo;
  String? city;
  String? state;
  String? country;
  String? pincode;
  List<double>? location;
  bool? isDefault;

  MyAddressModel(
      {this.id,
      this.title,
      this.address,
      this.houseNo,
      this.city,
      this.state,
      this.country,
      this.pincode,
      this.location,
      this.isDefault});

  MyAddressModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    address = json['address'];
    houseNo = json['house_no'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    pincode = json['pincode'];
    location = json['location']?.cast<double>();
    isDefault = json['is_default'] ?? false;
  }

  MyAddressModel copyWith({
    int? id,
    String? title,
    String? address,
    String? houseNo,
    String? city,
    String? state,
    String? country,
    String? pincode,
    List<double>? location,
    bool? isDefault,
  }) {
    return MyAddressModel(
      id: id ?? this.id,
      title: title ?? this.title,
      address: address ?? this.address,
      houseNo: houseNo ?? this.houseNo,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      pincode: pincode ?? this.pincode,
      location: location ?? this.location,
      isDefault: isDefault ?? this.isDefault,
    );
  }
}
