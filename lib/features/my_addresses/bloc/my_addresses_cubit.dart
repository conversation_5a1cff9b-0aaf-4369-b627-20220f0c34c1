import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:homeservice_app/utils/location_methods.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/common_models/address_components_model.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/my_address_model.dart';
import 'my_addresses_state.dart';

class MyAddressesBloc extends Cubit<MyAddressesState> {
  @override
  MyAddressesBloc() : super(MyAddressesInitState());

  void getAddresses({bool isGettingFromHome = false}) async {
    log("Getting addresses");
    MyAddressModel? selectedAddress = state is MyAddressesSuccessState ? (state as MyAddressesSuccessState).selectedAddress : null;
    emit(MyAddressesLoadingState());
    try {
      final response = await ApiService.instance.getAddresses();
      if (response.success) {
        log("Got address API success");
        List<MyAddressModel> addresses = response.data is List
            ? List<MyAddressModel>.from(
                response.data.map((x) => MyAddressModel.fromJson(x)))
            : [];
        if (addresses.isNotEmpty) {
          log("Addresses are not empty. Got ${addresses.length} addresses");
          // TODO: pass lat, lng to get Service Categories API
          emit(MyAddressesSuccessState(
            addresses: addresses,
            selectedAddress: getSelectedAddress(addresses),
          ));
        } else {
          log("Addresses are empty");
          log("isGettingFromHome $isGettingFromHome");
          if (isGettingFromHome) {
            Geolocator.requestPermission().then((value) async {
              log("Got location permission $value");
              if (value == LocationPermission.always ||
                  value == LocationPermission.whileInUse) {
                bool isLocationServiceEnabled =
                    await Geolocator.isLocationServiceEnabled();
                if (!isLocationServiceEnabled) {
                  emit(MyAddressesSuccessState(
                    addresses: addresses,
                    selectedAddress: selectedAddress,
                  ));
                }
                final position = await Geolocator.getCurrentPosition();
                log("Got location $position");
                // TODO: pass lat, lng to get Service Categories API
                LocationMethods.decodeAddress(
                        lat: position.latitude, long: position.longitude)
                    .then((decodedAddressModel) {
                  log("Got decoded address success = ${decodedAddressModel.success}");
                  if (decodedAddressModel.success) {
                    AddressComponentsModel? addressComponents =
                        LocationMethods.getAddressComponents(
                            geocodingResult:
                                decodedAddressModel.geocodingResult);
                    if (addressComponents != null) {
                      log("Got address as ${addressComponents.addressComplete}");
                      emit(MyAddressesSuccessState(
                        addresses: addresses,
                        selectedAddress: MyAddressModel(
                          title: [
                            addressComponents.houseNo,
                            addressComponents.sublocality,
                            if ((addressComponents.houseNo == null || addressComponents.houseNo!.isEmpty) &&
                                (addressComponents.sublocality == null || addressComponents.sublocality!.isEmpty))
                              addressComponents.city,
                          ]
                              .where((element) =>
                                  element != null && element.isNotEmpty)
                              .join(", "),
                          houseNo: addressComponents.houseNo,
                          city: addressComponents.city,
                          state: addressComponents.state,
                          country: addressComponents.country,
                          pincode: addressComponents.pincode,
                          isDefault: true,
                          address: addressComponents.addressComplete,
                          location: [position.latitude, position.longitude],
                        ),
                      ));
                    } else {
                      log("Address components are null");
                      emit(MyAddressesSuccessState(
                        addresses: addresses,
                        selectedAddress: selectedAddress,
                      ));
                    }
                  } else {
                    log("Got decoded address error = ${decodedAddressModel.address}");
                    emit(MyAddressesSuccessState(
                      addresses: addresses,
                      selectedAddress: selectedAddress,
                    ));
                  }
                });
              } else {
                log("Location permission denied");
                emit(MyAddressesSuccessState(
                  addresses: addresses,
                  selectedAddress: selectedAddress,
                ));
              }
            });
          } else {
            emit(MyAddressesSuccessState(
              addresses: addresses,
              selectedAddress: selectedAddress,
            ));
          }
        }
      } else {
        log("Got address API error");
        emit(MyAddressesErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      log(e.toString());
      emit(MyAddressesErrorState(AppStrings.genericErrorMsg));
    }
  }

  void selectAddress(int? addressId) {
    if (state is MyAddressesSuccessState) {
      final currentState = state as MyAddressesSuccessState;
      if (currentState.addresses == null ||
          currentState.addresses!.isEmpty ||
          addressId == null ||
          currentState.addresses!
                  .indexWhere((element) => element.id == addressId) ==
              -1) {
        return;
      }

      final selectedAddress = currentState.addresses!
          .firstWhere((element) => element.id == addressId);

      emit(currentState.copyWith(selectedAddress: selectedAddress));
    }
  }

  MyAddressModel? getSelectedAddress(
    List<MyAddressModel> addresses,
  ) {
    if (addresses.isNotEmpty) {
      // Try to find an address where isDefault is true.
      MyAddressModel? selectedAddress =
          addresses.firstWhereOrNull((address) => address.isDefault == true);

      if (selectedAddress == null) {
        // No default address found, but the list is not empty.
        // Select the first address and make it the default.
        return addresses.first;
      }

      return selectedAddress; // Return the found default address (or null if the list was empty).
    }
    return null;
  }
}
