import 'package:equatable/equatable.dart';

abstract class DeleteAddressState extends Equatable {}

class DeleteAddressInitState extends DeleteAddressState {
  @override
  List<Object> get props => [];
}

class DeleteAddressLoadingState extends DeleteAddressState {
  @override
  List<Object> get props => [];
}

class DeleteAddressSuccessState extends DeleteAddressState {
  final String successMsg;
  DeleteAddressSuccessState(this.successMsg);
  @override
  List<Object> get props => [successMsg];
}

class DeleteAddressErrorState extends DeleteAddressState {
  final String errorMsg;
  DeleteAddressErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
