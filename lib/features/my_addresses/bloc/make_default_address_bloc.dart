import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import 'make_default_address_state.dart';

class MakeDefaultAddressBloc extends Cubit<MakeDefaultAddressState> {
  @override
  MakeDefaultAddressBloc() : super(MakeDefaultAddressInitState());

  void markDefaultAddress({
    required String addressId,
  }) async {
    emit(MakeDefaultAddressLoadingState(addressId: addressId));
    try {
      final response = await ApiService.instance.makeDefaultAddress(addressId: addressId);
      if (response.success) {
        emit(MakeDefaultAddressSuccessState(response.message ?? ""));
      } else {
        emit(MakeDefaultAddressErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(MakeDefaultAddressErrorState(AppStrings.genericErrorMsg));
    }
  }
}
