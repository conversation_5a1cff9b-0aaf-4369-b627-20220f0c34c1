import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import '../data/save_address_req_model.dart';
import 'add_edit_address_state.dart';

class AddEditAddressBloc extends Cubit<AddEditAddressState> {
  @override
  AddEditAddressBloc() : super(AddEditAddressInitState());

  void addAddress({
    required AddressReqModel addressReqModel,
  }) async {
    emit(AddEditAddressLoadingState());
    try {
      final response = await ApiService.instance
          .addAddress(addressReqModel: addressReqModel);
      if (response.success) {
        emit(AddEditAddressSuccessState(response.message ?? ""));
      } else {
        emit(AddEditAddressErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(AddEditAddressErrorState(AppStrings.genericErrorMsg));
    }
  }

  void editAddress({
    required String addressId,
    required AddressReqModel addressReqModel,
  }) async {
    emit(AddEditAddressLoadingState());
    try {
      final response = await ApiService.instance
          .editAddress(addressId: addressId, addressReqModel: addressReqModel);
      if (response.success) {
        emit(AddEditAddressSuccessState(response.message ?? ""));
      } else {
        emit(AddEditAddressErrorState(
            response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(AddEditAddressErrorState(AppStrings.genericErrorMsg));
    }
  }
}
