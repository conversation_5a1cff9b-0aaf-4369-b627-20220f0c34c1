import 'package:equatable/equatable.dart';

import '../data/my_address_model.dart';

abstract class MyAddressesState extends Equatable {}

class MyAddressesInitState extends MyAddressesState {
  @override
  List<Object> get props => [];
}

class MyAddressesLoadingState extends MyAddressesState {
  @override
  List<Object> get props => [];
}

class MyAddressesSuccessState extends MyAddressesState {
  final List<MyAddressModel>? addresses;
  final MyAddressModel? selectedAddress;
  MyAddressesSuccessState({this.addresses, this.selectedAddress});
  @override
  List<Object?> get props => [addresses, selectedAddress];

  MyAddressesSuccessState copyWith({List<MyAddressModel>? addresses, MyAddressModel? selectedAddress}) {
    return MyAddressesSuccessState(addresses: addresses ?? this.addresses, selectedAddress: selectedAddress ?? this.selectedAddress);
  }
}

class MyAddressesErrorState extends MyAddressesState {
  final String errorMsg;
  MyAddressesErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
