import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/api/api_service.dart';
import '../../../utils/string_constants/app_strings.dart';
import 'delete_address_state.dart';

class DeleteAddressBloc extends Cubit<DeleteAddressState> {
  @override
  DeleteAddressBloc() : super(DeleteAddressInitState());

  void deleteAddress({
    required String addressId,
  }) async {
    emit(DeleteAddressLoadingState());
    try {
      final response = await ApiService.instance.deleteAddress(
          addressId: addressId);
      if (response.success) {
        emit(DeleteAddressSuccessState(response.message ?? ""));
      } else {
        emit(DeleteAddressErrorState(response.message ?? AppStrings.genericErrorMsg));
      }
    } catch (e) {
      emit(DeleteAddressErrorState(AppStrings.genericErrorMsg));
    }
  }
}
