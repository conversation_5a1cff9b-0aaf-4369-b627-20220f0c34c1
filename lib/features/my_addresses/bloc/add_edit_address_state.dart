import 'package:equatable/equatable.dart';

abstract class AddEditAddressState extends Equatable {}

class AddEditAddressInitState extends AddEditAddressState {
  @override
  List<Object> get props => [];
}

class AddEditAddressLoadingState extends AddEditAddressState {
  @override
  List<Object> get props => [];
}

class AddEditAddressSuccessState extends AddEditAddressState {
  final String successMsg;
  AddEditAddressSuccessState(this.successMsg);
  @override
  List<Object> get props => [successMsg];
}

class AddEditAddressErrorState extends AddEditAddressState {
  final String errorMsg;
  AddEditAddressErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
