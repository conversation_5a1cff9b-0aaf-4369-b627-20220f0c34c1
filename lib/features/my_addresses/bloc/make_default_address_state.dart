import 'package:equatable/equatable.dart';

abstract class MakeDefaultAddressState extends Equatable {}

class MakeDefaultAddressInitState extends MakeDefaultAddressState {
  @override
  List<Object> get props => [];
}

class MakeDefaultAddressLoadingState extends MakeDefaultAddressState {
  final String addressId;
  MakeDefaultAddressLoadingState({required this.addressId});

  @override
  List<Object> get props => [addressId];
}

class MakeDefaultAddressSuccessState extends MakeDefaultAddressState {
  final String successMsg;
  MakeDefaultAddressSuccessState(this.successMsg);
  @override
  List<Object> get props => [successMsg];
}

class MakeDefaultAddressErrorState extends MakeD<PERSON>aultAddressState {
  final String errorMsg;
  MakeDefaultAddressErrorState(this.errorMsg);
  @override
  List<Object> get props => [errorMsg];
}
