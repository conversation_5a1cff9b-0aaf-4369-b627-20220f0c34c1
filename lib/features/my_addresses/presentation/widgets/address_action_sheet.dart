import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/texts/app_text.dart';

class AddressActionSheet extends StatelessWidget {
  final Function() onEditAddress;
  final Function() onDeleteAddress;
  const AddressActionSheet({super.key, required this.onEditAddress, required this.onDeleteAddress});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 4.h,
            width: 36.w,
            decoration: BoxDecoration(
              color: colorScheme.lightGreyD0D8DE,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
          16.ph,
          InkWell(
            onTap: onEditAddress,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  SvgPicture.asset(AppImages.editIcon),
                  12.pw,
                  Text14Medium(AppStrings.editAddress),
                ],
              ),
            ),
          ),
          8.ph,
          InkWell(
            onTap: onDeleteAddress,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  SvgPicture.asset(AppImages.deleteIcon),
                  12.pw,
                  Text14Medium(AppStrings.delete, color: colorScheme.error),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
