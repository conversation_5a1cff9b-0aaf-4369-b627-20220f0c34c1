import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/features/my_addresses/bloc/delete_address_bloc.dart';
import 'package:homeservice_app/features/my_addresses/bloc/make_default_address_bloc.dart';
import 'package:homeservice_app/features/my_addresses/bloc/my_addresses_cubit.dart';
import 'package:homeservice_app/features/my_addresses/bloc/my_addresses_state.dart';
import 'package:homeservice_app/features/my_addresses/presentation/widgets/address_action_sheet.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/widgets/app_toast.dart';
import 'package:homeservice_app/widgets/textfields/app_textfield.dart';
import 'package:homeservice_app/widgets/warning_sheet.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../utils/string_constants/app_strings.dart';
import '../../../../widgets/app_gesture_detector.dart';
import '../../../../widgets/custom_appbar.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/google_map_location_picker/map_location_picker.dart';
import '../../../../widgets/loaction_disable_card.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/delete_address_state.dart';
import '../../bloc/make_default_address_state.dart';
import '../../data/my_address_model.dart';
import '../widgets/address_list_shimmer.dart';

class PickAddressScreen extends StatefulWidget {
  final String? cameFrom;

  const PickAddressScreen({
    super.key,
    this.cameFrom,
  });

  @override
  State<PickAddressScreen> createState() => _PickAddressScreenState();
}

class _PickAddressScreenState extends State<PickAddressScreen>
    with WidgetsBindingObserver {
  bool isLocationDenied = false;

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
    context.read<MyAddressesBloc>().getAddresses();
    WidgetsBinding.instance.addObserver(this);
  }

  /// Checks location permission and updates state accordingly.
  Future<void> _checkLocationPermission() async {
    final permission = await Geolocator.checkPermission();
    final denied = permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever;

    if (mounted && isLocationDenied != denied) {
      setState(() {
        isLocationDenied = denied;
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _checkLocationPermission();
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: CustomAppbar(title: AppStrings.selectLocation),
      body: BlocListener<DeleteAddressBloc, DeleteAddressState>(
        listener: (context, state) {
          if (state is DeleteAddressSuccessState) {
            Dialogs.hideDialog(context);
            context.read<MyAddressesBloc>().getAddresses();
          }
          if (state is DeleteAddressErrorState) {
            Dialogs.hideDialog(context);
            CustomToast.showToast(message: state.errorMsg);
          }
          if (state is DeleteAddressLoadingState) {
            Dialogs.showLoadingDialog(
              context,
              loadingText: AppStrings.deletingAddress,
            );
          }
        },
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.only(
              left: 20.w,
              right: 20.w,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  16.ph,
                  AppTextFormField(
                    contentPadding: EdgeInsets.zero,
                    prefixIcon: Padding(
                      padding: EdgeInsets.only(left: 12.w, right: 8.w),
                      child: SvgPicture.asset(AppImages.searchIcon),
                    ),
                    readOnly: true,
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MapLocationPicker(
                              apiKey: "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
                              isFocus: true,
                              latitude: isLocationDenied ? 26.9124 : null,
                              longitude: isLocationDenied ? 75.78 : null,
                            ),
                          ));
                    },
                    prefixIconConstraints: BoxConstraints(
                      minHeight: 20.h,
                      minWidth: 20.h,
                    ),
                    hintText: AppStrings.searchForAreaStreetName,
                  ),
                  if (isLocationDenied) ...[
                    24.ph,
                    LocationDisableCard(onEnableLocationPressed: () async {
                      Geolocator.requestPermission().then((value) async {
                        if (value == LocationPermission.deniedForever) {
                          Geolocator.openAppSettings();
                        } else {
                          if (mounted) _checkLocationPermission();
                        }
                      });
                    }),
                  ] else ...[
                    8.ph,
                    // Use Current Location Button
                    AppGestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MapLocationPicker(
                                apiKey:
                                    "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
                              ),
                            ));
                      },
                      child: Column(
                        children: [
                          16.ph,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(
                                      height: 24.h,
                                      width: 24.h,
                                      child: SvgPicture.asset(
                                          AppImages.locationIcon)),
                                  12.pw,
                                  Text20SemiBold(
                                    AppStrings.useCurrentLocation,
                                    fontSize: 14.sp,
                                    color: colorScheme.primary,
                                  ),
                                ],
                              ),
                              SvgPicture.asset(AppImages.arrowRightIcon),
                            ],
                          ),
                          16.ph,
                          Divider(
                            height: 1.h,
                            color: colorScheme.surface,
                          ),
                        ],
                      ),
                    ),
                  ],
                  // Add New Address Button
                  AppGestureDetector(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MapLocationPicker(
                              apiKey: "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
                              latitude: isLocationDenied ? 26.9124 : null,
                              longitude: isLocationDenied ? 75.78 : null,
                            ),
                          ));
                    },
                    child: Column(
                      children: [
                        16.ph,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                    height: 24.h,
                                    width: 24.h,
                                    child: SvgPicture.asset(AppImages.addIcon)),
                                12.pw,
                                Text20SemiBold(
                                  AppStrings.addNewAddress,
                                  fontSize: 14.sp,
                                  color: colorScheme.primary,
                                ),
                              ],
                            ),
                            SvgPicture.asset(AppImages.arrowRightIcon),
                          ],
                        ),
                        16.ph,
                        Divider(
                          height: 1.h,
                          color: colorScheme.surface,
                        ),
                      ],
                    ),
                  ),
                  16.ph,
                  // Saved Addresses Section
                  BlocListener<MakeDefaultAddressBloc, MakeDefaultAddressState>(
                    listener: (context, state) {
                      if (state is MakeDefaultAddressSuccessState) {
                        Dialogs.hideDialog(context);
                        context.read<MyAddressesBloc>().getAddresses();
                        Navigator.pop(context, true);
                      }
                      if (state is MakeDefaultAddressErrorState) {
                        Dialogs.hideDialog(context);
                        CustomToast.showToast(message: state.errorMsg);
                      }
                      if (state is MakeDefaultAddressLoadingState) {
                        Dialogs.showLoadingDialog(
                          context,
                          loadingText: AppStrings.loading,
                        );
                      }
                    },
                    child: BlocBuilder<MyAddressesBloc, MyAddressesState>(
                      builder: (context, state) {
                        if (state is MyAddressesLoadingState) {
                          return AddressListShimmer();
                        }
                        if (state is MyAddressesErrorState) {
                          return 1.ph;
                        }
                        if (state is MyAddressesSuccessState) {
                          if (state.addresses?.isEmpty ?? true) {
                            return 1.ph;
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text12Regular(
                                AppStrings.savedAddress,
                                color: colorScheme.subTextGrey6A6A6A,
                              ),
                              // Display Saved Addresses
                              ListView.builder(
                                shrinkWrap:
                                    true, // Important for nested ListView
                                physics:
                                    const NeverScrollableScrollPhysics(), // Disable scrolling for the inner ListView
                                itemCount: state.addresses!.length,
                                padding: EdgeInsets.zero,
                                itemBuilder: (context, index) {
                                  final address = state.addresses![index];
                                  return SavedAddressTile(
                                    address: address,
                                    onAddressSelected:
                                        (MyAddressModel selectedAddress) {
                                      context
                                          .read<MakeDefaultAddressBloc>()
                                          .markDefaultAddress(
                                              addressId: address.id.toString());
                                    },
                                    cameFrom: widget.cameFrom,
                                  );
                                },
                              ),
                            ],
                          );
                        }
                        return 1.ph;
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Saved Address Tile Widget
class SavedAddressTile extends StatelessWidget {
  final MyAddressModel address;
  final Function(MyAddressModel) onAddressSelected;
  final bool showOptions;
  final String? cameFrom;

  const SavedAddressTile({
    super.key,
    required this.address,
    required this.onAddressSelected,
    this.showOptions = true,
    this.cameFrom,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return AppGestureDetector(
      onTap: () {
        onAddressSelected(address);
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                padding: EdgeInsets.all(4.h),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: colorScheme.primary.withOpacity(0.1),
                ),
                child: SvgPicture.asset(AppImages.mapPinIcon)),
            8.pw,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (address.title != null && address.title!.isNotEmpty) ...[
                    Text14SemiBold(address.title ?? ""),
                    4.ph,
                  ],
                  Text12Regular(address.address ?? ""),
                ],
              ),
            ),
            if (address.isDefault ?? false)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text20SemiBold(
                  AppStrings.selected.toUpperCase(),
                  fontSize: 8.sp,
                  color: colorScheme.primary,
                ),
              ),
            4.pw,
            if (showOptions)
              AppGestureDetector(
                  onTap: () {
                    showModalBottomSheet(
                        enableDrag: false,
                        backgroundColor: colorScheme.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(16.r),
                            topRight: Radius.circular(16.r),
                          ),
                        ),
                        context: context,
                        builder: (_) => AddressActionSheet(
                              onDeleteAddress: () {
                                Navigator.pop(context);
                                showModalBottomSheet(
                                    enableDrag: false,
                                    backgroundColor: colorScheme.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(16.r),
                                        topRight: Radius.circular(16.r),
                                      ),
                                    ),
                                    context: context,
                                    builder: (_) => WarningSheet(
                                          title: AppStrings.deleteAddress,
                                          message:
                                              AppStrings.deleteAddressWarning,
                                          okButtonText: AppStrings.delete,
                                          okButtonColor: colorScheme.error,
                                          onOk: () {
                                            Navigator.pop(context);
                                            context
                                                .read<DeleteAddressBloc>()
                                                .deleteAddress(
                                                    addressId:
                                                        address.id.toString());
                                          },
                                        ));
                              },
                              onEditAddress: () {
                                Navigator.pop(context);
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => MapLocationPicker(
                                        apiKey:
                                            "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
                                        addressId: address.id,
                                        address: address,
                                        latitude: address.location?[0],
                                        longitude: address.location?[1],
                                        currentLatLng: LatLng(
                                            address.location?[0] ?? 0,
                                            address.location?[1] ?? 0),
                                        isEdit: true,
                                        cameFrom: cameFrom,
                                      ),
                                    ));
                              },
                            ));
                  },
                  child: SvgPicture.asset(AppImages.threeDotIcon)),
          ],
        ),
      ),
    );
  }
}
