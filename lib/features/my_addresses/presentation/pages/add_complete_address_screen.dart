import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:homeservice_app/core/routes/app_routes.dart';
import 'package:homeservice_app/features/my_addresses/bloc/add_edit_address_bloc.dart';
import 'package:homeservice_app/features/my_addresses/bloc/add_edit_address_state.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';
import 'package:homeservice_app/utils/location_methods.dart';
import 'package:homeservice_app/utils/string_constants/app_strings.dart';
import 'package:homeservice_app/widgets/custom_appbar.dart';
import 'package:homeservice_app/widgets/textfields/app_textfield.dart';
import 'package:remove_emoji_input_formatter/remove_emoji_input_formatter.dart';
import '../../../../utils/common_models/address_components_model.dart';
import '../../../../utils/string_constants/app_images.dart';
import '../../../../widgets/app_toast.dart';
import '../../../../widgets/buttons/category_button_widget.dart';
import '../../../../widgets/buttons/primary_button.dart';
import '../../../../widgets/dialogs.dart';
import '../../../../widgets/texts/app_text.dart';
import '../../bloc/my_addresses_cubit.dart';
import '../../bloc/my_addresses_state.dart';
import '../../data/add_complete_address_nav_data.dart';
import '../../data/save_address_req_model.dart';

enum AddressType {
  home(AppStrings.home),
  office(AppStrings.office),
  other(AppStrings.other);

  final String _name;

  const AddressType(this._name);

  @override
  String toString() => _name;

  static AddressType? getAddressTypeFromString(String? addressType) {
    switch (addressType) {
      case AppStrings.home:
        return AddressType.home;
      case AppStrings.office:
        return AddressType.office;
      case AppStrings.other:
        return AddressType.other;
      default:
        return AddressType.other;
    }
  }
}

class AddCompleteAddressScreen extends StatefulWidget {
  final AddCompleteAddressNavData? navData;
  const AddCompleteAddressScreen({super.key, this.navData});

  @override
  State<AddCompleteAddressScreen> createState() =>
      _AddCompleteAddressScreenState();
}

class _AddCompleteAddressScreenState extends State<AddCompleteAddressScreen> {
  final _houseNumberController = TextEditingController();
  final _addressTitleController = TextEditingController();
  final _pinCodeController = TextEditingController();
  String? addressHalf, addressComplete, sublocality, city, state, country;
  AddressType selectedAddressType = AddressType.home;
  @override
  void initState() {
    super.initState();
    _processAddressComponents();
  }

  void _processAddressComponents() {
    getAddressTitle();
    AddressComponentsModel? addressComponents =
        LocationMethods.getAddressComponents(
            geocodingResult: widget.navData?.geocodingResult);
    if (addressComponents != null) {
      sublocality = addressComponents.sublocality;
      city = addressComponents.city;
      state = addressComponents.state;
      country = addressComponents.country;
      _pinCodeController.text = addressComponents.pincode ?? "";
      _houseNumberController.text = widget.navData?.isEdit ?? false
          ? widget.navData?.address?.houseNo ?? ""
          : addressComponents.houseNo ?? "";
      addressHalf = addressComponents.addressHalf;
      addressComplete = addressComponents.addressComplete;
    }
  }

  void getAddressTitle() {
    if (widget.navData?.address != null) {
      selectedAddressType = AddressType.getAddressTypeFromString(
              widget.navData?.address?.title) ??
          AddressType.other;
      _addressTitleController.text = widget.navData?.address?.title ?? "";
    }
  }

  @override
  void dispose() {
    _houseNumberController.dispose();
    _pinCodeController.dispose();
    _addressTitleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: CustomAppbar(title: AppStrings.enterCompleteAddress),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            16.ph,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    padding: EdgeInsets.all(4.h),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colorScheme.primary.withOpacity(0.1),
                    ),
                    child: SvgPicture.asset(AppImages.mapPinIcon)),
                8.pw,
                Expanded(
                  child: Text12Regular(addressHalf ?? ""),
                ),
              ],
            ),
            16.ph,
            Divider(
              height: 1.h,
              color: colorScheme.surface,
            ),
            16.ph,
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: AddressType.values.map((addressType) {
                  return Padding(
                    padding: EdgeInsets.only(right: 10.w),
                    child: CategoryButtonRadioWidget(
                      label: addressType.toString(),
                      unselectedBorderColor: colorScheme.surface,
                      isSelected: addressType == selectedAddressType,
                      onPressed: () {
                        setState(() {
                          selectedAddressType = addressType;
                        });
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
            if (selectedAddressType == AddressType.other) ...[
              16.ph,
              AppTextFormField(
                titleText: AppStrings.addressTitle,
                controller: _addressTitleController,
                hintText: AppStrings.enterAddressTitle,
                maxLength: 50,
                inputFormatters: [
                  RemoveEmojiInputFormatter(),
                ],
              ),
            ],
            16.ph,
            AppTextFormField(
              titleText: AppStrings.houseNumber,
              controller: _houseNumberController,
              hintText: AppStrings.enterHouseNumber,
              maxLines: 2,
              maxLength: 150,
              inputFormatters: [
                RemoveEmojiInputFormatter(),
              ],
            ),
            16.ph,
            AppTextFormField(
              titleText: "${AppStrings.pinCode}*",
              controller: _pinCodeController,
              hintText: AppStrings.enterPinCode,
              maxLength: 8,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            )
          ],
        ),
      ),
      resizeToAvoidBottomInset: true,
      bottomSheet: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: BlocListener<AddEditAddressBloc, AddEditAddressState>(
            listener: (context, state) {
              if (state is AddEditAddressSuccessState) {
                Dialogs.hideDialog(context);
                CustomToast.showToast(message: state.successMsg, isSuccess: true);
                if (widget.navData?.cameFrom == AppRoute.paymentScreen) {
                  Navigator.popUntil(context, (route) {
                    if (route.settings.name == widget.navData?.cameFrom) {
                      (route.settings.arguments as Map)['result'] = {'message': state.successMsg};
                      return true;
                    }
                    return false;
                  });
                  return;
                }

                if (widget.navData?.cameFrom == AppRoute.profileSavedAddresses) {
                  Navigator.pop(context);
                  context.read<MyAddressesBloc>().getAddresses();
                  Navigator.pop(context, true);
                  return;
                }

                context.read<MyAddressesBloc>().getAddresses();
                Navigator.popUntil(
                  context,
                  (route) => route.settings.name == AppRoute.appBottomNavBar,
                );
              }
              if (state is AddEditAddressErrorState) {
                Dialogs.hideDialog(context);
                CustomToast.showToast(
                  message: state.errorMsg,
                );
              }
              if (state is AddEditAddressLoadingState) {
                Dialogs.showLoadingDialog(context,
                    loadingText: AppStrings.savingAddress);
              }
            },
            child: ListenableBuilder(
                listenable: Listenable.merge([
                  _houseNumberController,
                  _pinCodeController,
                ]),
                builder: (context, child) {
                  bool isButtonEnabled =
                      _houseNumberController.text.trim().isNotEmpty &&
                          _pinCodeController.text.trim().isNotEmpty &&
                          _pinCodeController.text.trim().length >= 5;
                  return PrimaryButton(
                    buttonText: widget.navData?.isEdit ?? false
                        ? AppStrings.updateAddressDetails
                        : AppStrings.saveAddress,
                    onPressed: isButtonEnabled
                        ? () {
                            final addressCubit = context.read<MyAddressesBloc>();
                            final addressState = addressCubit.state;
                            if (widget.navData?.isEdit ?? false) {
                              context.read<AddEditAddressBloc>().editAddress(
                                    addressId:
                                        widget.navData?.addressId?.toString() ??
                                            "",
                                    addressReqModel: AddressReqModel(
                                      title: selectedAddressType ==
                                              AddressType.other
                                          ? _addressTitleController.text.trim()
                                          : selectedAddressType.toString(),
                                      address:
                                          "${_houseNumberController.text.trim()}, $addressHalf, ${_pinCodeController.text.trim()}",
                                      city: city,
                                      country: country,
                                      houseNo: _houseNumberController.text,
                                      isDefault: widget.navData?.cameFrom == AppRoute.profileSavedAddresses
                                          ? widget.navData?.address?.isDefault == true
                                          : true,
                                      location: [
                                        widget.navData?.latitude ?? 0,
                                        widget.navData?.longitude ?? 0,
                                      ],
                                      pincode: _pinCodeController.text,
                                      state: state,
                                    ),
                                  );
                            } else {
                              context.read<AddEditAddressBloc>().addAddress(
                                    addressReqModel: AddressReqModel(
                                      address:
                                          "${_houseNumberController.text.trim()}, $addressHalf, ${_pinCodeController.text.trim()}",
                                      title: selectedAddressType ==
                                              AddressType.other
                                          ? _addressTitleController.text
                                                  .trim()
                                                  .isEmpty
                                              ? selectedAddressType.toString()
                                              : _addressTitleController.text
                                                  .trim()
                                          : selectedAddressType.toString(),
                                      city: city,
                                      country: country,
                                      houseNo: _houseNumberController.text,
                                      isDefault: widget.navData?.cameFrom == AppRoute.profileSavedAddresses
                                          ? (addressState is MyAddressesSuccessState && (addressState.addresses?.isEmpty ?? true))
                                          : true,
                                      location: [
                                        widget.navData?.latitude ?? 0,
                                        widget.navData?.longitude ?? 0,
                                      ],
                                      pincode: _pinCodeController.text,
                                      state: state,
                                    ),
                                  );
                            }
                          }
                        : null,
                  );
                }),
          ),
        ),
      ),
    );
  }
}
