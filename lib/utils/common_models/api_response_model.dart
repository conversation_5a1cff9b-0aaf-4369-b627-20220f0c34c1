import 'package:dio/dio.dart';

class ApiResponse {
  late bool success;
  String? message;
  dynamic errors;
  dynamic data;

  ApiResponse({this.data, this.message, this.errors, this.success = false});

  ApiResponse.fromJson(Map<String, dynamic> json) {
    success = json["success"] ?? false;
    message = json["message"];
    if (json["errors"] != null) {
      errors = json["errors"];
    }
    if (json["data"] != null) {
      data = json["data"];
    }
  }
}

Future<ApiResponse> apiRequest({
  required Future<dynamic> request
}) async {
  try {
    Response response = await request;
    
    switch (response.statusCode) {
      case 200:
        return ApiResponse(
          success: true,
          message: (response.data != null && response.data.isNotEmpty)
              ? response.data['message']
              : "",
          data: (response.data != null && response.data.isNotEmpty)
              ? response.data['data']
              : ""
        );
        
      case 201:
        return ApiResponse(
          success: true,
          message: (response.data.isNotEmpty) 
              ? response.data['message'] 
              : "",
          data: (response.data != null && response.data.isNotEmpty)
              ? response.data['data']
              : ""
        );
        
        case 204:
        return ApiResponse(
          success: true,
        );
        
      default:
        return ApiResponse(
          success: false,
          message: (response.data.isNotEmpty)
              ? response.data['message']
              : "Something Went Wrong",
          errors: (response.data.isNotEmpty)
              ? response.data['errors']['data']
              : "",
          data: (response.data.isNotEmpty) 
              ? response.data['data']
              : ""
        );
    }
    
  } on DioException catch (e) {
    final message = (e.response != null && e.response?.data != null)
        ? (e.response?.data is Map<String, dynamic> && e.response?.data!.containsKey('message'))
            ? e.response?.data['message']?.toString()
            : "Something Went Wrong"
        : "Something Went Wrong";
    return ApiResponse(
      success: false,
      message: message,
      errors: e.response?.data['errors']['data'],
      data: e.response?.data['data'],
    );
  }
}