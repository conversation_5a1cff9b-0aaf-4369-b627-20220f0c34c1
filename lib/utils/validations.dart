import 'string_constants/app_strings.dart';

class Validator {
  static String? emailValidator(String value) {
    String pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = RegExp(pattern);
    if (!regex.hasMatch(value)) {
      return ValidationMsg.enterValidEmail;
    } else {
      return null;
    }
  }

  static String? firstNameValidator(String value) {
    if (value.length < 3) {
      return ValidationMsg.enterFirstName;
    } else {
      return null;
    }
  }

  static String? lastNameValidator(String value) {
    if (value.length < 3) {
      return ValidationMsg.enterLastName;
    } else {
      return null;
    }
  }

  static String? phoneNumberValidator(String value) {
    // Remove non-digit characters
    final cleanedValue = value.replaceAll(RegExp(r'\D'), '');

    // Remove leading zeros
    final trimmedValue = cleanedValue.replaceFirst(RegExp(r'^0+'), '');

    // Check if it's empty or less than 10 digits after trimming
    if (trimmedValue.isEmpty || trimmedValue.length < 10) {
      return ValidationMsg.enterPhoneNumber;
    }

    return null;
  }

  static String? otpValidator(String value) {
    if (value.length < 6) {
      return ValidationMsg.plsEnterValidOtp;
    } else {
      return null;
    }
  }

  static String? emptyValidator(String? value, String errorMessage, {int minCharLimit = 1, bool isNumber = false}) {
    if (value == null || value.trim().isEmpty || value.trim().length < minCharLimit || (isNumber && (int.tryParse(value) == null || int.tryParse(value) == 0))) {
      return errorMessage;
    }
    return null;
  }
}
