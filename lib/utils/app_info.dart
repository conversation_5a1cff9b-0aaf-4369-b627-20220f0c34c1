import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppPackageInfo {
  static final AppPackageInfo _instance = AppPackageInfo._internal();

  factory AppPackageInfo() => _instance;

  AppPackageInfo._internal();

  static late final PackageInfo _packageInfo;
  static late final BaseDeviceInfo _deviceInfoPlugin;

  static Future<void> init() async {
    _packageInfo = await PackageInfo.fromPlatform();
    _deviceInfoPlugin = await DeviceInfoPlugin().deviceInfo;
  }

  static String get appName => _packageInfo.appName;

  static String get packageName => _packageInfo.packageName;

  static String get version => _packageInfo.version;

  static String get buildNumber => _packageInfo.buildNumber;

  static String get deviceName => (_deviceInfoPlugin.data['name'] ?? '${_deviceInfoPlugin.data['brand']} ${_deviceInfoPlugin.data['model']}')?.replaceAll("’", "") ?? "Unknown";
}
