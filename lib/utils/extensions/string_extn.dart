extension StringExtension on String {
  String removeLastChar() {
    if (isEmpty) return this; // Return as is if string is empty
    return substring(0, length - 1);
  }

  String capitalizeString(bool toUpper) {
    if (isEmpty) return "";
    return "${toUpper ? this[0].toUpperCase() : this[0].toLowerCase()}${substring(1).toLowerCase()}";
  }

  String get toUpperCaseEachFirst => split(" ").map((str) => str.capitalizeString(true)).join(" ");

  String get toTitleCase => replaceAll(RegExp(' +'), ' ').split(' ').map((str) => str.toUpperCaseEachFirst).join(' ');
}
