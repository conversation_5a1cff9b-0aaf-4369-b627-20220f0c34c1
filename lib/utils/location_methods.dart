import 'package:google_maps_webapi/geocoding.dart';
import 'common_models/address_components_model.dart';
import 'common_models/decode_address_return_model.dart';

class LocationMethods {
  /// Decode address from latitude & longitude
  static Future<DecodeAddressReturnModel> decodeAddress({
    required double lat,
    required double long,
  }) async {
    try {
      final geocoding = GoogleMapsGeocoding(
        apiKey: "AIzaSyDJdcB-MT6Ml-G_rS2kW-Jh_moq_C28ze8",
      );
      final response = await geocoding.searchByLocation(
        Location(lat: lat, lng: long),
      );

      /// When get any error from the API, show the error in the console.
      if (response.hasNoResults ||
          response.isDenied ||
          response.isInvalid ||
          response.isNotFound ||
          response.unknownError ||
          response.isOverQueryLimit) {
        return DecodeAddressReturnModel(
            success: false,
            address: response.errorMessage ?? "",
            lat: lat,
            long: long);
      }
      return DecodeAddressReturnModel(
          success: true,
          address: response.results.first.formattedAddress ?? "",
          geocodingResult: response.results.first,
          lat: lat,
          long: long);
    } catch (e) {
      return DecodeAddressReturnModel(
          success: false, address: "", lat: lat, long: long);
    }
  }

  static AddressComponentsModel? getAddressComponents(
      {GeocodingResult? geocodingResult}) {
    void addUniqueAddressPart(List<String> parts, String? part) {
      if (part != null && !parts.contains(part)) {
        parts.add(part);
      }
    }

    String? sublocality, city, state, country, pincode;

    final addressComponents = geocodingResult?.addressComponents;
    if (addressComponents == null || addressComponents.isEmpty) {
      return null; // Early return if no address components
    }

    final typesToCheck = {
      // Use a Set for faster contains checks
      'subpremise',
      'street_number',
      'premise',
      'landmark'
    };
    final presentTypes = <String>[];
    final addressParts = <String>[];

    for (final component in addressComponents) {
      // Efficiently check for multiple types using any
      if (typesToCheck.any((type) => component.types.contains(type))) {
        presentTypes.add(component.longName);
      }

      // Use a switch statement for clearer type checking
      switch (component.types.firstOrNull) {
        case "political":
        case "sublocality":
        case "sublocality_level_3":
          sublocality = component.longName;
          addUniqueAddressPart(addressParts, sublocality);
          break;
        case "locality":
        case "administrative_area_level_3":
          city = component.longName;
          addUniqueAddressPart(addressParts, city);
          break;
        case "administrative_area_level_1":
        case "state":
          state = component.longName;
          addUniqueAddressPart(addressParts, state);
          break;
        case "country":
          country = component.longName;
          addUniqueAddressPart(addressParts, country);
          break;
        case "postal_code":
        case "zip_code": // You probably only need postal_code.  zip_code is redundant
          pincode = component.longName;
          break;
      }
    }
    return AddressComponentsModel(
        addressComplete: geocodingResult?.formattedAddress ?? "",
        addressHalf: addressParts.join(', '),
        houseNo: presentTypes.join(', '),
        sublocality: sublocality,
        city: city,
        state: state,
        country: country,
        pincode: pincode);
  }
}
