import 'package:flutter/material.dart';
import 'package:homeservice_app/core/theme/app_theme.dart';
import 'package:homeservice_app/utils/extensions/empty_space_extn.dart';

import '../widgets/buttons/primary_button.dart';
import '../widgets/texts/app_text.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String message;
  final String okButtonText;
  final String dismissButtonText;
  final VoidCallback onOkButtonPressed;
  final VoidCallback? onDismissButtonPressed;

  const CustomDialog({
    super.key,
    required this.title,
    required this.message,
    required this.okButtonText,
    required this.dismissButtonText,
    required this.onOkButtonPressed,
    this.onDismissButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.white,
      title: Center(child: Text14Medium(title)),
      content: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          16.ph,
          Text14Medium(message),
          24.ph,
          PrimaryButton(
            onPressed: onOkButtonPressed,
            buttonText: okButtonText,
          ),
          16.ph,
          if (onDismissButtonPressed != null)
            TextButton(
              onPressed: onDismissButtonPressed,
              child: Text14Medium(dismissButtonText),
            ),
        ],
      ),
    );
  }
}

void customDialog(
  BuildContext context, {
  required String title,
  required String message,
  required String okButtonText,
  required String dismissButtonText,
  required VoidCallback onOkButtonPressed,
  VoidCallback? onDismissButtonPressed,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return CustomDialog(
        title: title,
        message: message,
        okButtonText: okButtonText,
        dismissButtonText: dismissButtonText,
        onOkButtonPressed: onOkButtonPressed,
        onDismissButtonPressed: onDismissButtonPressed,
      );
    },
  );
}
