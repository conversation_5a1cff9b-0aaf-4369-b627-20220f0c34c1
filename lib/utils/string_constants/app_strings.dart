class AppStrings {
  static const legUp = 'Home Service';
  static const whereCareMeetsComfort = 'Where care meets comfort';
  static const search = "Search";
  static const selectCountry = "Select country";
  static const enterPhoneNumber = "Enter phone number";
  static const phoneNumber = "Phone number";
  static const signIn = "Sign in";
  static const getOTP = "Get OTP";
  static const acceptTAndC = "By continuing, I accept Home Service's ";
  static const tAndC = "Terms & Condition";
  static const privacyP = "Privacy Policy";
  static const facingissue = "Facing any issue? ";
  static const contactUs = "Contact Us";
  static const refresh = "Refresh";
  static const cancel = "Cancel";
  static const okay = "Okay";
  static const genericErrorMsg = "Oops! Something went wrong.";
  static const loading = "Loading...";
  static const otpVerification = "OTP Verification";
  static const plsEnter6DigitCode = "Please enter the 6-digit code sent on your ";
  static const didntReceiveCode = "Didn't receive code?";
  static String resendIn(int timeLeft) => " Resend in ${timeLeft}s";
  static const verifyOTP = "Verify OTP";
  static const resendOTP = " Resend OTP";
  static const gettingOTP = "Getting OTP";
  static const verifyingOTP = "Verifying OTP";
  static const discountOff = 'OFF';
  static const enterOtp = 'Enter OTP';
  static const verifiedProfessions100 = '100% verified\nprofessionals';
  static const legUpInstant = 'Home Service instant';
  static const getServiceIn = 'Get services in ';
  static const mins15 = '15mins';
  static const legUpLongTerm = 'Home Service long term';
  static const legUpLongTermDescription = 'Shortlist your ideal caregiver for long term needs.';
  static const quickBookWithLegUpInstant = 'Quick book with\nLegUp instant';
  static const babyCare = 'Baby care';
  static const elderlyCare = 'Elderly Care';
  static const bookNow = 'Book Now';
  static const availabilityReplacementRefund = '24/7 Availability\nUnlimited Replacements\n100% Refund Policy';
  static const onDemandOrLongTerm = 'On demand\nor Long term';
  static const trainedVerifiedCareGivers = '500+\ntrained and\nverified\ncaregivers';
  static const weGotYouCovered = 'We got you covered';
  static const ourCareGivers = 'Our\ncaregivers';
  static const onlyTheBestForYourLovedOnes = 'Only the best for\nyour loved ones';
  static const madeWithLoveAndEmpathy = 'Made with\nlove and\nempathy';
  static const image = 'image';
  static const video = 'video';
  static const findYourIdealLongTermCareGiver = 'Find your ideal long term care giver';
  static const customizedJustForYou = 'Customized just for you';
  static const scheduleNow = 'Schedule Now';
  static const requestService = 'Request Service';
  static const chooseLocation = "Choose Location";
  static const searchForAreaStreetName = "Search for area, street name...";
  static const updateAddressDetails = "Update Address Details";
  static const selectLocation = "Select Location";
  static const useCurrentLocation = "Use Current Location";
  static const addNewAddress = "Add New Address";
  static const savedAddress = "Saved Address";
  static const selected = "Selected";
  static const editAddress = "Edit Address";
  static const delete = "Delete";
  static const deleteAddress = "Delete Address?";
  static const deleteAddressWarning =
      "Are you sure you want to delete this address?";
  static const ok = "Ok";
  static const houseNumber = "House No./Street & Locality/Apartment*";
  static const pinCode = "Pin code";
  static const enterPinCode = "Enter pin code";
  static const enterCompleteAddress = "Enter complete address";
  static const enterHouseNumber = "Enter house No./street & locality/apartment";
  static const saveAddress = "Save Address";
  static const savingAddress = "Saving address...";
  static const continueTxt = "Continue";
  static const deletingAddress = "Deleting address...";
  static const home = "Home";
  static const bookings = "Bookings";
  static const office = "Office";
  static const other = "Other";
  static const enterAddressTitle = "Enter address title";
  static const addressTitle = "Address title";
  static const locationNotSelected = 'Location not\nselected';
  static const getServicesWithInMinutes = 'Get services within minutes';
  static const locationUnavailable = 'Location unavailable';
  static const locationUnavailableTitle = 'Unable to fetch location';
  static const locationUnavailableDescription = 'Your location helps us find caregivers around you';
  static const locationDisabled = 'Device location disabled';
  static const enableLocationAccess = 'Enable location access';
  static const selectALocation = 'Select a location';
  static const upcomingRenewal = 'Upcoming renewal';
  static const serviceExpired = 'Service expired';
  static const renewNow = 'Renew Now';
  static String pendingActionsCount(int count) => '$count Pending actions';
  static const startsAt = 'starts at';
  static const perHour = ' /hour';
  static const select = 'Select';
  static const confirm = 'Confirm';
  static const hour = 'hour';
  static const caregiverLanguagePreference = 'Caregiver language preference';
  static const free = 'Free';
  static const addOns = 'Add ons';
  static const durationOfService = 'Duration of service';
  static const selectDuration = 'Select duration';
  static const selectLanguage = 'Select language';
  static const instantOrPrebook = 'Instant or prebook';
  static const flexibleTimeSlots = 'Flexible time slots';
  static const preferredLanguageSelection = 'Preferred language selection';
  static const expertsInAllAgeGroup = 'Experts in all age group';
  static const howToBookAnInstantCaregiver = 'How to book an instant caregiver? ';
  static const learnMore = 'Learn more';
  static const verifiedProfessionals = '100% verified professionals';
  static const gotQuestions = 'Got questions?';
  static const ourOnboardingExpertIsJustOneCallAway = 'Our onboarding\nexpert is just one\ncall away';
  static const legUpPromise = 'Home Service promise';
  static const legUpPromiseDescription = "Every nanny is background-checked and trained to deliver attentive, professional care tailored to your baby's needs.";
  static const legUpCustomerStories = 'Home Service customer stories';
  static const simpleAndEasyBooking = 'Simple and easy booking';
  static const gotIt = 'Got it';
  static const couponsAndOffers = 'Coupons and offers';
  static const payByAnyUpiApp = 'Pay by any UPI app';
  static const payByCard = 'Credit/Debit card, Net banking';
  static const totalToPay = 'Total to pay';
  static const itemTotal = 'Item total';
  static const gst18 = 'GST 18%';
  static const couponDiscount = 'Coupon discount';
  static const safeAndSecurePayment = '100% safe & secure payment';
  static const change = 'Change';
  static const hours = 'hours';
  static String instantCare(String name) => 'Instant $name';
  static const noSubServicesFound = 'No sub-services found';
  static const caregiverArrivalTime = 'Caregiver arrival time';
  static const instant = 'Instant';
  static const instantLowerCase = 'instant';
  static const within15mins = 'Within 15mins';
  static const scheduleForLater = 'Schedule for later';
  static const startTimeOfService = 'Start time of service';
  static const bookingSlot10amTo8pm = 'Booking slot 10am to 8pm';
  static const proceedToPayment = 'Proceed to payment';
  static const notAvailable = "Not available";
  static const instantIn = 'IN';
  static const instantSL = 'SL';
  static const starts = 'Starts';
  static const instant15mins = 'Instant 15mins';
  static const selectPaymentMethod = 'Select payment method';
  static const submitChanges = 'Submit changes';
  static const processing = 'Processing...';
  static const retryingPayment = 'Retrying payment...';
  static const verifyingPayment = 'Verifying payment...';
  static const paymentFailed = 'Payment failed';
  static const paymentFailedDescription = 'Be assured any amount deducted will be refunded to your source account.';
  static const retrySameMethod = 'Retry Same Method';
  static const tryAnotherMethod = 'Try Another Method';
  static const bookingSuccessful = 'Booking successful';
  static const completePayment = 'Complete payment';
  static const paymentVerificationPending = 'Payment verification pending';
  static const paymentVerificationPendingDescription = 'Your payment is being verified. Please wait while we process your payment. You can view updated payment status under bookings tab.';

  // Booking status enums
  static const bookingStatusPending = 'Pending';
  static const bookingStatusBooked = 'Booked';
  static const bookingStatusConfirmed = 'Confirmed';
  static const bookingStatusStarted = 'Started';
  static const bookingStatusCompleted = 'Completed';
  static const bookingStatusCancelled = 'Cancelled';
  static const bookingStatusFailed = 'Failed';

  // Payment status enums
  static const transactionStatusPending = 'Pending';
  static const transactionStatusSuccess = 'Success';
  static const transactionStatusFailed = 'Failed';
  static const transactionStatusCancelled = 'Cancelled';
  static const transactionStatusRefundInitiated = 'Refund Initiated';
  static const transactionStatusRefundCompleted = 'Refund Completed';
  static const transactionStatusRefundFailed = 'Refund Failed';
  static const transactionStatusExpired = 'Expired';
  static const transactionStatusStale = 'Stale';

  // CMS dropdown sub-type enums
  static const experience = 'experience';
  static const languages = 'languages';
  static const nativeLanguages = 'native languages';
  static const workHours = 'work hours';
  static const nonInstant = 'non instant';

  static const addAddressManually = 'Add address manually';
  static const addressUpdated = 'Address updated';
  static const selectedAddressInstantUnavailableAtThisTime = 'Selected address is not serviceable for Home Service Instant at this time';
  static const youCanChooseToScheduleTheServiceForALaterTime = 'You can choose to schedule the service for a later time for this address.';
  static const selectedAddressIsNotServiceableTitle = 'Selected address is not serviceable by Home Service';
  static const selectedAddressIsNotServiceableDescription = 'You can choose to continue with current address or discard booking';
  static const updatingAddress = 'Updating address...';
  static const invalidTimeSelected = 'Service is only available between 10am to 8pm';
  static const bookingDetails = 'Booking details';
  static const speciality = 'Speciality';
  static const language = 'Language';
  static const type = 'Type';
  static const scheduledFor = 'Scheduled for';
  static const changedYourMind = 'Changed your mind?';
  static const caregiverDetailsDescription = 'Caregiver details will be shared once the booking is confirmed';
  static const needHelpWithBooking = 'Need help with your booking?';
  static const ourServiceExpertIsJustOneCallAway = 'Our service expert is just one call away';
  static const ourServiceExpertIsJustOneCallOrEmailAway = 'Our service expert is just one call or email away';
  static const totalPaid = 'Total Paid';
  static const totalAmount = 'Total Amount';
  static const cancelBooking = 'Cancel Booking?';
  static const cancelBookingDescription = 'You are about to cancel the booking, any paid amount will be refunded to your source payment method within 14 days.';
  static const dontCancel = 'Don’t Cancel';
  static const cancelBookingButton = 'Confirm Cancel';
  static const cancelBookingTimerDescription = 'Don’t worry. You can cancel the booking with 1 minute of booking and we’ll process full refund.';
  static const refundInitiated = 'Refund has been initiated to your source\naccount';
  static const refundInitiatedDescription = 'It may take up-to 14 working days to get credited.';
  static const refundCompletedDescription = 'Refund successfully credited to source.';
  static const continueBooking = 'Continue booking';
  static const discardBooking = 'Discard booking';
  static const discardBookingTitle = 'Are you sure you want to discard this booking?';
  static const discardBookingDescription = 'Discarding this booking will remove it from your draft bookings.';
  static const discardBookingTitleInstant = 'You have a pending booking';
  static const discardBookingDescriptionInstant = 'Are you sure you want to discard your pending booking and start a new one?';
  static const bookingCancelledSuccessfully = 'Booking cancelled';
  static const downloadInvoice = 'Download Invoice';
  static const profile = 'Profile';
  static const savedAddresses = 'Saved Addresses';
  static const savedAddressDescription = 'View and manage your saved addresses';
  static const view = 'View';
  static const edit = 'Edit';
  static const yourLegUpAccount = 'Your Home Service Account';
  static const yourLegUpAccountDescription = 'Your customer journey, Log-out, Delete account';
  static const needAssistance = 'Need assistance?';
  static const privacyPolicyDescription = 'Learn how we handle your data and protect your privacy.';
  static const tAndCDescription = 'Understand the rules and terms for using our services.';
  static const legUpAccount = 'Home Service Account';
  static const manageAccount = 'Manage account';
  static const logOutOfLegUpApp = 'Log-Out of Home Service app';
  static const logOut = 'Log-Out';
  static const deleteYourLegUpAccount = 'Delete your Home Service account';
  static const deleteAccount = 'Delete Account';
  static const loggingOut = 'Logging out...';
  static const deletingAccount = 'Deleting account...';
  static const deleteAccountDescription = 'Are you sure you want to delete your Home Service account?';
  static const or = 'Or';
  static const specifyOtherReason = 'Specify other reason';
  static const pleaseRateCaregiverExperience = 'Please rate our caregiver experience';
  static const weAreSorryToHearYouThatPleaseShareDetails = 'We’re sorry to hear you that, please share details';
  static const selectCaregiverIssue = 'Select caregiver issue';
  static const otherReason = 'Other reason';
  static const pleaseShareDetails = 'Please share details about your experience';
  static const submit = 'Submit';
  static const serviceCompleted = 'Service completed';
  static const feedbackSubmitted = 'Feedback submitted';
  static const active = 'Active';
  static const renewalOn = 'Renewal on';
  static const currentBookings = 'Current Bookings';
  static const pastBookings = 'Past Bookings';
  static const otp = 'OTP';
  static const noActiveBookings = 'No active bookings';
  static const manageAllYourActiveBookingsHere =
      'Manage all your active bookings here';
  static const noUpcomingBookings = 'No upcoming bookings';
  static const manageAllYourUpcomingBookingsHere =
      'Manage all your upcoming bookings here';
  static const noBookingHistory = 'No booking history';
  static const retry = 'Retry';
  static const manageAllYourPastBookingsHere =
      'Manage all your past bookings here';
  static const expert = 'Expert';
  static const longTerm = 'LONG-TERM';
  static const caregiverDetailsWillBeSharedOnceTheBookingIsConfirmed = 'Caregiver details will be shared once the booking is confirmed';
  static const noSavedAddresses = 'No Saved Addresses';
  static const manageAllYourSavedAddressesHere = 'Manage all your saved addresses here';
  static const helpAndSupportAndFaq = 'Help, Support & FAQ';
  static const helpAndSupportAndFaqDescription = 'Browse FAQs or contact our support team for assistance';
  static const ourServiceExpertIsJustOneEmailAway = 'Our service expert is just one email away.';
  static const emailUs = 'Email Us';
  static const faqTitle = 'FAQs (Frequently Asked Questions)';
  static const updatingDefaultAddress = 'Updating default address...';
  static const defaultAddressUpdatedSuccessfully = 'Default address updated successfully';
  static const logout = 'Logout';
  static const logoutDescription = 'Are you sure you want to logout?';
  static const yesLogout = 'Yes, Logout';
  static String selectServiceType(String serviceName) => 'Select $serviceName type';
  static const ourServiceIsntAvailableInYourLocation = 'Our service isn’t available in your location';
  static const weAreWorkingOnExpanding = 'We’re working on expanding, stay tuned!';
  static String caretakers(int count) => count > 1 ? 'Caretakers' : "Caretaker";
  static const nearYou = 'Near you';
  static const instantBookingUnavailable = 'Instant booking unavailable';
  static const youCanScheduleForLaterOrInstantBookFrom10amTo8pm = 'You can schedule for later or Instant book from 10am to 8pm';
  static const onwards = 'Onwards';
  static const newUpdateIsAvailable = 'NEW UPDATE IS AVAILABLE';
  static const newUpdateIsRequired = 'NEW UPDATE IS REQUIRED';
  static const updateAvailable = 'Update your application to the latest version';
  static const updateAvailableDescription = 'A brand new version of Home Service app is available. Please update your app to use all of our amazing features.';
  static const skip = 'Skip';
  static const update = 'Update';
  static const startingAt = 'Starting at';
  static const refundsAndCancellation = 'Refunds & Cancellation';
  static const refundsAndCancellationDescription = 'Learn about our refund and cancellation policy';
  static const refundsAndCancellationPolicy = 'Refunds & Cancellation Policy';
  static const checkingAddressServiceability = 'Checking address serviceability';
  static const applyCoupon = 'Apply coupon';
  static const enterCouponCode = 'Enter coupon code';
  static const apply = 'Apply';
  static const remove = 'Remove';
  static const availableOffers = 'Available offers';
  static const off = 'OFF';
  static String offUpTo(String amount) => 'Up to ₹$amount off';
  static String flatDiscount(String amount) => 'Flat $amount%';
  static const noCouponsAvailable = 'No coupons available';
  static const couponNotFound = 'Coupon not found';
  static const couponAlreadyApplied = 'Coupon already applied';
  static const applied = 'Applied';
  static String codeWithCode(String code) => 'Code: $code';
  static const legUpUser = 'Home Service User';
  static const nameCannotBeEmpty = 'Name cannot be empty';
  static const nameUpdatedSuccessfully = 'Name updated successfully';
}

class ValidationMsg {
  static const enterFirstName = 'Please enter first name';
  static const enterLastName = 'Please enter last name';
  static const enterEmail = 'Please enter email';
  static const enterValidEmail = 'Please enter valid email';
  static const enterPhoneNumber = 'Please enter a valid phone number';
  static const audioSizeError = 'Audio size should be less then 50 MB';
  static const plsEnterOtp = 'Please enter OTP';
  static const plsEnterValidOtp = 'Please enter valid OTP';
  static const plsEnterBio = 'Please enter bio';
  static const plsEnterCouponCode = 'Please enter coupon code';
}
