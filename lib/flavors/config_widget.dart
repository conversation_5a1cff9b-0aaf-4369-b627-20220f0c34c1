import 'package:flutter/material.dart';

import '../core/config/app_config.dart';
import '../core/config/app_flavors.dart';
import '../core/config/metadata_model.dart';

class ConfigWidget extends StatefulWidget {
  final Widget child;
  final Flavor flavor;

  const ConfigWidget({
    super.key,
    required this.flavor,
    required this.child,
  });

  @override
  State<ConfigWidget> createState() => _ConfigWidgetState();
}

class _ConfigWidgetState extends State<ConfigWidget> {
  @override
  void initState() {
    super.initState();
    AppConfig.init(
      flavor: widget.flavor,
      baseUrl: BaseUrl.getBaseUrl(widget.flavor),
      cashfreeEnv: CashfreeEnvironment.getCashfreeEnvironment(widget.flavor),
      metadata: MetadataModel.getMetadata(widget.flavor),
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
