import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:homeservice_app/utils/responsive.dart';

import 'core/dependency_injection/service_locator.dart';
import 'core/navigation/navigation_service.dart';
import 'core/route_observer/route_observer_impl.dart';
import 'core/routes/app_routes.dart';
import 'core/theme/app_theme.dart';
import 'features/booking_details/bloc/cancel_booking_cubit.dart';
import 'features/booking_type/bloc/instant_service_available_cubit.dart';
import 'features/common/cms_dropdown_data/bloc/cms_dropdown_data_cubit.dart';
import 'features/common/continue_booking/bloc/continue_booking_cubit.dart';
import 'features/dashboard/bloc/caregiver_count_cubit.dart';
import 'features/dashboard/bloc/is_area_serviceable_cubit.dart';
import 'features/my_addresses/bloc/add_edit_address_bloc.dart';
import 'features/my_addresses/bloc/delete_address_bloc.dart';
import 'features/my_addresses/bloc/make_default_address_bloc.dart';
import 'features/my_addresses/bloc/my_addresses_cubit.dart';
import 'features/profile/bloc/delete_account_cubit.dart';
import 'features/profile/bloc/faqs_cubit.dart';
import 'features/profile/bloc/logout_cubit.dart';
import 'features/profile/bloc/profile_cubit.dart';
import 'features/service_booking/bloc/booking_cubit.dart';
import 'features/service_booking/bloc/coupon_offers_cubit.dart';
import 'features/service_booking/bloc/payment_service_cubit.dart';
import 'features/service_booking/bloc/sub_service_cubit.dart';
import 'features/service_booking/bloc/sub_service_instructions_cubit.dart';
import 'features/service_booking/bloc/testimonials_cubit.dart';
import 'features/splash/bloc/splash_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await setupLocator();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => SplashScreenCubit(),
        ),
        BlocProvider(
          create: (context) => MyAddressesBloc(),
        ),
        BlocProvider(
          create: (context) => AddEditAddressBloc(),
        ),
        BlocProvider(
          create: (context) => DeleteAddressBloc(),
        ),
        BlocProvider(
          create: (context) => MakeDefaultAddressBloc(),
        ),
        BlocProvider(create: (_) => TestimonialsCubit()),
        BlocProvider(create: (_) => SubServiceCubit()),
        BlocProvider(create: (_) => SubServiceInstructionsCubit()),
        BlocProvider(create: (_) => PaymentServiceCubit(), lazy: false),
        BlocProvider(create: (_) => InstantServiceAvailableCubit()),
        BlocProvider(create: (_) => BookingCubit()),
        BlocProvider(create: (_) => CMSDropdownDataCubit()),
        BlocProvider(create: (_) => ContinueBookingCubit()),
        BlocProvider(create: (_) => CancelBookingCubit()),
        BlocProvider(create: (_) => ProfileCubit()),
        BlocProvider(create: (_) => LogoutCubit()),
        BlocProvider(create: (_) => DeleteAccountCubit()),
        BlocProvider(create: (_) => FAQsCubit()),
        BlocProvider(create: (_) => IsAreaServiceableCubit()),
        BlocProvider(create: (_) => CaregiverCountCubit()),
        BlocProvider(create: (_) => CouponOffersCubit()),
      ],
      child: ScreenUtilInit(
          useInheritedMediaQuery: true,
          designSize: isTablet(context) ? const Size(834, 1144) : const Size(375, 812),
          minTextAdapt: true,
          builder: (context, child) {
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                FocusScopeNode currentFocus = FocusScope.of(context);
                //currentFocus node check primary focus if doest not then call un focus
                if (!currentFocus.hasPrimaryFocus &&
                    currentFocus.focusedChild != null) {
                  currentFocus.focusedChild!.unfocus();
                }
              },
              child: MaterialApp(
                theme: AppTheme.lightThemeData,
                debugShowCheckedModeBanner: false,
                navigatorObservers: [
                  MyRouteObserver.getInstance(),
                ],
                navigatorKey:
                    serviceLocator<NavigationService>().getGlobalKey(),
                onGenerateRoute: (settings) => AppRoute.generateRoute(settings),
                initialRoute: AppRoute.splash,
              ),
            );
          }),
    );
  }
}
